import { Chip } from "@heroui/chip";
import { NavLink } from "react-router-dom";

const SidebarMenuItem = ({ item, pathname, isSubmenuItem = false, sidebarExpanded }) => {
    const isActive = item.exact ? pathname === item.path : pathname.includes(item.path);

    const baseClasses = isSubmenuItem
        ? "block transition duration-150 truncate ps-8 py-3"
        : `block transition-all duration-300 ease-in-out rounded-xl hover:bg-gray-200 dark:hover:bg-zinc-800/60 ${isActive
            ? "bg-info dark:bg-zinc-800/60 hover:bg-info/80"
            : "dark:text-gray-400 text-gray-600 dark:hover:bg-gray-900"
        } ${sidebarExpanded ? 'w-full p-2' : 'w-12 p-2'} py-3.5`;

    return (
        <li className={baseClasses}>
            <NavLink
                to={item.path}
                className="flex items-center justify-center"
                end={item.exact}
            >
                <div className={`flex items-center ${sidebarExpanded ? 'w-full pl-1' : 'w-6'}`}>
                    {item.badge && sidebarExpanded && (
                        <Chip size="sm" className="mx-1.5 px-0.5 rounded-full" color={item.badge.color}>
                            {item.badge.content}
                        </Chip>
                    )}
                    {item.icon && (
                        <item.icon
                            className={`${isActive ? 'dark:text-glb_blue text-white' : 'text-black dark:text-white'} shrink-0`}
                            size={24}
                        />
                    )}
                    <div className={`overflow-hidden transition-all duration-300
                    ${isActive ? item.exact ? 'text-white' : 'text-glb_blue' : 'text-black dark:text-white'}
                     ${sidebarExpanded ? 'w-auto ml-3' : 'w-0'}`}>
                        <span className="text-sm font-medium whitespace-nowrap">
                            {item.name}
                        </span>
                    </div>
                </div>
            </NavLink>
        </li>
    );
};

export default SidebarMenuItem;
