// src/modules/dashboard/pages/Funds.jsx
import React, { useState } from "react";
import {
  ArrowDown01Icon,
  Calendar03Icon,
  CallEnd03Icon,
  CancelCircleIcon,
  CheckmarkCircle01Icon,
  Clock01Icon,
  CustomerSupportIcon,
  DeliveryReturn01Icon,
  FilterIcon,
  PackageIcon,
  Settings01Icon,
  ShoppingBasket01Icon,
  ShoppingBasket03Icon,
  ShoppingCart01Icon,
  Airplane01Icon,
} from "hugeicons-react";
import { VerticalBarChart } from "@/modules/dashboard/components/BarChartCard.jsx";
import { Button } from "@heroui/button";
import StatisticsCard from "@/modules/statistics/components/StatisticsCard.jsx";
import TotalFees from "@/modules/statistics/components/TotalFees.jsx";
import DashboardLayout from "../../shared/layouts/DashboardLayout";

import DateRangeDropdown from "../../shared/components/DateRangeDropdown.jsx"; // adjust path as needed
import FilterModal from "../components/FilterModal.jsx";             // adjust path as needed
import {
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
  } from "@heroui/dropdown";
  
const statisticCardsData = [
    { title: "Total Leads", percentage: "100", count: 2354, icon: ShoppingBasket01Icon, bgColor: "bg-[#0055A5]" },
    { title: "Real Leads", percentage: "100", count: 1892, icon: ShoppingBasket03Icon, bgColor: "bg-[#B82660]" },
    { title: "New Leads", percentage: "100", count: 1723, icon: ShoppingCart01Icon, bgColor: "bg-[#4C6D87]" },
    { title: "Test", percentage: "100", count: 2145, icon: Settings01Icon, bgColor: "bg-[#495EAE]" },
    { title: "Upsell", percentage: "100", count: 1680, icon: ShoppingCart01Icon, bgColor: "bg-[#505565]" },
    { title: "No Answer", percentage: "100", count: 1934, icon: CallEnd03Icon, bgColor: "bg-[#A52424]" },
    { title: "Cancel", percentage: "100", count: 1450, icon: CancelCircleIcon, bgColor: "bg-[#7B3232]" },
    { title: "Scheduled", percentage: "100", count: 1876, icon: Clock01Icon, bgColor: "bg-[#A8A813]" },
    { title: "Wrong Phone", percentage: "100", count: 1638, icon: CancelCircleIcon, bgColor: "bg-[#6D6D6D]" },
    { title: "Double Order", percentage: "100", count: 2002, icon: ShoppingCart01Icon, bgColor: "bg-[#458F9D]" },
    { title: "Return", percentage: "100", count: 1579, icon: DeliveryReturn01Icon, bgColor: "bg-[#563637]" },
    { title: "In Transit", percentage: "100", count: 1940, icon: Airplane01Icon, bgColor: "bg-[#6B4270]" },
    { title: "Confirmed", percentage: "100", count: 1831, icon: CustomerSupportIcon, bgColor: "bg-[#5B9465]" },
    { title: "Delivered", percentage: "100", count: 2307, icon: CheckmarkCircle01Icon, bgColor: "bg-[#3A9E79]" },
    { title: "Shipped", percentage: "100", count: 2115, icon: PackageIcon, bgColor: "bg-[#2D9A5A]" },
  ];

export default function Funds() {
    const barChartData = [
        { label: "Sun 12", value: 1600, color: "#0258E8" },
        { label: "Mon 13", value: 2550, color: "#ED0006" },
        { label: "Tue 14", value: 2300, color: "#0258E8" },
        { label: "Wed 15", value: 2500, color: "#ED0006" },
        { label: "Thu 16", value: 2700, color: "#0258E8" },
        { label: "Fri 17", value: 2800, color: "#ED0006" },
        { label: "Sat 18", value: 2590, color: "#0258E8" },
      ];

  const [showFilterModal, setShowFilterModal] = useState(false);

  const [dateRange, setDateRange] = useState({
    startDate: new Date("2023-10-28"),
    endDate: new Date("2024-01-01"),
  });

  const [timeRange, setTimeRange] = useState("Monthly");

  return (
    <DashboardLayout
      title="Funds"
      actions={
        <div className="flex flex-row gap-2 flex-1 justify-end">
          
          <DateRangeDropdown
            initialStartDate={dateRange.startDate}
            initialEndDate={dateRange.endDate}
            onRangeChange={({ startDate, endDate }) => {
              // store it in your local state or do anything needed
              setDateRange({ startDate, endDate });
              console.log("User picked:", startDate, "->", endDate);
            }}
          />

<Dropdown>
  <DropdownTrigger>
    <Button 
      variant="bordered" 
      className="rounded-full text-xs p-2 md:text-base md:p-4 border-[#444444] border-1"
    >
      {timeRange}
      <ArrowDown01Icon className="ml-1" />
    </Button>
  </DropdownTrigger>
  <DropdownMenu
    aria-label="Time Range"
    onAction={(key) => setTimeRange(key)}
  >
    <DropdownItem key="Daily">Daily</DropdownItem>
    <DropdownItem key="Monthly">Monthly</DropdownItem>
    <DropdownItem key="Yearly">Yearly</DropdownItem>
  </DropdownMenu>
</Dropdown>

          {/* FILTER button - open the modal */}
          <Button
            variant="solid"
            className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_red text-white"
            onPress={() => setShowFilterModal(true)}
          >
            <FilterIcon size={18} />
            Filter
          </Button>
        </div>
      }
    >
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 justify-start my-4">
        {statisticCardsData.map((item, index) => (
          <StatisticsCard
            key={index}
            icon={item.icon}
            iconBg={item.bgColor}
            percentage={item.percentage}
            sales={item.count}
            title={item.title}
          />
        ))}
      </div>

      <div className="flex flex-col lg:flex-row gap-4">
        <div className="w-full lg:w-2/3">
          <h2 className="text-2xl font-bold my-4">Profits</h2>
          <VerticalBarChart chartData={barChartData} />
        </div>
        <div className="w-full lg:w-1/3">
          <h2 className="text-2xl font-bold my-4">Total Fees</h2>
          <TotalFees />
        </div>
      </div>

      {/* The FilterModal below */}
      <FilterModal
        open={showFilterModal}
        onClose={() => setShowFilterModal(false)}
      />
    </DashboardLayout>
  );
}
