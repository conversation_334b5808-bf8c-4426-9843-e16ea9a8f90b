import { Button } from "@heroui/button";
import CustomModal from "@shared/components/CustomModal.jsx";
import { BubbleChatIcon, GoogleDocIcon, ImageAdd01Icon, InformationCircleIcon, Search01Icon } from "hugeicons-react";
import { useState } from "react";
import UnderlinedInput from "@/modules/settings/components/UnderlinedInput.jsx";
import { Select, SelectItem } from "@heroui/select";
import { Checkbox } from "@heroui/checkbox";
import WYSIWYGEditor from "@/modules/sourcing-request/components/WYSIWYGEditor.jsx";
import { Input } from "@heroui/input";


const CreateNewRequestModal = ({ isOpen, onClose }) => {

    const steps = [
        {
            icon: InformationCircleIcon,
            title: "Enter the Sourcing Information",
        },
        {
            icon: BubbleChatIcon,
            title: "Quick Response form our Sourcing team",
        },
        {
            icon: GoogleDocIcon,
            title: "Accept Quotation",
        }
    ];

    const [activeStep, setActiveStep] = useState(0);

    const [formData, setFormData] = useState({
        productName: '',
        arabicName: '',
        category: '',
        productUrl: '',
        estimatedQuantity: '',
        targetPrice: '',
        destinationCountry: '',
        shippingMethod: 'ocean',
        description: ''
    });

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        // Handle form submission
        console.log('Form submitted:', formData);
    };

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={onClose}
            width="max-w-6xl"
            showHeader={true}
            title="Create New Request"

            headerClassName="px-2 md:px-4 lg:px-8 py-4 border-b"
            bodyClassName="p-2 md:p-4 lg:p-8"
            showFooter={activeStep === steps.length - 1}
            footerContent={
                <div className="flex justify-center gap-4">
                    <Button
                        className="px-8 py-2 rounded-full bg-glb_blue text-white">
                        Accept
                    </Button>
                    <Button
                        className="px-8 py-2 rounded-full border border-gray-300 bg-white dark:bg-transparent"
                        onClick={onClose}
                    >
                        Cancel
                    </Button>
                </div>
            }
        >
            <div className="flex flex-col lg:flex-row gap-8">
                {/* Left Side - Steps */}
                <div className=" pb-4 flex flex-row lg:flex-col gap-2 items-center">
                    {steps.map((step, index) => (
                        <Button
                            key={index}
                            onClick={() => setActiveStep(index)}
                            className={`rounded-2xl border border-black/10 dark:border-white/10 lg:h-[100px] lg:justify-start ${activeStep === index
                                    ? 'w-auto h-16 flex items-center justify-start px-4 bg-glb_blue text-white lg:w-full '
                                    : 'w-16 h-16 flex items-center justify-center bg-black/5 dark:bg-white/5 lg:w-full  '
                                }`}
                        >
                            <div className="text-2xl lg:ml-8 lg:mr-5">{<step.icon />}</div>
                            <div
                                className={` text-left text-xs md:text-sm lg:text-lg font-medium whitespace-normal break-words  ${activeStep === index ? 'block' : 'hidden lg:block'
                                    }`}
                            >
                                {step.title}
                            </div>
                        </Button>
                    ))}

                </div>


                {/* Right Side - Product Details and Quotation */}
                <div className="w-full lg:w-3/5 space-y-6">

                    <div className={`${activeStep === 0 ? 'block' : 'hidden'}  w-full max-w-2xl px-2 lg:px-6`}>
                        <div className="mb-6">
                            <div
                                className="bg-black/5 mx-auto lg:mx-0 dark:bg-white/5 hover:bg-black/10 dark:hover:bg-white/10 w-40 h-40 rounded-2xl flex items-center justify-center p-2 cursor-pointer">
                                <div className="text-center">
                                    <ImageAdd01Icon className="mx-auto my-2" />
                                    <p className="mt-1 text-sm text-gray-500">Upload your Product Picture</p>
                                </div>
                            </div>
                        </div>

                        <form onSubmit={handleSubmit} className="space-y-4 flex flex-col gap-2">
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                <div>

                                    <Input type="text" variant="underlined" color="primary"
                                        onchange={handleChange}
                                        label="Product Name"
                                        name="productName"
                                        defaultValue={formData.productName}
                                        classNames={{
                                            label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],

                                            // input tag inside innerWrapper
                                        }}
                                    />
                                </div>
                                <div>

                                    <Input type="text" variant="underlined" color="primary"
                                        onchange={handleChange}
                                        label="Arabic Name"
                                        name="arabicName"
                                        defaultValue={formData.arabicName}
                                        classNames={{
                                            label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],

                                            // input tag inside innerWrapper
                                        }}
                                    />
                                </div>
                            </div>

                            <div>
                                <Select
                                    variant="underlined"
                                    label="Category"
                                    className="border-b-gray-700">
                                    <SelectItem key="texas">
                                        Electronics
                                    </SelectItem>
                                    <SelectItem key="california">
                                        Clothing
                                    </SelectItem>
                                    <SelectItem key="florida">
                                        Furniture
                                    </SelectItem>
                                </Select>
                            </div>

                            <div>
                                {/* <UnderlinedInput
                                    label="Product URL"
                                    name="productUrl"
                                    onchange={handleChange}
                                    defaultValue={formData.productUrl}
                                /> */}
                                <Input type="text" variant="underlined" color="primary"
                                    onchange={handleChange}
                                    label="Product URL"
                                    name="productUrl"
                                    defaultValue={formData.productUrl}
                                    classNames={{
                                        label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],

                                        // input tag inside innerWrapper
                                    }}
                                />
                            </div>

                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                <div>
                                    {/* <UnderlinedInput
                                        label="Estimated Quantity"
                                        name="estimatedQuantity"
                                        onchange={handleChange}
                                        defaultValue={formData.estimatedQuantity}
                                        placeholder="Estimated Quantity"
                                    /> */}
                                    <Input type="text" variant="underlined" color="primary"
                                        onchange={handleChange}
                                        name="estimatedQuantity"
                                        defaultValue={formData.estimatedQuantity}
                                        classNames={{
                                            label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],

                                            // input tag inside innerWrapper
                                        }}

                                        label="Estimated Quantity" />
                                </div>
                                <div>
                                    {/* <UnderlinedInput
                                        label="Target Price (USD)"
                                        name="targetPrice"
                                        onchange={handleChange}
                                        defaultValue={formData.targetPrice}
                                    /> */}
                                    <Input type="text" variant="underlined" color="primary"
                                        name="targetPrice"
                                        onchange={handleChange}
                                        defaultValue={formData.targetPrice}
                                        classNames={{
                                            label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],

                                            // input tag inside innerWrapper
                                        }}
                                        endContent={
                                            <span className="!text-[#00000050] dark:!text-[#FFFFFF30]">
                                                USD
                                            </span>
                                        }
                                        label="Target Price" />
                                </div>
                            </div>

                            <div>
                                {/* <UnderlinedInput
                                    label="Destination Country"
                                    name="destinationCountry"
                                    onchange={handleChange}
                                    endContent={<Search01Icon size={16} className="my-1 text-gray-400" />}
                                    defaultValue={formData.destinationCountry}
                                /> */}
                                <Input type="text" variant="underlined" color="primary"
                                    label="Destination Country"
                                    name="destinationCountry"
                                    onchange={handleChange}
                                    defaultValue={formData.destinationCountry}
                                    classNames={{
                                        label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],

                                        // input tag inside innerWrapper
                                    }}
                                    endContent={
                                        <Search01Icon size={16} className="my-1 !text-[#00000050] dark:!text-[#FFFFFF30]" />
                                    } />
                            </div>

                            <div className="flex flex-row justify-start items-center gap-10 text-xs lg:text-lg ">
                                <label className=" text-gray-500">Shipping Method</label>

                                <label className="flex items-center ">
                                    <Checkbox value={formData.shippingMethod} classNames={{ wrapper: "after:bg-info " }}>Air
                                        Freight</Checkbox>

                                </label>
                                <label className="flex items-center ">
                                    <Checkbox value={formData.shippingMethod}
                                        classNames={{ wrapper: "after:bg-info" }}>Ocean</Checkbox>
                                </label>
                            </div>

                            <div>
                                <label className="block text-sm text-gray-700 mb-1">Description</label>
                                <WYSIWYGEditor />
                                <p className="text-sm text-gray-500 mt-1">500 Limited Characters</p>
                            </div>

                            <button
                                type="submit"
                                className="mr-auto rounded-full bg-danger text-white py-2 px-8 hover:bg-glb_red transition duration-200"
                            >
                                Send Request
                            </button>
                        </form>
                    </div>

                    <div className={`${activeStep === 1 ? 'block' : 'hidden'}`}>


                        {/* Product Details */}
                        <div className="bg-black/5 dark:bg-white/5 rounded-2xl space-y-4 font-medium mb-8 text-[12px] lg:text-lg">
                            <div className="flex items-center px-2 lg:px-6 py-4 even:bg-gray-50 even:dark:bg-zinc-800">
                                <span className="w-1/2 text-center dark:text-gray-300">Product Details</span>
                                <span className="w-1/2 text-center font-medium">iPhone Cover</span>
                            </div>
                            <div className="flex items-center px-2 lg:px-6 py-4 even:bg-gray-50 even:dark:bg-zinc-800">
                                <span className="w-1/2 text-center dark:text-gray-300">Product Category</span>
                                <span className="w-1/2 text-center font-medium">Phone Accessories</span>
                            </div>
                            <div className="flex items-center px-2 lg:px-6 py-4 even:bg-gray-50 even:dark:bg-zinc-800">
                                <span className="w-1/2 text-center dark:text-gray-300">Quantity</span>
                                <span className="w-1/2 text-center font-medium">120</span>
                            </div>
                        </div>
                        {/* Quotation Details */}
                        <div className="bg-black/5 dark:bg-white/5 rounded-2xl p-6 space-y-4">
                            <h3 className="text-lg font-semibold mb-6">Quotation #0198</h3>

                            <div className="space-y-4 text-[12px] lg:text-lg">
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600 dark:text-gray-300 ">Destination Country</span>
                                    <span className="font-medium">Saudi Arabia</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600 dark:text-gray-300">Shipping Method</span>
                                    <span className="font-medium">Air Freight</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600 dark:text-gray-300">Quantity</span>
                                    <span className="font-medium">129</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600 dark:text-gray-300">Unit Price</span>
                                    <span className="font-medium">$81.02</span>
                                </div>
                                <div
                                    className="flex justify-between items-center bg-black/5 dark:bg-white/5 p-6 rounded-xl">
                                    <span className="font-semibold">Total</span>
                                    <span className="font-semibold">$ 1,297.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className={`${activeStep === 2 ? 'block' : 'hidden'}`}>


                        {/* Quotation Details */}
                        <div className="bg-black/5 dark:bg-white/5 rounded-2xl p-6 space-y-4">
                            <h3 className="text-lg font-semibold mb-6">Quotation #0198</h3>

                            <div className="space-y-4 text-[12px] lg:text-lg">
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600 dark:text-gray-300 ">Destination Country</span>
                                    <span className="font-medium">Saudi Arabia</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600 dark:text-gray-300">Shipping Method</span>
                                    <span className="font-medium">Air Freight</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600 dark:text-gray-300">Quantity</span>
                                    <span className="font-medium">129</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600 dark:text-gray-300">Unit Price</span>
                                    <span className="font-medium">$81.02</span>
                                </div>
                                <div
                                    className="flex justify-between items-center bg-black/5 dark:bg-white/5 p-6 rounded-xl">
                                    <span className="font-semibold">Total</span>
                                    <span className="font-semibold">$ 1,297.00</span>
                                </div>
                            </div>
                        </div>
                        {/* Product Details */}
                        <div className="bg-black/5 dark:bg-white/5 rounded-2xl space-y-4 font-medium mt-8 text-[12px] lg:text-lg">
                            <div className="flex items-center px-2 lg:px-6 py-4 even:bg-gray-50 even:dark:bg-zinc-800">
                                <span className="w-1/2 text-center dark:text-gray-300">Product Details</span>
                                <span className="w-1/2 text-center font-medium">iPhone Cover</span>
                            </div>
                            <div className="flex items-center px-2 lg:px-6 py-4 even:bg-gray-50 even:dark:bg-zinc-800">
                                <span className="w-1/2 text-center dark:text-gray-300">Product Category</span>
                                <span className="w-1/2 text-center font-medium">Phone Accessories</span>
                            </div>
                            <div className="flex items-center px-2 lg:px-6 py-4 even:bg-gray-50 even:dark:bg-zinc-800">
                                <span className="w-1/2 text-center dark:text-gray-300">Quantity</span>
                                <span className="w-1/2 text-center font-medium">120</span>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </CustomModal>
    );
};


export default CreateNewRequestModal;