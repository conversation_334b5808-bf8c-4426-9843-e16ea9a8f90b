const DataTile = ({
    label,
    value,
    labelClassName = 'w-[40%] truncate text-start text-xs lg:text-base',
    valueClassName = 'w-[60%] text-end text-sm lg:text-base'
}) => {
    return (
        <div
            className=" py-4 px-2 lg:px-16 flex justify-between items-center font-medium text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
            <div className={labelClassName}>
                {label}
            </div>
            <div className={valueClassName}>
                {value === 0 ? '0' : (value || '-')}
            </div>
        </div>
    );
};

export default DataTile;