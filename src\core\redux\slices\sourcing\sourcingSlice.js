import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { getToken } from '../../../services/TokenHandler';
import { addSourcingRequestUrl, deleteSourcingRequestUrl, getSourcingRequesDetailstUrl, getSourcingRequestUrl, updateSourcingRequestUrl } from '../URLs';

const initialState = {
    sourcingRequest: [],
    params: {
        page: 1,
        startDate: null,
        endDate: null,
        status: null,
        productReference: null,
        sortBy: null,
    },
    loading: false,
    error: null,
};

export const updateParams = createAsyncThunk(
    "sourcing/updateParams",
    async (newParams, { getState, rejectWithValue }) => {
        try {
            Object.keys(newParams).forEach(key => {
                if (newParams[key] === '') {
                    newParams[key] = null;
                }
            });
            const { params } = getState().sourcing;
            if (!newParams.hasOwnProperty('page')) {
                newParams.page = 1;
            }
            return { ...params, ...newParams };
        } catch (error) {
            return rejectWithValue("Failed to update params");
        }
    }
);



export const resetParams = createAsyncThunk(
    "sourcing/resetParams",
    async (_, { getState, rejectWithValue }) => {
        try {
            const { params } = getState().sourcing;
            const resetParams = Object.keys(params).reduce((acc, key) => {
                if (key !== 'page') {
                    acc[key] = null;
                }
                acc.page = 1;
                return acc;
            }, {});
            return resetParams;
        } catch (error) {
            return rejectWithValue("Failed to reset params");
        }
    }
);

export const getSourcingRequestDetails = createAsyncThunk(
    "sourcing/getRequestDetails",
    async (id, { rejectWithValue }) => {
        try {
            let query = `${getSourcingRequesDetailstUrl}${id}`;
            const response = await axios.get(query, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching sourcing requests' });
            }
            return response.data.result
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);

export const getSourcingRequest = createAsyncThunk(
    "sourcing/getRequest",
    async (_, { getState, rejectWithValue }) => {
        try {
            const { params } = getState().sourcing;
            let query = `${getSourcingRequestUrl}?page=${params.page}`;

            params.startDate && (query += `&startDate=${params.startDate}`);
            params.endDate && (query += `&endDate=${params.endDate}`);
            params.status && (query += `&status=${params.status === 'all' ? '' : params.status}`);
            params.productReference && (query += `&productReference=${params.productReference}`);
            params.sortBy && (query += `&dateType=${params.sortBy}`);

            const response = await axios.get(query, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching sourcing requests' });
            }

            return response.data;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);

export const postSourcingRequest = createAsyncThunk(
    'sourcing/postRequest',
    async (formData, { rejectWithValue }) => {
        try {
            const response = await axios.post(addSourcingRequestUrl, formData, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                    //'Content-Type': 'multipart/form-data',
                },
            });
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const updateSourcingRequest = createAsyncThunk(
    'sourcing/updateRequest',
    async ({ id, updatedData }, { rejectWithValue }) => {
        try {

            const response = await axios.post(`${updateSourcingRequestUrl}${id}`, updatedData, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error updating sourcing request' });
            }

            return response.data.result;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);

export const deleteSourcingRequest = createAsyncThunk(
    'sourcing/deleteRequest',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axios.delete(`${deleteSourcingRequestUrl}${id}`, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error deleting sourcing request' });
            }

            return id; // Return the ID of the deleted request
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);

const sourcingSlice = createSlice({
    name: 'sourcing',
    initialState,
    reducers: {
        setParams: (state, action) => {
            state.params = { ...state.params, ...action.payload };
        },
        resetSourcingState: (state) => {
            state.sourcingRequest = [];
            state.params = { page: 1 };
            state.loading = false;
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(getSourcingRequest.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSourcingRequest.fulfilled, (state, action) => {
                state.loading = false;
                state.sourcingRequest = action.payload;
            })
            .addCase(getSourcingRequest.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(postSourcingRequest.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(postSourcingRequest.fulfilled, (state, action) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(postSourcingRequest.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(updateSourcingRequest.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateSourcingRequest.fulfilled, (state, action) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(updateSourcingRequest.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(deleteSourcingRequest.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteSourcingRequest.fulfilled, (state, action) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(deleteSourcingRequest.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(updateParams.pending, (state) => {
                state.paramsLoading = true;
                state.error = null;
            })
            .addCase(updateParams.fulfilled, (state, action) => {
                state.paramsLoading = false;
                state.params = action.payload;
                state.paramsCount = Object.keys(state.params).filter(key => {
                    const value = state.params[key];
                    return key !== 'page' &&
                        value !== null &&
                        value !== '' &&
                        value !== 0;
                }).length;
            })
            .addCase(updateParams.rejected, (state, action) => {
                state.paramsLoading = false;
                state.error = action.payload;
            })
            .addCase(resetParams.pending, (state) => {
                state.paramsLoading = true;
                state.error = null;
            })
            .addCase(resetParams.fulfilled, (state, action) => {
                state.paramsLoading = false;
                state.params = action.payload;
                state.paramsCount = Object.keys(state.params).filter(key => {
                    const value = state.params[key];
                    return key !== 'page' &&
                        value !== null &&
                        value !== '' &&
                        value !== 0;
                }).length;
            })
            .addCase(resetParams.rejected, (state, action) => {
                state.paramsLoading = false;
                state.error = action.payload;
            });
    },
});

export const { setParams, resetSourcingState } = sourcingSlice.actions;
export default sourcingSlice.reducer;

