import Header from "@/modules/dashboard/components/partials/Navbar.jsx";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowDown01Icon, ArrowLeft01Icon, ArrowRight01Icon } from "hugeicons-react";
import { useEffect, useRef, useState } from "react";
import FilterModal from "@/modules/dashboard/components/FilterModal.jsx";
import { Link } from "react-router-dom";
import SearchModal from "@/modules/dashboard/components/SearchModal.jsx";


export default function DashboardLayout({
    children,
    icon,
    title,
    additionalContent,
    hasSearchInput = true,
    hasReturnLink = null
}) {

    const [showFilterModal, setShowFilterModal] = useState(false);

    const [SmallNotOpen, setSmallNotOpen] = useState(false);
    const dropdownRef = useRef(null);
    const [searchModalOpen, setSearchModalOpen] = useState(false);
    const storedSidebarEpingled = localStorage.getItem("sidebar-epingled");
    const [sidebarEpingled, setSidebarEpingled] = useState(storedSidebarEpingled === null ? false : storedSidebarEpingled === "true");
    const [showSidebar, setShowSidebar] = useState(sidebarEpingled);
    const [showReSidebar, setShowReSidebar] = useState(false);

    useEffect(() => {
        function handleClickOutside(event) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setSmallNotOpen(false);
            }
        }

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    const dropdownVariants = {
        hidden: { opacity: 0, height: 0 },
        visible: {
            opacity: 1,
            height: "auto",
            transition: {
                duration: 0.1,
                when: "beforeChildren",
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { opacity: 0, x: -10 },
        visible: { opacity: 1, x: 0 },
    };

    const HandleSideBarChange = (v) => {

        setShowSidebar(v); // Ensure to update showSidebar here
    }

    const HandlePinglingSideBar = (v) => {

        localStorage.setItem("sidebar-epingled", v);
        setSidebarEpingled(v);
    }
    return (
        <>
            {/* i want to add a hover on 10px in the right of this parent div to show the side bar  */}
            <div className="flex w-screen overflow-hidden min-h-screen">


                <div
                    className={`relative flex flex-col w-full lg:ml-auto min-h-screen ${sidebarEpingled ? ' lg:w-[calc(100%-20rem)] ' : showSidebar ? ' lg:w-[calc(100%-20rem)]' : 'lg:w-[calc(100%-3.5rem)]'}`}>
                    {/*  Site header */}
                    <Header epingled={sidebarEpingled} setEpingled={HandlePinglingSideBar}
                        showSidebar={showReSidebar} setShowSidebar={setShowReSidebar} />

                    <div className="relative  h-12 w-full p-3 md:p-4 mx-auto text-center hidden lg:hidden">
                        <div ref={dropdownRef} onClick={() => setSmallNotOpen(!SmallNotOpen)}
                            className="z-30 cursor-pointer absolute top-3 w-[90%] sm:w-[80%] max-w-80 left-1/2 transform -translate-x-1/2 rounded-xl p-2 font-semibold text-red-500 dark:text-white bg-red-200 dark:bg-[#2F1214]">
                            <div className=" flex justify-center items-center gap-2 ">
                                <h4 className="text-sm font-semibold ">Important Notifications in the ERP</h4>
                                {SmallNotOpen ? <ArrowDown01Icon className="font-thin" /> :
                                    <ArrowRight01Icon className="font-thin" />}
                            </div>

                            <AnimatePresence>
                                {SmallNotOpen && (
                                    <motion.div initial="hidden"
                                        animate="visible"
                                        exit="hidden"
                                        index={22}
                                        variants={dropdownVariants} className="w-full flex flex-col gap-2 mt-2">
                                        {[{ data: 154, label: 'No Answers Late' }, {
                                            data: 21415,
                                            label: 'Schedule Late - Follow Up'
                                        }
                                            , { data: 21415, label: 'Schedule Late - Follow Up' }, {
                                            data: 21415,
                                            label: 'Schedule Late - Follow Up'
                                        }
                                        ].map((i, ix) => (
                                            <motion.div variants={itemVariants}
                                                custom={ix} key={ix}
                                                className=" flex justify-start items-center gap-2 ">
                                                <h4 className="text-sm"><b>{i.data}</b></h4>
                                                <h4 className="text-sm font-thin">{i.label}</h4>

                                            </motion.div>))}</motion.div>
                                )}
                            </AnimatePresence>
                        </div>
                    </div>
                    <div className="flex-grow">
                        {children}
                    </div>


                    <footer className=" mx-auto my-12 text-center">
                        <span className="text-sm text-gray-600">
                            Copyright © {new Date().getFullYear()}. Power Group World, All rights reserved.
                        </span>
                    </footer>
                </div>
                sidebar for mobiles screens
                {/* <ResideBar showSidebar={showReSidebar} setShowSidebar={setShowReSidebar}/> */}
                {/* sidebar for bigger screens */}
                {/* <AdminSidebar showSidebar={sidebarEpingled ? true : showSidebar} setShowSidebar={HandleSideBarChange}/> */}

            </div>
            <SearchModal id="search-modal" searchId="search" modalOpen={searchModalOpen}
                setModalOpen={setSearchModalOpen} />
            <FilterModal modalOpen={showFilterModal} setModalOpen={setShowFilterModal} id={2} />
        </>
    )
}