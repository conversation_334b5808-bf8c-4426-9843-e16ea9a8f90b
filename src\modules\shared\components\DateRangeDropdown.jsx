// src/modules/shared/components/DateRangeDropdown.jsx
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@heroui/button";
import { Popover, PopoverTrigger, PopoverContent } from "@heroui/popover";
import { DateRange } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import "../css/dateRangeDark.css"; // <-- NEW: your custom overrides for dark mode or styling
import { Calendar03Icon, ArrowDown01Icon } from "hugeicons-react";
import moment from "moment";

export default function DateRangeDropdown({
  // Default "from" and "to" (optional)
  initialStartDate = new Date(),
  initialEndDate = new Date(),
  // Callback to parent once user picks the full range
  onRangeChange = () => {},
}) {
  const [selectionRange, setSelectionRange] = useState([
    {
      startDate: initialStartDate,
      endDate: initialEndDate,
      key: "selection",
    },
  ]);

  // Tracks if the popover is open
  const [isOpen, setIsOpen] = useState(false);

  // Tracks how many clicks so far; 0 = none, 1 = just picked "from", 2 = finalize
  const [clickCount, setClickCount] = useState(0);

  // Format the button label, e.g. "Oct 19 - Oct 20"
  function getButtonLabel() {
    const { startDate, endDate } = selectionRange[0];
    if (startDate && endDate) {
      const fromStr = moment(startDate).format("MMM D");
      const toStr = moment(endDate).format("MMM D");
      return `${fromStr} - ${toStr}`;
    }
    return "Select Range";
  }

  // Called whenever user clicks a date in react-date-range
  function handleSelect(ranges) {
    // Update our local range
    setSelectionRange([ranges.selection]);

    // Increment click count
    setClickCount((prev) => {
      const newCount = prev + 1;
      // If it's the second click -> close & submit
      if (newCount === 2) {
        setIsOpen(false);
        const { startDate, endDate } = ranges.selection;
        onRangeChange({ startDate, endDate });
        return 0; // reset for next time
      }
      return newCount;
    });
  }

  return (
    <Popover isOpen={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger>
        <Button
          variant="bordered"
          className="text-xs p-2 md:text-base md:p-4 rounded-full border-[#444444] border-1"
        >
          <Calendar03Icon size={18} />
          {getButtonLabel()}
          <ArrowDown01Icon size={18} />
        </Button>
      </PopoverTrigger>

      <PopoverContent className="bg-white dark:bg-[#1f1f1f] shadow-lg rounded-lg border border-gray-200 dark:border-gray-700">
  <DateRange
    onChange={handleSelect}
    ranges={selectionRange}
    showDateDisplay={false}
    moveRangeOnFirstSelection={false}
    // rangeColors={["#0072F5"]}
  />
</PopoverContent>

    </Popover>
  );
}
