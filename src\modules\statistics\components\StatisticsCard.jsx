const StatisticsCard = ({
    icon: Icon,
    iconBg,
    percentage,
    sales,
    title,
    isExpanded
}) => {
    return (
        <div className={`w-full ${isExpanded ? '' : 'col-span-1 h-36'}`}>
            <div
                className="group flex flex-col justify-between border border-black/10 dark:border-white/10 bg-white dark:bg-transparent hover:bg-[#0057FF]/20 dark:hover:bg-white/5 px-6 py-4 rounded-2xl shadow-sm m-2 gap-4">
                {/* Icon and percentage change */}
                <div className="flex flex-row justify-start items-center gap-2">
                    <span
                        className={`w-10 h-10 flex flex-row items-center justify-center rounded-full text-white ${iconBg}`}> <Icon /></span>
                    <p className="text-sm dark:text-gray-200 inline-block">{title}</p>
                </div>

                <div className="flex flex-row justify-between items-center">
                    <h4 className="text-xl font-medium">{percentage}%</h4>
                    <div
                        className="dark:bg-white/5 bg-black/5 px-2.5 py-0.5 rounded-full">
                        <span className="font-bold">{sales}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StatisticsCard;
