import { Tab, Tabs } from "@heroui/tabs";
import { Chip } from "@heroui/chip";
import { useEffect, useRef, useState } from "react";
import { Spinner } from "@heroui/react";
import { formatNumber } from "../../../core/utils/functions";

const CustomTabs = ({
    tabLabels = {},
    tabCounts = null,
    selectedTab: externalSelectedTab,
    onTabChange,
    initialTab = "all",
    params = null
}) => {
    const tabRefs = useRef({});
    const tabsContainerRef = useRef(null);
    const [counts, setCounts] = useState({});
    const [loadingStatus, setLoadingStatus] = useState({});
    // Internal state for selected tab - independent from filter params
    const [internalSelectedTab, setInternalSelectedTab] = useState(externalSelectedTab || initialTab);

    // Load counts only once when component mounts and tabLabels are available
    const [hasRunEffect, setHasRunEffect] = useState(false);

    useEffect(() => {
        setInternalSelectedTab(externalSelectedTab)
    }, [externalSelectedTab])





    useEffect(() => {
        // if (counts && Object.keys(counts).length > 0 && !params) {
        //     return;
        // }
        // Only run if tabCounts is a function, tabLabels has keys, and the effect hasn't run yet
        if (typeof tabCounts === 'function' && Object.keys(tabLabels).length > 0) {
            // Fetch counts for all tabs at once
            const fetchAllCounts = async () => {
                const tabKeys = Object.keys(tabLabels);

                // Set all tabs to loading state
                const initialLoadingState = tabKeys.reduce((acc, key) => {
                    acc[key] = true;
                    return acc;
                }, {});
                setLoadingStatus(initialLoadingState);

                // Fetch counts for all tabs
                for (const tabKey of tabKeys) {
                    try {
                        const count = await tabCounts(tabKey);
                        setCounts(prev => ({ ...prev, [tabKey]: count }));
                    } catch (error) {
                        console.error(`Error fetching count for tab ${tabKey}:`, error);
                        setCounts(prev => ({ ...prev, [tabKey]: 0 }));
                    } finally {
                        setLoadingStatus(prev => ({ ...prev, [tabKey]: false }));
                    }
                }


            };

            fetchAllCounts();
        }
    }, [tabCounts, tabLabels, params]); // Include dependencies but control execution with hasRunEffect flag

    // Update internal selected tab when external prop changes (for initial render)
    useEffect(() => {
        if (externalSelectedTab && externalSelectedTab !== internalSelectedTab) {
            setInternalSelectedTab(externalSelectedTab);
        }
    }, []);

    const handleTabChange = (key) => {
        // Update internal state
        setInternalSelectedTab(key);

        // Call parent callback with the selected tab
        onTabChange(key);

        // Scroll the tab into view
        setTimeout(() => {
            if (tabRefs.current[key]) {
                tabRefs.current[key].scrollIntoView({
                    behavior: "smooth",
                    block: "nearest",
                    inline: "center"
                });
            }
        }, 0);
    };


    return (
        <div ref={tabsContainerRef} className="hide-scrollbar overflow-x-auto">
            <Tabs
                defaultSelectedKey={initialTab}
                aria-label="Status Options"
                color="primary"
                variant="underlined"
                classNames={{
                    tabList: "gap-6 w-full relative rounded-none p-0 border-b bg-transparent border-b-transparent overflow-x-auto",
                    cursor: "w-full bg-info",
                    tab: "max-w-fit px-0 h-12 text-red-500",
                    tabContent: "group-data-[selected=true]:text-info text-gray-600"
                }}
                selectedKey={internalSelectedTab}
                onSelectionChange={handleTabChange}
            >
                {Object.entries(tabLabels).map(([tabKey, tabLabel]) => (
                    <Tab
                        ref={(el) => tabRefs.current[tabKey] = el}
                        className='px-2'
                        key={tabKey}
                        title={
                            <div className="flex items-center space-x-2">
                                <strong className={`${internalSelectedTab === tabKey ? 'text-black dark:text-white' : 'text-black/50 dark:text-white/50'}`}>
                                    {tabLabel}
                                </strong>
                                {tabCounts ? (
                                    loadingStatus[tabKey] ? (
                                        <Spinner size="sm" color="default" />
                                    ) : (
                                        <Chip
                                            size="sm"
                                            className={`${internalSelectedTab === tabKey ? 'bg-glb_red text-white' : 'bg-black dark:bg-white bg-opacity-25 dark:bg-opacity-25 dark:text-white text-black text-opacity-50 dark:text-opacity-50'}`}
                                        >

                                            {formatNumber(counts[tabKey] ?? 0)}
                                        </Chip>
                                    )
                                ) : null}
                            </div>
                        }
                    />
                ))}
            </Tabs>
        </div>
    );
};

export default CustomTabs;