import React, { useEffect, useRef, useState } from "react";
import {
  ArrowLeft01Icon,
  ArrowRight01Icon,
  ArrowUpDownIcon,
} from "hugeicons-react";
import { motion } from "framer-motion";
import { useDispatch } from "react-redux";
import { Pagination, Spinner } from "@heroui/react";
import { logoutUser } from "../../../core/redux/slices/authSlice";
import { usePermissions } from "../../../core/providers/PermissionContext";

const ICON_SIZE = 12;

const CustomTable = ({
  clickableRow = false,
  clickableRowAction = null,
  coloredHeader = null,
  columns,
  data,
  renderCell,
  setSelectedRows,
  selectedRows = [],
  enablePagination = true,
  loading = false,
  error = null,
  rowClassNames = {
    even: "bg-white dark:bg-transparent h-12",
    odd: "bg-[#00000010] dark:bg-[#ffffff05] h-12",
  },
  emptyMessage = "No records available.",
  rowDetails = null, //shipping cost page
  expandedRow = null, //shipping cost page
  currentPage = 1,
  setCurrentPage = null,
  paginate = null,
  maxHeight = true,
}) => {

  const tableRef = useRef(null);
  const lastSelectedIndex = useRef(null);
  const { hasPermission } = usePermissions();
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const dispatch = useDispatch();

  const [isDown, setIsDown] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  const handleCheckboxChange = (keys, isRange) => {
    if (isRange) {
      // Add all keys in the range
      setSelectedRows((prevSelected) => {
        const newSelection = [...prevSelected];
        keys.forEach((key) => {
          if (!newSelection.includes(key)) {
            newSelection.push(key);
          }
        });
        return newSelection;
      });
    } else if (Array.isArray(keys)) {
      // Select all or unselect all
      setSelectedRows(keys);
    } else {
      // Toggle single selection
      setSelectedRows((prevSelected) =>
        prevSelected.includes(keys)
          ? prevSelected.filter((key) => key !== keys)
          : [...prevSelected, keys]
      );
    }
  };

  // Sorting function
  const sortedData = React.useMemo(() => {
    let processedData = data ? [...data] : [];

    // Apply sorting if a sort key is provided
    if (sortConfig.key) {
      processedData = processedData.sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];

        if (aValue < bValue) {
          return sortConfig.direction === "asc" ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === "asc" ? 1 : -1;
        }
        return 0;
      });
    }

    // // Apply pagination if enabled
    // if (enablePagination) {
    //     const startIndex = (currentPage - 1) * rowsPerPage;
    //     const endIndex = currentPage * rowsPerPage;
    //     processedData = processedData.slice(startIndex, endIndex);
    // }

    return processedData;
  }, [data, sortConfig, currentPage, enablePagination]);

  // Handle click on a sortable column header
  const handleSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  // Determine if any rows are selected
  const isAnySelected = selectedRows.length > 0;
  // make variable chacking if all items are selected
  const isAllSelected = data && selectedRows.length === data.length;

  // Handle select all/unselect all
  const handleSelectAll = () => {
    if (isAnySelected) {
      // Unselect all
      handleCheckboxChange([], false);
    } else {

      const allKeys = data.map((row) => row.id);
      handleCheckboxChange(allKeys, true);
    }
    lastSelectedIndex.current = null; // Reset last selected index
  };

  // if (loading) {
  //     return <div className='m-2 flex justify-start items-center gap-3'><Spinner /> <span>Loading...</span></div>;
  // }
  // if (error) {
  //     return <div className='m-2 flex justify-start items-center gap-3 text-red-500'><span>Error: {error}</span></div>;
  // }

  // if (data && data.length === 0) {
  //     console.log(data);

  //     return <div>{emptyMessage}</div>;
  // }

  const handleSelection = (itemKey, index, event) => {
    if (event.shiftKey && lastSelectedIndex.current !== null) {
      const start = Math.min(lastSelectedIndex.current, index);
      const end = Math.max(lastSelectedIndex.current, index);
      const rangeKeys = sortedData.slice(start, end + 1).map((row) => row.id);

      handleCheckboxChange(rangeKeys, true);
    } else {
      handleCheckboxChange(itemKey);
      lastSelectedIndex.current = index;
    }
  };

  const handleMouseDown = (e) => {
    tableRef.current.style.cursor = "grabbing";

    setIsDown(true);
    setStartX(e.pageX - tableRef.current.offsetLeft);
    setScrollLeft(tableRef.current.scrollLeft);
  };

  const handleMouseLeave = () => {
    setIsDown(false);
    tableRef.current.style.cursor = "default";
  };

  const handleMouseUp = () => {
    setIsDown(false);
    tableRef.current.style.cursor = "default";
  };

  const handleMouseMove = (e) => {
    if (!isDown) return;
    e.preventDefault();
    const x = e.pageX - tableRef.current.offsetLeft;
    const walk = (x - startX) * 2; // scroll-fast multiplier
    tableRef.current.scrollLeft = scrollLeft - walk;
  };

  return (
    <div className="w-full">
      {/* Wrapper div for table and pagination to control the fixed height */}
      <div
        className={`${maxHeight && "min-h-[calc(3rem_*_12)]"
          } flex flex-col justify-between  w-full`}>
        {/* Add overflow-x-auto to enable horizontal scrolling */}
        <div
          ref={tableRef}
          className={` ${maxHeight && "min-h-[650px]"
            } min-h-[300px] w-full overflow-x-auto flex-grow custom-scrollbar`}>
          <table className="box-border border-collapse w-full min-w-[600px]">
            <thead>
              <tr className="border-b border-gray-300 h-11 dark:border-gray-600">
                {columns
                  .filter(
                    (c) =>
                      !c.permission ||
                      (hasPermission(c.permission))
                  )
                  .map((column, index) => (
                    <th
                      key={column.key}
                      onClick={() => column.sortable && handleSort(column.key)}
                      style={{
                        backgroundColor: coloredHeader
                          ? coloredHeader[index % coloredHeader.length]
                          : "transparent",
                      }}
                      className={`${column.label === "#"
                        ? "w-[3%]"
                        : column.w
                          ? column.w
                          : ""
                        } ${column.sortable ? "cursor-pointer" : "cursor-default"
                        } text-center py-2 px-3 text-[#00000060] dark:text-[#ffffff60] text-base font-medium whitespace-nowrap`}>
                      {column.label === "#" ? (
                        <motion.div
                          initial={{ scale: 1 }}
                          whileTap={{ scale: 0.9 }}
                          transition={{ duration: 0.1 }}
                          className="h-full w-full cursor-pointer py-2"
                          onClick={handleSelectAll}>
                          <div className="w-5 h-5 mx-auto rounded-md border border-[#00000050] dark:border-[#ffffff50] flex justify-center items-center">
                            {isAnySelected && !isAllSelected && (
                              <motion.div
                                initial={{ scale: 0 }}
                                transition={{ type: "spring", stiffness: 100 }}
                                animate={{ scale: 1 }}
                                className="w-3 h-1 my-auto rounded-sm bg-glb_blue"
                              />
                            )}
                            {isAllSelected && (
                              <motion.div
                                initial={{ scale: 0 }}
                                transition={{ type: "spring", stiffness: 100 }}
                                animate={{ scale: 1 }}
                                className="w-3 h-3 my-auto rounded-sm bg-glb_blue"
                              />
                            )}
                          </div>
                        </motion.div>
                      ) : column.sortable && column.sortable === true ? (
                        <div className="flex justify-center items-center">
                          {column.label}
                          <ArrowUpDownIcon
                            size={15}
                            className="ml-1 cursor-pointer text-gray-400 hover:text-blue-500"
                          />
                        </div>
                      ) : (
                        column.label
                      )}
                    </th>
                  ))}
              </tr>
            </thead>
            <tbody>
              {!loading ? (
                data && data.length === 0 ? (
                  <tr>
                    <td
                      colSpan={columns.length}
                      className="text-center py-8 text-gray-500">
                      {emptyMessage}
                    </td>
                  </tr>
                ) : (
                  data &&
                  data.length > 0 &&
                  data.map((item, index) => {
                    const isSelected = selectedRows.includes(item.id);

                    const rowClass = isSelected
                      ? "bg-[#0587FF35] dark:bg-[#0587FF30] h-12"
                      : index % 2 === 0
                        ? rowClassNames.even
                        : rowClassNames.odd;

                    return (
                      <React.Fragment key={item.id}>
                        <tr
                          className="group"
                        >
                          {columns
                            .filter(
                              (c) =>
                                !c.permission || hasPermission(c.permission)
                            )
                            .map((column, indx) => {
                              // Determine additional class names based on the column index
                              const borderRadiusClass =
                                indx === 0
                                  ? "rounded-l-lg"
                                  : indx === columns.length - 1
                                    ? "rounded-r-lg"
                                    : "";

                              return (
                                <td
                                  onDoubleClick={() => {
                                    if (clickableRow && indx !== columns.length - 1 && column.key !== "checkbox") {
                                      clickableRowAction(item);
                                    }
                                  }}
                                  key={indx}
                                  className={`${columns[indx].w ?? ""} ${rowClass} ${borderRadiusClass} ${clickableRow
                                    ? " cursor-pointer group-hover:bg-[#0587FF15] group-hover:dark:bg-[#0587FF15]"
                                    : ""
                                    } px-3 py-2 text-center dark:text-gray-300 text-sm`}>
                                  {column.key === "checkbox" ? (
                                    <motion.div
                                      initial={{
                                        scale: 1,
                                      }}
                                      whileTap={{
                                        scale: 0.9,
                                      }}
                                      transition={{
                                        duration: 0.1,
                                      }}
                                      className="h-full w-full cursor-pointer py-2"
                                      onClick={(e) =>
                                        handleSelection(item.id, index, e)
                                      } // Pass event to handleSelection
                                    >
                                      <div className="w-5 h-5 mx-auto rounded-md border border-[#00000050] dark:border-[#ffffff50] flex justify-center items-center">
                                        {isSelected && (
                                          <motion.div
                                            initial={{
                                              scale: 0,
                                            }}
                                            transition={{
                                              type: "spring",
                                              stiffness: 100,
                                            }}
                                            animate={{
                                              scale: 1,
                                            }}
                                            className="w-3 h-3 rounded-sm bg-glb_blue"
                                          />
                                        )}
                                      </div>
                                    </motion.div>
                                  ) : (
                                    <div className={`${column.nowrap ? "whitespace-nowrap" : "break-words"}`}>
                                      {renderCell(item, column.key, index)}  {/* Pass rowIndex here */}
                                    </div>
                                  )}
                                </td>
                              );
                            })}
                        </tr>
                        {rowDetails &&
                          expandedRow &&
                          expandedRow === item.key &&
                          rowDetails(item)}
                      </React.Fragment>
                    );
                  })
                )
              ) : (
                Array.from({ length: 10 }).map((_, rowIndex) => (
                  <motion.tr
                    key={rowIndex}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: rowIndex * 0.1 }}
                    className={` h-14 ${rowIndex % 2 === 0
                      ? rowClassNames.even
                      : rowClassNames.odd
                      }`}>
                    {columns.map((_, colIndex) => (
                      <td
                        key={colIndex}
                        className="px-3 py-2 text-center dark:text-gray-300 text-sm">
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: [0, 1, 0] }} // fade in and out
                          transition={{
                            duration: 1,
                            repeat: Infinity,
                            repeatType: "loop",
                            repeatDelay: 0.5,
                            delay: colIndex * 0.1,
                          }}
                          className="h-4 bg-gray-300 dark:bg-gray-700 rounded"
                        />
                      </td>
                    ))}
                  </motion.tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        {enablePagination && paginate?.totalPages > 1 && (
          <div className="px-2 pagination-container flex justify-center items-center my-4 space-x-2">
            <Pagination
              isCompact
              showControls
              onch
              color="primary"
              page={paginate?.currentPage}
              total={paginate?.totalPages}
              onChange={setCurrentPage}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomTable;
