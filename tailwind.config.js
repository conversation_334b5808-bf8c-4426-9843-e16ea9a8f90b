import { heroui } from "@heroui/theme"

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,jsx}",
    "./node_modules/@heroui/theme/dist/components/(accordion|avatar|button|card|checkbox|spacer|chip|code|date-picker|dropdown|input|modal|navbar|pagination|progress|select|spacer|toggle|table|tabs|user|divider|ripple|spinner|calendar|date-input|popover|menu|listbox|scroll-shadow).js"
  ],
  theme: {
    extend: {
      backgroundImage: {
        'dark-gradient': 'linear-gradient(to bottom right, #141316, #000000)',
        'dark-gradient-header': 'linear-gradient(to bottom right, #141316, #141316)',
      },
      colors: {

        // brand colors
        primary: '#0258E8',

        // accent colors
        info: '#0258E8',
        success: '#026712',
        danger: '#ED0006',
        warning: '#F59523',

        // background
        normal: '#F2F2F9',
        ghost: '#E9E9EE',
        ghosted: '#FFFFFF40',
        selected: '#CDE7FF',
        greyish: '#f9f9fb',

        dark_opacity: '#FFFFFF10',
        light_opacity: '#00000010',

        dark_selected: '#0258E8',
        dark_selected_hover: '#0258E815',

        glb_blue: '#0258E8',
        glb_blue_opacity: '#0258E820',
        glb_red: '#ED0006',
        glb_red_opacity: '#ED000630',
        glb_green: '#27B83E',

        base_dark: '#141618',
        base_card: '#0C0B0C',
        base_light: '#ffffff'

      }
    },
  },
  darkMode: "class",
  plugins: [
    heroui(),
  ],
}
