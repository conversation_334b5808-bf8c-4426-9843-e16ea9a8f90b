import { useEffect, useRef } from "react";
import Transition from "@/core/utils/Transition.jsx";
import { Button } from "@heroui/button";
import { Cancel01Icon, RepeatIcon } from "hugeicons-react";
import { useThemeProvider } from "../../../core/providers/ThemeContext";

const CustomModal = ({
  id,
  isOpen,
  onClose,
  title,
  children,
  showHeader = true,
  showFooter = true,
  footerContent,
  width = "max-w-2xl",
  height = "h-[80%]",
  headerClassName = "",
  headerAdditionalItem = null,
  bodyClassName = "",
  footerClassName = "",
  modalClassName = "",
  closeButton = true,
  hideBackdrop = false,
  showBlur = false,
  onBlurContent = null,
  closeOnClickOutside = true,
  customBackdrop,
}) => {
  const modalContent = useRef(null);
  const { currentTheme } = useThemeProvider();
  // Handle ESC key press
  useEffect(() => {
    const handleEsc = (event) => {
      if (event.key === "Escape" && isOpen) {
        onClose();
      }
    };
    window.addEventListener("keydown", handleEsc);
    return () => window.removeEventListener("keydown", handleEsc);
  }, [isOpen, onClose]);



  useEffect(() => {
    // Only add the event listener if closeOnClickOutside is true
    if (!closeOnClickOutside) return;

    const handleClickOutside = (event) => {
      // Check if the click target is part of a date picker or dropdown
      // This prevents the modal from closing when interacting with date pickers or dropdowns
      const isDatePickerElement = event.target.closest('[role="dialog"]') ||
        event.target.closest('.nextui-date-range-picker') ||
        event.target.closest('.nextui-calendar') ||
        event.target.closest('.nextui-dropdown');

      if (modalContent.current &&
        !modalContent.current.contains(event.target) &&
        !isDatePickerElement) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [closeOnClickOutside, onClose]);

  const defaultBackdrop = (
    <Transition
      className="fixed inset-0 bg-[#DCDDE4]/50 dark:bg-[#08090B]/70 z-50 transition-opacity"
      show={isOpen}
      enter="transition ease-out duration-200"
      enterStart="opacity-0"
      enterEnd="opacity-100"
      leave="transition ease-out duration-100"
      leaveStart="opacity-100"
      leaveEnd="opacity-0"
      aria-hidden="true"
      unmountOnExit={true}
    />
  );



  return (
    <>
      {/* Modal backdrop */}
      {!hideBackdrop && (customBackdrop || defaultBackdrop)}

      {/* Modal dialog */}
      <Transition
        id={id}
        className={`
          fixed inset-0 z-[60]
          overflow-hidden
          flex items-center justify-center
          px-4 sm:px-6
        `}
        unmountOnExit={true}
        role="dialog"
        aria-modal="true"
        show={isOpen}
        enter="transition ease-in-out duration-200"
        enterStart="opacity-0 translate-y-4"
        enterEnd="opacity-100 translate-y-0"
        leave="transition ease-in-out duration-200"
        leaveStart="opacity-100 translate-y-0"
        leaveEnd="opacity-0 translate-y-4"

      >
        <div
          ref={modalContent}
          className={`
            bg-white dark:bg-base_card
            border dark:border-[#ffffff10]
            border-[#00000010]
            overflow-y-auto shadow-lg
            w-full ${width} ${height}
            relative
            rounded-2xl shadow-lg
            ${modalClassName}
            flex flex-col
          `}
        >
          {showBlur &&
            (
              <div
                className="absolute flex justify-center items-center z-20 inset-0 bg-[#00000010] dark:bg-[#ffffff10] backdrop-blur-sm rounded-2xl"
              >
                {onBlurContent && onBlurContent}

              </div>
            )}
          {showHeader && (
            <div
              className={`
                flex flex-row justify-between items-center w-full
                border-b border-[#00000010] dark:border-[#ffffff10]
                px-2 lg:px-6 py-4 flex-wrap
                ${headerClassName}
              `}
            >
              <h3 className="text-lg font-bold">{title}</h3>

              <div className="flex justify-start items-center gap-2 ml-auto">
                {headerAdditionalItem}
                {closeButton && (

                  <Button
                    size="sm"
                    isIconOnly
                    className="rounded-full bg-transparent"
                    onClick={onClose}
                  >
                    <Cancel01Icon size={18} />
                  </Button>
                )}
              </div>

            </div>
          )}

          <div
            className={`px-2 lg:px-6 py-4 overflow-y-auto ${bodyClassName} flex-grow min-h-0 relative`}
          >
            {children}
          </div>

          {showFooter && (
            <div
              className={`
                px-2 lg:px-6 py-4
                border-t border-t-transparent dark:border-t-[#ffffff10]
                ${footerClassName}
              `}
            >
              {footerContent || (
                <div className="flex flex-row justify-center items-center gap-4">
                  <Button
                    className="rounded-full bg-gray-900 text-white px-4 py-2"
                    onClick={onClose}
                  >
                    <Cancel01Icon size={16} /> Close
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </Transition>
    </>
  );
};

export default CustomModal;