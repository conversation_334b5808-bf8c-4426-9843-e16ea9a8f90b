import {
  Add01Icon,
  ArrowUpDownIcon,
  Delete01Icon,
  Home01Icon,
  InboxUploadIcon,
  PencilEdit01Icon,
  ViewIcon,
} from "hugeicons-react";
import { Chip } from "@heroui/chip";
import { Button } from "@heroui/button";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useAsyncList } from "@react-stately/data";
import CustomTabs from "@shared/components/CustomTabs.jsx";
import CustomTable from "@shared/components/CustomTable.jsx";
import CustomPagination from "@shared/components/CustomPagination.jsx";
import CreateNewRequestModal from "@/modules/sourcing-request/components/CreateNewRequestModal.jsx";
import ViewProductRequestModal from "@/modules/sourcing-request/components/ViewProductRequestModal.jsx";
import DashboardLayout from "../../shared/layouts/DashboardLayout";
import CODTable from "../../shared/components/CODTable";
import CreateNewSourcingRequest from "../components/CreateNewSourcingRequest";
import { useDispatch, useSelector } from "react-redux";
import {
  getSourcingRequest,
  getSourcingRequestDetails,
  updateParams,
} from "../../../core/redux/slices/sourcing/sourcingSlice";
import moment from "moment";
import { Popover, PopoverContent, PopoverTrigger, Tooltip } from "@heroui/react";
import { statusColorMap, statusIconsMap } from "../../../core/utils/functions";

const columns = [
  { key: "createdAt", label: "Request Date", w: "w-[160px]" },
  { key: "requestCode", label: "Request Code", w: "w-[130px]" },
  { key: "products", label: "Products", w: "!w-[400px] !max-w-[400px]" }, // Wider for ellipsis
  { key: "totalQuantity", label: "Total Quantity", w: "w-[120px]" },
  { key: "status", label: "Status", w: "w-[120px]" },
  { key: "actions", label: "Actions", w: "w-[80px]" },
];

export default function SourcingRequests() {
  const dispatch = useDispatch();

  // Get products from the Redux state
  const { sourcingRequest, params, loading, error } = useSelector(
    (state) => state.sourcing
  );

  const [isCreateNewRequestModalOpen, setIsCreateNewRequestModalOpen] =
    useState(false);
  const [isViewProductRequestModalOpen, setIsViewProductRequestModalOpen] =
    useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedItemID, setSelectedItemID] = useState(null)
  const [selectedTab, setSelectedTab] = useState("active");
  const [loadingRowId, setLoadingRowId] = useState(null);
  const [loadingRowEditId, setLoadingRowEditId] = useState(null);

  const handleGetDetails = async (id, forWhat) => {

    forWhat === 'edit' ? setLoadingRowEditId(id) : setLoadingRowId(id);
    const result = await dispatch(getSourcingRequestDetails(id))
    if (getSourcingRequestDetails.fulfilled.match(result)) {
      setSelectedItemID(result.payload);
      if (forWhat === 'edit') {
        setIsCreateNewRequestModalOpen(true)
      } else {
        setIsViewProductRequestModalOpen(true)
      }
      setLoadingRowId(null)
      setLoadingRowEditId(null)
    }

  }
  useEffect(() => {
    dispatch(getSourcingRequest());
  }, [dispatch, params]);

  useEffect(() => {
    dispatch(updateParams({ ...{ page: currentPage } }));
  }, [dispatch, currentPage]);

  useEffect(() => {
    console.log(selectedItemID);

  }, [selectedItemID])


  const renderCell = useCallback((item, columnKey) => {
    const cellValue = item[columnKey];

    switch (columnKey) {
      case "createdAt":
        return <span>{moment(item.createdAt).format("DD/MM/YYYY HH:mm")}</span>;
      case "status":
        return (
          <Chip
            className={`capitalize py-1 px-2 h-fit text-white ${statusColorMap[item.status.toLowerCase().replace(" ", "")]
              }`}
            variant="solid">
            <div className="flex flex-row py-2 justify-evenly gap-1 items-center text-black dark:text-white">
              <span className="inline-block">
                {statusIconsMap[item.status.toLowerCase().replace(" ", "")] &&
                  React.createElement(
                    statusIconsMap[item.status.toLowerCase().replace(" ", "")],
                    {
                      className: "mr-2 ml-1",
                      size: 20,
                    }
                  )}
              </span>
              <span className="inline-block">{item.status}</span>
            </div>
          </Chip>
        );
      case "products":
        return (
          <Tooltip
            color="primary"
            content={
              <div className="px-1 py-2 max-w-[300px]">
                <div className="text-small font-bold">Products :</div>
                <div dangerouslySetInnerHTML={{ __html: cellValue }} />
              </div>
            }>
            <div className="truncate cursor-help overflow-hidden text-ellipsis w-full">
              {cellValue && cellValue.replace(/<br\s*\/?>/g, " | ")}
            </div>
          </Tooltip>
        );
      case "actions":
        return (
          <div className="flex flex-row gap-2 justify-center">
            <Button
              isIconOnly
              isLoading={loadingRowId === item.id}
              size="sm"
              className="rounded-full bg-blue-700" onClick={() => {
                handleGetDetails(item.id, 'view');
              }}>
              <ViewIcon size={18} className="text-white" />
            </Button>
            {
              !["placed", "created", "unverified"].includes(item.status.toLowerCase()) ?
                <Tooltip
                  color="warning"
                  content={
                    <div className="px-1 py-2 max-w-[300px]">
                      <div className="text-small font-bold">Action Restricted</div>
                      <div>
                        This request cannot be abandoned because it has passed verification. Please contact your account manager for more information.
                      </div>
                    </div>
                  }>
                  <Button isIconOnly size="sm" className="rounded-full bg-gray-400 cursor-not-allowed" >
                    <PencilEdit01Icon size={18} className="text-white" />
                  </Button>
                </Tooltip> :
                <Button isLoading={loadingRowEditId === item.id}
                  isIconOnly size="sm" className="rounded-full bg-glb_red" onClick={() => {
                    handleGetDetails(item.id, 'edit');
                  }}>
                  <PencilEdit01Icon size={18} className="text-white" />
                </Button>
            }
          </div >
        );
      default:
        return <div dangerouslySetInnerHTML={{ __html: cellValue }} />;
    }
  }, [loadingRowId, loadingRowEditId]);

  return (
    <>
      <DashboardLayout
        title="All Sourcing Requests"
        icon={<Home01Icon className="text-info" />}
        actions={
          <div className="flex flex-row  gap-2 flex-1 justify-end">
            <Button
              color="default"
              className="rounded-full bg-info text-white"
              onClick={() => setIsCreateNewRequestModalOpen(true)}>
              <Add01Icon size={18} /> Create New Request
            </Button>

          </div>
        }>
        <div>
          {/* <CustomTabs
                        activeCount={rows.filter((row) => row.active).length}
                        archivedCount={
                            rows.filter((row) => !row.active).length
                        }
                        selectedTab={selectedTab}
                        onTabChange={setSelectedTab}
                    /> */}
        </div>

        <div className="w-full py-4 rounded">
          <CustomTable
            clickableRow={true}
            clickableRowAction={(row) => {
              handleGetDetails(row.id, 'view');
            }}
            columns={columns}
            data={sourcingRequest.result} // Pass filtered products based on the view
            paginate={sourcingRequest.paginate}
            renderCell={renderCell}
            setSelectedRows={setSelectedRows}
            selectedRows={selectedRows} // Pass selected rows state
            className="dark:bg-gray-800 dark:text-white" // Dark mode support
            loading={loading} // Pass loading state
            error={error} // Pass error state
            currentPage={currentPage} // Pass current page
            setCurrentPage={setCurrentPage} // Pass setCurrentPage function
          />
        </div>

        {/* CREATE NEW REQUEST MODAL */}
        <CreateNewSourcingRequest
          isOpen={isCreateNewRequestModalOpen}
          onClose={() => { setSelectedItemID(null); setIsCreateNewRequestModalOpen(false) }}
          selectedItemID={selectedItemID}
        />
        <ViewProductRequestModal
          isOpen={isViewProductRequestModalOpen}
          onClose={() => { setSelectedItemID(null); setIsViewProductRequestModalOpen(false) }}
          selectedItemID={selectedItemID}
        />
      </DashboardLayout>
    </>
  );
}
