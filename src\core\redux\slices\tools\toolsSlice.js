
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { orderPayementsUrl, countriesUrl, simulatorUrl } from "../URLs";
import { getToken } from "../../../services/TokenHandler";
import axios from "axios";
import { toast } from "sonner";


const initialState = {
    loading: false,
    error: null,
};

// Async thunk for fetching order payements
export const simulate = createAsyncThunk(
    "simulate/post",
    async (data, { rejectWithValue }) => {
        try {
            const response = await axios.post(`${simulatorUrl}`,
                data,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error simulating' });
            }
            return response.data.result;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message })
        };
    }
);

const toolsSlice = createSlice({
    name: "tools",
    initialState,
    extraReducers: (builder) => {
        builder
            .addCase(simulate.pending, (state) => {
                state.loadingPayments = true;
                state.error = null;
            })
            .addCase(simulate.fulfilled, (state, action) => {
                state.loadingPayments = false;
                state.simulatorResult = action.payload;
            })
            .addCase(simulate.rejected, (state, action) => {
                state.loadingPayments = false;
                state.error = action.payload;
            });
    },
});


export default toolsSlice.reducer;


