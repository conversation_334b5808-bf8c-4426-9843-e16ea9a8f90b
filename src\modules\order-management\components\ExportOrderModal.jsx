import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { useDropzone } from "react-dropzone";
import { But<PERSON> } from "@heroui/button";
import {
  Cancel01Icon,
  CloudUploadIcon,
  File02Icon,
  FileDownloadIcon,
  GoogleSheetIcon,
  Link03Icon,
  RepeatIcon,
  Unlink03Icon,
} from "hugeicons-react";
import { Progress } from "@heroui/progress";
import CustomModal from "@shared/components/CustomModal.jsx";
import { Link } from "react-router-dom";
import googlesheet from "@shared/assets/images/googlesheet.svg";
import shopify from "@shared/assets/images/shopify.svg";
import lightfunnels from "@shared/assets/images/lightfunnels.png";
import youcan from "@shared/assets/images/youcan.png";
import { Card, CardBody, CardFooter, CardHeader } from "@heroui/card";
import { DateRangePicker, Divider, Input, Select, SelectItem } from "@heroui/react";
import codPowerGroupLogo from "@shared/assets/images/cod-power-group-logo.svg";
import codPowerGroupLogoDark from "@shared/assets/images/cod-power-group-logo-dark.svg";
import { useDispatch, useSelector } from "react-redux";
import { useThemeProvider } from "../../../core/providers/ThemeContext";
import { parseDate } from "@internationalized/date";
import { CreateOrdersExcel } from "../../../core/services/ExcelHandler";
import moment from "moment";



const ExportOrderModal = ({ isOpen, setIsOpen }) => {
  const { currentTheme } = useThemeProvider();
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [dateRange, setDateRange] = useState({
    start: parseDate(moment().subtract(7, 'days').format('YYYY-MM-DD')),
    end: parseDate(moment().format('YYYY-MM-DD')),
  });

  const modalRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setShopifyStore('');
        setshopifyInputOpen(false)
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const status = ['All', 'New Lead', 'Confirmed', 'All Confirmed', 'No Answer', 'Cancelled'];

  const [loadingExcel, setLoadingExcel] = useState(false)
  const handleSheet = async () => {
    setLoadingExcel(true)
    await CreateOrdersExcel()
    setLoadingExcel(false)
  }




  return (
    <CustomModal
      isOpen={isOpen}
      onClose={setIsOpen}
      width="max-w-lg"
      height="h-fit"

      showHeader={false}
      bodyClassName="p-8"
      footerContent={
        <div className="flex w-full gap-2">

          <Button
            className="mx-auto px-8 py-2 rounded-full bg-glb_red text-white flex flex-row items-center justify-center gap-1"
            onClick={() => setIsOpen(false)}
          >
            <Cancel01Icon size={18} />
            Close
          </Button>
          <Button
            isLoading={loadingExcel}
            className="mx-auto px-8 py-2 rounded-full bg-glb_green text-white flex flex-row items-center justify-center gap-1"
            onClick={() => handleSheet()}
          >
            <GoogleSheetIcon size={18} />
            Export
          </Button>
        </div>
      }
    >
      <div className="w-full max-w-2xl mx-auto p-2 lg:p-4">
        <h2 className="text-xl text-center lg:text-start font-semibold mb-2 w-full">
          Export your order by status or date
        </h2>
        <div className="flex flex-col w-full my-4 justify-center items-center gap-2">
          <Select
            selectedKeys={selectedStatus}
            // onChange={(value) => handleInputChange("productType", value)}
            onSelectionChange={(value) => setSelectedStatus(value)}
            color="primary" className="max-w-xs" label="Select a status" variant='underlined'>
            {status.map((s) => (
              <SelectItem key={s}>{s}</SelectItem>
            ))}
          </Select>
          <DateRangePicker
            isRequired
            className="max-w-xs"
            onClick={(e) => e.stopPropagation()}
            value={dateRange}
            onChange={setDateRange}
            color="primary"
            label="Date"
            variant='underlined'
          />
        </div>





      </div>
    </CustomModal>
  );
};

export default ExportOrderModal;
