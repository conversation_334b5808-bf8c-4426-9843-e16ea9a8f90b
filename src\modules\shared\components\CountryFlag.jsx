import {COUNTRIES} from "@/core/constants/countries.js";

const CountryFlag = ({
                         countryCode,
                         flagClassName = 'inline mr-2 h-4 rounded-sm',
                         showName = false,
                     }) => {
    return (
        <>
            <img
                alt={`${countryCode}`}
                src={`https://purecatamphetamine.github.io/country-flag-icons/3x2/${countryCode}.svg`}
                className={flagClassName}
            />
            <span className={`${showName ? 'inline' : 'hidden'} mr-2`}>
                {COUNTRIES.find(option => option.value === countryCode)['title']}
            </span>
        </>

    );
};

export default CountryFlag;