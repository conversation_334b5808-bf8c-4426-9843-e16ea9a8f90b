import react from '@vitejs/plugin-react';
import path from "path";
import {defineConfig} from 'vite';

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [react()],
    resolve: {
        alias: {
            "@": path.resolve(path.dirname(''), './src'),
            "@shared": path.resolve(path.dirname(''), './src/modules/shared'),
            "@assets":path.resolve(path.dirname(''), './public/assets'),
        }
    }
})
