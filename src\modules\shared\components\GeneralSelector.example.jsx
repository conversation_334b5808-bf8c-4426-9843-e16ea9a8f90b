import React, { useState } from 'react';
import GeneralSelector from './GeneralSelector';

// Example usage of the updated GeneralSelector component
export default function GeneralSelectorExample() {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedValue, setSelectedValue] = useState(null);
    const [multiSelectedValues, setMultiSelectedValues] = useState([]);

    // Example options as array of objects with id and title
    const options = [
        { id: '1', title: 'Option 1' },
        { id: '2', title: 'Option 2' },
        { id: '3', title: 'Option 3' },
        { id: '4', title: 'Option 4' },
        { id: '5', title: 'Option 5' },
    ];

    // Example with different property names (still supported)
    const alternativeOptions = [
        { id: 'user1', name: '<PERSON>' },
        { id: 'user2', name: '<PERSON>' },
        { id: 'user3', label: '<PERSON>' },
        { id: 'user4', value: 'alice', title: '<PERSON>' }, // fallback to value if id missing
    ];

    return (
        <div className="p-4 space-y-6">
            <h2 className="text-xl font-bold">GeneralSelector Examples</h2>
            
            {/* Single Selection Example */}
            <div>
                <h3 className="text-lg font-semibold mb-2">Single Selection</h3>
                <GeneralSelector
                    id="single-selector"
                    open={isOpen}
                    onToggle={() => setIsOpen(!isOpen)}
                    onChange={(value) => {
                        setSelectedValue(value);
                        console.log('Selected value ID:', value);
                    }}
                    selectedValue={selectedValue}
                    options={options}
                    placeholder="Select an option..."
                />
                <p className="mt-2 text-sm text-gray-600">
                    Selected ID: {selectedValue || 'None'}
                </p>
            </div>

            {/* Multiple Selection Example */}
            <div>
                <h3 className="text-lg font-semibold mb-2">Multiple Selection</h3>
                <GeneralSelector
                    id="multi-selector"
                    open={isOpen}
                    onToggle={() => setIsOpen(!isOpen)}
                    onChange={(values) => {
                        setMultiSelectedValues(values || []);
                        console.log('Selected value IDs:', values);
                    }}
                    selectedValue={multiSelectedValues}
                    options={alternativeOptions}
                    placeholder="Select multiple options..."
                    selectMultiple={true}
                />
                <p className="mt-2 text-sm text-gray-600">
                    Selected IDs: {multiSelectedValues.length > 0 ? multiSelectedValues.join(', ') : 'None'}
                </p>
            </div>

            {/* Backward Compatibility - String Array */}
            <div>
                <h3 className="text-lg font-semibold mb-2">Backward Compatibility (String Array)</h3>
                <GeneralSelector
                    id="string-selector"
                    open={isOpen}
                    onToggle={() => setIsOpen(!isOpen)}
                    onChange={(value) => console.log('Selected string:', value)}
                    selectedValue={null}
                    options={['String Option 1', 'String Option 2', 'String Option 3']}
                    placeholder="Select a string option..."
                />
            </div>
        </div>
    );
}
