export const rows = [
  { key: "1", name: "United States", shipping: "Yes", tags: "Technology, Finance, Entertainment" },
  { key: "2", name: "Canada", slogan: "The Great White North", shipping: "No", tags: "Natural Resources, Healthcare, Tourism" },
  { key: "3", name: "Japan", slogan: "Land of the Rising Sun", shipping: "Yes", tags: "Technology, Robotics, Culture" },
  { key: "4", name: "Germany", slogan: "Engineering Excellence", shipping: "Yes", tags: "Automotive, Engineering, Beer" },
  { key: "5", name: "Brazil", slogan: "Order and Progress", shipping: "No", tags: "Agriculture, Football, Carnival" },
  { key: "6", name: "Australia", slogan: "Down Under", shipping: "Yes", tags: "Tourism, Mining, Beaches" },
  { key: "7", name: "India", slogan: "Unity in Diversity", shipping: "Yes", tags: "Technology, Agriculture, Bollywood" },
  { key: "8", name: "China", slogan: "The Middle Kingdom", shipping: "No", tags: "Manufacturing, Technology, Silk Road" },
  { key: "9", name: "France", slogan: "Liberty, Equality, Fraternity", shipping: "Yes", tags: "Fashion, Wine, Art" },
  { key: "10", name: "Italy", slogan: "Land of Art and History", shipping: "No", tags: "Cuisine, Fashion, Art" },
];


export const countries_code = [
  { code: "AF", telephoneCode: "+93", flag: "🇦🇫", name: "Afghanistan" },
  { code: "AL", telephoneCode: "+355", flag: "🇦🇱", name: "Albania" },
  { code: "DZ", telephoneCode: "+213", flag: "🇩🇿", name: "Algeria" },
  { code: "AD", telephoneCode: "+376", flag: "🇦🇩", name: "Andorra" },
  { code: "AO", telephoneCode: "+244", flag: "🇦🇴", name: "Angola" },
  { code: "AR", telephoneCode: "+54", flag: "🇦🇷", name: "Argentina" },
  { code: "AM", telephoneCode: "+374", flag: "🇦🇲", name: "Armenia" },
  { code: "AU", telephoneCode: "+61", flag: "🇦🇺", name: "Australia" },
  { code: "AT", telephoneCode: "+43", flag: "🇦🇹", name: "Austria" },
  { code: "AZ", telephoneCode: "+994", flag: "🇦🇿", name: "Azerbaijan" },
  { code: "BH", telephoneCode: "+973", flag: "🇧🇭", name: "Bahrain" },
  { code: "BD", telephoneCode: "+880", flag: "🇧🇩", name: "Bangladesh" },
  { code: "BY", telephoneCode: "+375", flag: "🇧🇾", name: "Belarus" },
  { code: "BE", telephoneCode: "+32", flag: "🇧🇪", name: "Belgium" },
  { code: "BZ", telephoneCode: "+501", flag: "🇧🇿", name: "Belize" },
  { code: "BJ", telephoneCode: "+229", flag: "🇧🇯", name: "Benin" },
  { code: "BT", telephoneCode: "+975", flag: "🇧🇹", name: "Bhutan" },
  { code: "BO", telephoneCode: "+591", flag: "🇧🇴", name: "Bolivia" },
  { code: "BA", telephoneCode: "+387", flag: "🇧🇦", name: "Bosnia and Herzegovina" },
  { code: "BW", telephoneCode: "+267", flag: "🇧🇼", name: "Botswana" },
  { code: "BR", telephoneCode: "+55", flag: "🇧🇷", name: "Brazil" },
  { code: "BN", telephoneCode: "+673", flag: "🇧🇳", name: "Brunei" },
  { code: "BG", telephoneCode: "+359", flag: "🇧🇬", name: "Bulgaria" },
  { code: "BF", telephoneCode: "+226", flag: "🇧🇫", name: "Burkina Faso" },
  { code: "BI", telephoneCode: "+257", flag: "🇧🇮", name: "Burundi" },
  { code: "KH", telephoneCode: "+855", flag: "🇰🇭", name: "Cambodia" },
  { code: "CM", telephoneCode: "+237", flag: "🇨🇲", name: "Cameroon" },
  { code: "CA", telephoneCode: "+1", flag: "🇨🇦", name: "Canada" },
  { code: "CV", telephoneCode: "+238", flag: "🇨🇻", name: "Cape Verde" },
  { code: "CF", telephoneCode: "+236", flag: "🇨🇫", name: "Central African Republic" },
  { code: "TD", telephoneCode: "+235", flag: "🇹🇩", name: "Chad" },
  { code: "CL", telephoneCode: "+56", flag: "🇨🇱", name: "Chile" },
  { code: "CN", telephoneCode: "+86", flag: "🇨🇳", name: "China" },
  { code: "CO", telephoneCode: "+57", flag: "🇨🇴", name: "Colombia" },
  { code: "KM", telephoneCode: "+269", flag: "🇰🇲", name: "Comoros" },
  { code: "CD", telephoneCode: "+243", flag: "🇨🇩", name: "Congo (DRC)" },
  { code: "CG", telephoneCode: "+242", flag: "🇨🇬", name: "Congo (Republic)" },
  { code: "CR", telephoneCode: "+506", flag: "🇨🇷", name: "Costa Rica" },
  { code: "CI", telephoneCode: "+225", flag: "🇨🇮", name: "Côte d'Ivoire" },
  { code: "HR", telephoneCode: "+385", flag: "🇭🇷", name: "Croatia" },
  { code: "CU", telephoneCode: "+53", flag: "🇨🇺", name: "Cuba" },
  { code: "CY", telephoneCode: "+357", flag: "🇨🇾", name: "Cyprus" },
  { code: "CZ", telephoneCode: "+420", flag: "🇨🇿", name: "Czech Republic" },
  { code: "DK", telephoneCode: "+45", flag: "🇩🇰", name: "Denmark" },
  { code: "DJ", telephoneCode: "+253", flag: "🇩🇯", name: "Djibouti" },
  { code: "DM", telephoneCode: "******", flag: "🇩🇲", name: "Dominica" },
  { code: "DO", telephoneCode: "******", flag: "🇩🇴", name: "Dominican Republic" },
  { code: "EC", telephoneCode: "+593", flag: "🇪🇨", name: "Ecuador" },
  { code: "EG", telephoneCode: "+20", flag: "🇪🇬", name: "Egypt" },
  { code: "SV", telephoneCode: "+503", flag: "🇸🇻", name: "El Salvador" },
  { code: "GQ", telephoneCode: "+240", flag: "🇬🇶", name: "Equatorial Guinea" },
  { code: "ER", telephoneCode: "+291", flag: "🇪🇷", name: "Eritrea" },
  { code: "EE", telephoneCode: "+372", flag: "🇪🇪", name: "Estonia" },
  { code: "ET", telephoneCode: "+251", flag: "🇪🇹", name: "Ethiopia" },
  { code: "FJ", telephoneCode: "+679", flag: "🇫🇯", name: "Fiji" },
  { code: "FI", telephoneCode: "+358", flag: "🇫🇮", name: "Finland" },
  { code: "FR", telephoneCode: "+33", flag: "🇫🇷", name: "France" },
  { code: "GA", telephoneCode: "+241", flag: "🇬🇦", name: "Gabon" },
  { code: "GM", telephoneCode: "+220", flag: "🇬🇲", name: "Gambia" },
  { code: "GE", telephoneCode: "+995", flag: "🇬🇪", name: "Georgia" },
  { code: "DE", telephoneCode: "+49", flag: "🇩🇪", name: "Germany" },
  { code: "GH", telephoneCode: "+233", flag: "🇬🇭", name: "Ghana" },
  { code: "GR", telephoneCode: "+30", flag: "🇬🇷", name: "Greece" },
  { code: "GD", telephoneCode: "******", flag: "🇬🇩", name: "Grenada" },
  { code: "GT", telephoneCode: "+502", flag: "🇬🇹", name: "Guatemala" },
  { code: "GN", telephoneCode: "+224", flag: "🇬🇳", name: "Guinea" },
  { code: "GW", telephoneCode: "+245", flag: "🇬🇼", name: "Guinea-Bissau" },
  { code: "GY", telephoneCode: "+592", flag: "🇬🇾", name: "Guyana" },
  { code: "HT", telephoneCode: "+509", flag: "🇭🇹", name: "Haiti" },
  { code: "HN", telephoneCode: "+504", flag: "🇭🇳", name: "Honduras" },
  { code: "HU", telephoneCode: "+36", flag: "🇭🇺", name: "Hungary" },
  { code: "IS", telephoneCode: "+354", flag: "🇮🇸", name: "Iceland" },
  { code: "IN", telephoneCode: "+91", flag: "🇮🇳", name: "India" },
  { code: "ID", telephoneCode: "+62", flag: "🇮🇩", name: "Indonesia" },
  { code: "IR", telephoneCode: "+98", flag: "🇮🇷", name: "Iran" },
  { code: "IQ", telephoneCode: "+964", flag: "🇮🇶", name: "Iraq" },
  { code: "IE", telephoneCode: "+353", flag: "🇮🇪", name: "Ireland" },
  { code: "IL", telephoneCode: "+972", flag: "🇮🇱", name: "Israel" },
  { code: "IT", telephoneCode: "+39", flag: "🇮🇹", name: "Italy" },
  { code: "JM", telephoneCode: "******", flag: "🇯🇲", name: "Jamaica" },
  { code: "JP", telephoneCode: "+81", flag: "🇯🇵", name: "Japan" },
  { code: "JO", telephoneCode: "+962", flag: "🇯🇴", name: "Jordan" },
  { code: "KZ", telephoneCode: "+7", flag: "🇰🇿", name: "Kazakhstan" },
  { code: "KE", telephoneCode: "+254", flag: "🇰🇪", name: "Kenya" },
  { code: "KI", telephoneCode: "+686", flag: "🇰🇮", name: "Kiribati" },
  { code: "KP", telephoneCode: "+850", flag: "🇰🇵", name: "North Korea" },
  { code: "KR", telephoneCode: "+82", flag: "🇰🇷", name: "South Korea" },
  { code: "KW", telephoneCode: "+965", flag: "🇰🇼", name: "Kuwait" },
  { code: "KG", telephoneCode: "+996", flag: "🇰🇬", name: "Kyrgyzstan" },
  { code: "LA", telephoneCode: "+856", flag: "🇱🇦", name: "Laos" },
  { code: "LV", telephoneCode: "+371", flag: "🇱🇻", name: "Latvia" },
  { code: "LB", telephoneCode: "+961", flag: "🇱🇧", name: "Lebanon" },
  { code: "LS", telephoneCode: "+266", flag: "🇱🇸", name: "Lesotho" },
  { code: "LR", telephoneCode: "+231", flag: "🇱🇷", name: "Liberia" },
  { code: "LY", telephoneCode: "+218", flag: "🇱🇾", name: "Libya" },
  { code: "LI", telephoneCode: "+423", flag: "🇱🇮", name: "Liechtenstein" },
  { code: "LT", telephoneCode: "+370", flag: "🇱🇹", name: "Lithuania" },
  { code: "LU", telephoneCode: "+352", flag: "🇱🇺", name: "Luxembourg" },
  { code: "MG", telephoneCode: "+261", flag: "🇲🇬", name: "Madagascar" },
  { code: "MW", telephoneCode: "+265", flag: "🇲🇼", name: "Malawi" },
  { code: "MY", telephoneCode: "+60", flag: "🇲🇾", name: "Malaysia" },
  { code: "MV", telephoneCode: "+960", flag: "🇲🇻", name: "Maldives" },
  { code: "ML", telephoneCode: "+223", flag: "🇲🇱", name: "Mali" },
  { code: "MT", telephoneCode: "+356", flag: "🇲🇹", name: "Malta" },
  { code: "MH", telephoneCode: "+692", flag: "🇲🇭", name: "Marshall Islands" },
  { code: "MR", telephoneCode: "+222", flag: "🇲🇷", name: "Mauritania" },
  { code: "MU", telephoneCode: "+230", flag: "🇲🇺", name: "Mauritius" },
  { code: "MX", telephoneCode: "+52", flag: "🇲🇽", name: "Mexico" },
  { code: "FM", telephoneCode: "+691", flag: "🇫🇲", name: "Micronesia" },
  { code: "MD", telephoneCode: "+373", flag: "🇲🇩", name: "Moldova" },
  { code: "MC", telephoneCode: "+377", flag: "🇲🇨", name: "Monaco" },
  { code: "MN", telephoneCode: "+976", flag: "🇲🇳", name: "Mongolia" },
  { code: "ME", telephoneCode: "+382", flag: "🇲🇪", name: "Montenegro" },
  { code: "MA", telephoneCode: "+212", flag: "🇲🇦", name: "Morocco" },
  { code: "MZ", telephoneCode: "+258", flag: "🇲🇿", name: "Mozambique" },
  { code: "MM", telephoneCode: "+95", flag: "🇲🇲", name: "Myanmar" },
  { code: "NA", telephoneCode: "+264", flag: "🇳🇦", name: "Namibia" },
  { code: "NR", telephoneCode: "+674", flag: "🇳🇷", name: "Nauru" },
  { code: "NP", telephoneCode: "+977", flag: "🇳🇵", name: "Nepal" },
  { code: "NL", telephoneCode: "+31", flag: "🇳🇱", name: "Netherlands" },
  { code: "NZ", telephoneCode: "+64", flag: "🇳🇿", name: "New Zealand" },
  { code: "NI", telephoneCode: "+505", flag: "🇳🇮", name: "Nicaragua" },
  { code: "NE", telephoneCode: "+227", flag: "🇳🇪", name: "Niger" },
  { code: "NG", telephoneCode: "+234", flag: "🇳🇬", name: "Nigeria" },
  { code: "NO", telephoneCode: "+47", flag: "🇳🇴", name: "Norway" },
  { code: "OM", telephoneCode: "+968", flag: "🇴🇲", name: "Oman" },
  { code: "PK", telephoneCode: "+92", flag: "🇵🇰", name: "Pakistan" },
  { code: "PW", telephoneCode: "+680", flag: "🇵🇼", name: "Palau" },
  { code: "PS", telephoneCode: "+970", flag: "🇵🇸", name: "Palestine" },
  { code: "PA", telephoneCode: "+507", flag: "🇵🇦", name: "Panama" },
  { code: "PG", telephoneCode: "+675", flag: "🇵🇬", name: "Papua New Guinea" },
  { code: "PY", telephoneCode: "+595", flag: "🇵🇾", name: "Paraguay" },
  { code: "PE", telephoneCode: "+51", flag: "🇵🇪", name: "Peru" },
  { code: "PH", telephoneCode: "+63", flag: "🇵🇭", name: "Philippines" },
  { code: "PL", telephoneCode: "+48", flag: "🇵🇱", name: "Poland" },
  { code: "PT", telephoneCode: "+351", flag: "🇵🇹", name: "Portugal" },
  { code: "QA", telephoneCode: "+974", flag: "🇶🇦", name: "Qatar" },
  { code: "RO", telephoneCode: "+40", flag: "🇷🇴", name: "Romania" },
  { code: "RU", telephoneCode: "+7", flag: "🇷🇺", name: "Russia" },
  { code: "RW", telephoneCode: "+250", flag: "🇷🇼", name: "Rwanda" },
  { code: "WS", telephoneCode: "+685", flag: "🇼🇸", name: "Samoa" },
  { code: "SM", telephoneCode: "+378", flag: "🇸🇲", name: "San Marino" },
  { code: "ST", telephoneCode: "+239", flag: "🇸🇹", name: "Sao Tome and Principe" },
  { code: "SA", telephoneCode: "+966", flag: "🇸🇦", name: "Saudi Arabia" },
  { code: "SN", telephoneCode: "+221", flag: "🇸🇳", name: "Senegal" },
  { code: "RS", telephoneCode: "+381", flag: "🇷🇸", name: "Serbia" },
  { code: "SC", telephoneCode: "+248", flag: "🇸🇨", name: "Seychelles" },
  { code: "SL", telephoneCode: "+232", flag: "🇸🇱", name: "Sierra Leone" },
  { code: "SG", telephoneCode: "+65", flag: "🇸🇬", name: "Singapore" },
  { code: "SK", telephoneCode: "+421", flag: "🇸🇰", name: "Slovakia" },
  { code: "SI", telephoneCode: "+386", flag: "🇸🇮", name: "Slovenia" },
  { code: "SB", telephoneCode: "+677", flag: "🇸🇧", name: "Solomon Islands" },
  { code: "SO", telephoneCode: "+252", flag: "🇸🇴", name: "Somalia" },
  { code: "ZA", telephoneCode: "+27", flag: "🇿🇦", name: "South Africa" },
  { code: "SS", telephoneCode: "+211", flag: "🇸🇸", name: "South Sudan" },
  { code: "ES", telephoneCode: "+34", flag: "🇪🇸", name: "Spain" },
  { code: "LK", telephoneCode: "+94", flag: "🇱🇰", name: "Sri Lanka" },
  { code: "SD", telephoneCode: "+249", flag: "🇸🇩", name: "Sudan" },
  { code: "SR", telephoneCode: "+597", flag: "🇸🇷", name: "Suriname" },
  { code: "SZ", telephoneCode: "+268", flag: "🇸🇿", name: "Eswatini" },
  { code: "SE", telephoneCode: "+46", flag: "🇸🇪", name: "Sweden" },
  { code: "CH", telephoneCode: "+41", flag: "🇨🇭", name: "Switzerland" },
  { code: "SY", telephoneCode: "+963", flag: "🇸🇾", name: "Syria" },
  { code: "TJ", telephoneCode: "+992", flag: "🇹🇯", name: "Tajikistan" },
  { code: "TZ", telephoneCode: "+255", flag: "🇹🇿", name: "Tanzania" },
  { code: "TH", telephoneCode: "+66", flag: "🇹🇭", name: "Thailand" },
  { code: "TL", telephoneCode: "+670", flag: "🇹🇱", name: "Timor-Leste" },
  { code: "TG", telephoneCode: "+228", flag: "🇹🇬", name: "Togo" },
  { code: "TO", telephoneCode: "+676", flag: "🇹🇴", name: "Tonga" },
  { code: "TT", telephoneCode: "******", flag: "🇹🇹", name: "Trinidad and Tobago" },
  { code: "TN", telephoneCode: "+216", flag: "🇹🇳", name: "Tunisia" },
  { code: "TR", telephoneCode: "+90", flag: "🇹🇷", name: "Turkey" },
  { code: "TM", telephoneCode: "+993", flag: "🇹🇲", name: "Turkmenistan" },
  { code: "TV", telephoneCode: "+688", flag: "🇹🇻", name: "Tuvalu" },
  { code: "UG", telephoneCode: "+256", flag: "🇺🇬", name: "Uganda" },
  { code: "UA", telephoneCode: "+380", flag: "🇺🇦", name: "Ukraine" },
  { code: "AE", telephoneCode: "+971", flag: "🇦🇪", name: "United Arab Emirates" },
  { code: "GB", telephoneCode: "+44", flag: "🇬🇧", name: "United Kingdom" },
  { code: "US", telephoneCode: "+1", flag: "🇺🇸", name: "United States" },
  { code: "UY", telephoneCode: "+598", flag: "🇺🇾", name: "Uruguay" },
  { code: "UZ", telephoneCode: "+998", flag: "🇺🇿", name: "Uzbekistan" },
  { code: "VU", telephoneCode: "+678", flag: "🇻🇺", name: "Vanuatu" },
  { code: "VE", telephoneCode: "+58", flag: "🇻🇪", name: "Venezuela" },
  { code: "VN", telephoneCode: "+84", flag: "🇻🇳", name: "Vietnam" },
  { code: "YE", telephoneCode: "+967", flag: "🇾🇪", name: "Yemen" },
  { code: "ZM", telephoneCode: "+260", flag: "🇿🇲", name: "Zambia" },
  { code: "ZW", telephoneCode: "+263", flag: "🇿🇼", name: "Zimbabwe" },
]