import { useState } from 'react';
import Mastercard from '@shared/assets/images/onboarding/Mastercard.svg'
import Visa from '@shared/assets/images/onboarding/Visa.svg'
import { Input } from "@heroui/input";

const CARD_PATTERNS = {
    visa: /^4/,
    mastercard: /^5[1-5]|^2[2-7]/,
    amex: /^3[47]/,
};

export const CardNumberInput = ({ value, onChange }) => {
    const [cardType, setCardType] = useState(null);

    const formatCardNumber = (input) => {
        // Remove all non-digits and spaces
        const cleaned = input.replace(/[^\d\s]/g, '');
        // Remove all spaces
        const numbersOnly = cleaned.replace(/\s/g, '');
        const limit = 16;
        let formatted = '';

        // Add spaces every 4 characters
        for (let i = 0; i < numbersOnly.length && i < limit; i++) {
            if (i > 0 && i % 4 === 0) {
                formatted += ' ';
            }
            formatted += numbersOnly[i];
        }

        return formatted;
    };

    const detectCardType = (number) => {
        const cleaned = number.replace(/\s/g, '');
        if (CARD_PATTERNS.visa.test(cleaned)) return 'visa';
        if (CARD_PATTERNS.mastercard.test(cleaned)) return 'mastercard';
        if (CARD_PATTERNS.amex.test(cleaned)) return 'amex';
        return null;
    };

    const handleChange = (e) => {
        let newValue = e.target.value;

        // Handle deletion/backspace
        if (newValue.length < value.length) {
            newValue = newValue.replace(/\s+$/, ''); // Remove trailing spaces
            const formatted = formatCardNumber(newValue);
            setCardType(detectCardType(formatted));
            onChange(formatted);
            return;
        }

        // Handle addition
        const formatted = formatCardNumber(newValue);
        setCardType(detectCardType(formatted));
        onChange(formatted);
    };

    const CardIcon = () => {
        switch (cardType) {
            case 'visa':
                return <img src={Visa} alt="Visa Card" className="w-6 h-6 me-1 text-gray-400" />;
            case 'mastercard':
                return <img src={Mastercard} alt="Mastercard" className="w-6 me-1 h-6" />;
            default:
                return <img src={Mastercard} alt="Mastercard" className="w-6 me-1 h-6" />;
        }
    };

    return (
        <Input
            type="text"
            placeholder="1234 1234 1234 1234"
            value={value}
            onChange={handleChange}
            maxLength={19} // 16 digits + 3 spaces
            startContent={<CardIcon />}
            className="font-mono"
            classNames={{
                inputWrapper: `bg-black/5 dark:bg-white/5 border-1 border-black/5 dark:border-white/5 rounded-md py-6 px-4 focus:bg-normal`
            }}
        />
    );
};

export const ExpiryInput = ({ value, onChange }) => {
    const formatExpiry = (input) => {
        // Remove all non-digits
        const cleaned = input.replace(/\D/g, '');

        // Handle backspace
        if (cleaned.length <= 2) return cleaned;

        // Format as MM / YY
        const month = cleaned.substring(0, 2);
        const year = cleaned.substring(2, 4);
        return month + (year.length ? ' / ' + year : '');
    };

    const handleChange = (e) => {
        const formatted = formatExpiry(e.target.value);
        onChange(formatted);
    };

    const validateMonth = (input) => {
        const month = parseInt(input.substring(0, 2));
        return month > 0 && month <= 12;
    };

    return (
        <Input
            type="text"
            placeholder="MM / YY"
            value={value}
            onChange={handleChange}
            maxLength={7}
            className="font-mono"
            classNames={{
                inputWrapper: `bg-black/5 dark:bg-white/5 border ${value.length >= 2 && !validateMonth(value) ? 'border-red-500' : 'border-black/5 dark:border-white/5'} rounded-md py-6 px-4 focus:bg-normal`
            }}
        />
    );
};

export const CVVInput = ({ value, onChange }) => {
    const handleChange = (e) => {
        const cleaned = e.target.value.replace(/\D/g, '');
        onChange(cleaned);
    };

    return (
        <Input
            type="password"
            placeholder="123"
            value={value}
            onChange={handleChange}
            maxLength={3}
            className="font-mono"
            classNames={{
                inputWrapper: `bg-black/5 dark:bg-white/5 border-1 border-1 border-black/5 dark:border-white/5 rounded-md py-6 px-4 focus:bg-normal`
            }}
        />
    );
};