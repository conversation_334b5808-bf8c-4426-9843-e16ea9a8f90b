import { Button } from "@heroui/button";
import CustomModal from "@shared/components/CustomModal.jsx";
import React, { useEffect, useState, useRef } from "react";
import {
  Globe02Icon,
  DeliveryBox01Icon,
  TaskAdd01Icon,
  CheckmarkCircle02Icon,
  CheckmarkCircle04Icon,
  ArrowRight01Icon,
  PackageAddIcon,
  Delete02Icon,
  PackageProcessIcon,
  CancelCircleIcon,
  CloudUploadIcon,
  ArrowLeft01Icon,
  MessageCancel01Icon,
} from "hugeicons-react";
import { AnimatePresence, motion } from "framer-motion";
import CountrySelector from "../../shared/components/CountrySelector";
import axios from "axios";
import { getToken } from "../../../core/services/TokenHandler";
import { countriesUrl } from "../../../core/redux/slices/URLs";
import { Select, SelectItem } from "@heroui/select";
import UnderlinedInput from "../../settings/components/UnderlinedInput";
import { CustomRadio } from "../../shared/components/CustomRadio";
import { Checkbox, Radio, RadioGroup, Textarea, Progress, Popover, PopoverTrigger, PopoverContent } from "@heroui/react";
import { Input } from "@heroui/react";
import UnderlinedTextAreaInput from "../../settings/components/UnderlinedTextArea";
import { toast } from "sonner";
import { useDispatch, useSelector } from "react-redux";
import { getSourcingRequest, postSourcingRequest, updateSourcingRequest, deleteSourcingRequest } from "../../../core/redux/slices/sourcing/sourcingSlice";
import { urlPattern } from "../../../core/utils/functions";
import { fetchProductsCategories } from "../../../core/redux/slices/stock/products/productsSlice";
import { getProcessMode, getShippingMethods } from "../../../core/redux/slices/general/generalSlice";

const TimeLine = ({ steps, currentStep }) => {
  const stepKeys = Object.keys(steps);
  const currentIndex = stepKeys.indexOf(currentStep);
  const array = stepKeys;

  const icons = [Globe02Icon, PackageProcessIcon, TaskAdd01Icon];

  return (
    <div className="flex items-center font-sans select-none">
      {array.map((stepKey, i) => (
        <React.Fragment key={stepKey}>
          {/* Step label with icon */}
          <div
            className={`flex flex-col px-2 justify-center rounded-full items-center relative ${i === currentIndex ? "w-14 h-24" : "w-11 h-20"
              }`}>
            {i < currentIndex && (
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{
                  type: "spring",
                  stiffness: 260,
                  damping: 20,
                  duration: 0.5,
                }}
                className="absolute top-1 right-0">
                <CheckmarkCircle04Icon className="text-glb_green p-0 bg-white dark:bg-black rounded-full" />
              </motion.div>
            )}
            {React.createElement(icons[i], {
              className:
                (i < currentIndex
                  ? "bg-glb_blue"
                  : i === currentIndex
                    ? "bg-[#00000060] dark:bg-[#ffffff60]"
                    : "bg-[#00000020] dark:bg-[#ffffff20]") +
                " text-white rounded-full p-2",
              size: i === currentIndex ? 50 : 40,
            })}
            <motion.h6
              animate={{
                color:
                  i < currentIndex
                    ? "#0258E8"
                    : i === currentIndex
                      ? "#00000060"
                      : "#00000020",
              }}
              transition={{ duration: 0.3 }}
              className={`whitespace-nowrap font-semibold text-[12px]`}>
              {steps[stepKey]}
            </motion.h6>
          </div>

          {/* Line between steps */}
          {i < array.length - 1 && (
            <motion.div
              animate={{
                background:
                  i < currentIndex - 1
                    ? "linear-gradient(to right, #0258E8, #0258E8)"
                    : i === currentIndex - 1
                      ? "linear-gradient(to right, #0258E8, #00000060)"
                      : "linear-gradient(to right, #00000060, #00000000)",
              }}
              transition={{ duration: 0.5 }}
              className={`relative flex-1 h-0.5 mx-2 rounded-full ${i <= currentIndex ? "" : "opacity-20"
                }`}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

const CreateNewSourcingRequest = ({ isOpen, onClose, selectedItemID = null }) => {
  const steps = {
    origin: "Origin",
    products: "Products",
    validation: "Validation",
  };



  const [ProductsList, setProductsList] = useState([]);
  const [isModifying, setIsModifying] = useState(false);
  const [modifyingIndex, setModifyingIndex] = useState(null);
  const [category, setCategory] = useState({});
  const [ShippingMethods, setShippingMethods] = useState({});
  const [processingMode, setProcessingMode] = useState({})
  const [message, setMessage] = useState("");
  const [SourcingProducts, SetSourcingProducts] = useState({
    productName: "",
    productArabicName: "",
    productLink: "",
    productImage: null,
    imageUrl: null,
    category: "",
    shippingMethod: "",
    HasVariants: false,
    isTested: false,
    ProcessingMode: "Bulk",
    Qty: 100,
    originCountry: 4,
    TargetCountry: 0,
    note: "",
    variants: [],
  });

  const [currentStep, setCurrentStep] = useState("origin");
  const [isFromCountryOpen, setIsFromCountryOpen] = useState(false);
  const [isToCountryOpen, setIsToCountryOpen] = useState(false);
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [toCountries, setToCountries] = useState([]);
  const [fromCountries, setFromCountries] = useState([]);
  const [fromCountry, setFromCountry] = useState("CHINA");
  const [CountrFromPage, setCountrFromPage] = useState(1);
  const [CountrToPage, setCountrToPage] = useState(1);
  const [startProduct, setStartProduct] = useState(false);
  const { params, loading } = useSelector((state) => state.sourcing);
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [submitProgress, setSubmitProgress] = useState(0);

  // Add refs for inputs
  const containerRef = useRef(null);
  const productNameRef = useRef(null);
  const productLinkRef = useRef(null);
  const categoryRef = useRef(null);
  const shippingMethodRef = useRef(null);
  const variantsRef = useRef(null);
  const processingModeRef = useRef(null);
  const productImageRef = useRef(null);

  const scrollToRef = (ref) => {
    if (containerRef.current && ref.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const refRect = ref.current.getBoundingClientRect();
      const offset = refRect.top - containerRect.top + containerRef.current.scrollTop - containerRect.height / 2 + refRect.height / 2;
      containerRef.current.scrollTo({
        top: offset,
        behavior: "smooth",
      });
      ref.current.focus();
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      const fetchCategories = async () => {
        const res = await dispatch(fetchProductsCategories());

        if (fetchProductsCategories.fulfilled.match(res)) {
          setCategory(res.payload);
        }
      }
      const fetchShippingMethods = async () => {
        const res = await dispatch(getShippingMethods())
        if (getShippingMethods.fulfilled.match(res)) {
          setShippingMethods(res.payload)
        }
      }
      const fetchProcessMode = async () => {
        const res = await dispatch(getProcessMode())
        if (getProcessMode.fulfilled.match(res)) {
          setProcessingMode(res.payload)
        }
      }

      if (isOpen) {
        await fetchCategories();
        await fetchShippingMethods();
        await fetchProcessMode();
      }
    };

    fetchData();
  }, [dispatch, isOpen])


  useEffect(() => {
    if (selectedItemID) {
      // Ensure message is set correctly
      setMessage(selectedItemID.message || ""); // Default to an empty string if no message exists

      // Map products to ProductsList state
      setProductsList(
        Array.isArray(selectedItemID.products)
          ? selectedItemID.products.map((p) => ({
            productName: p.name || "",
            productArabicName: p.arabicName || "",
            productLink: p.productLink || "",
            imageUrl: p.image, // You may need to handle image loading separately
            category: p.category || "",
            shippingMethod: p.shippingMethod || "",
            HasVariants: Array.isArray(p.variants) && p.variants.length > 0,
            isTested: !!p.isTest,
            ProcessingMode: p.processMode || "Bulk",
            Qty: p.quantity || 100,
            originCountry: p.originCountry || 4,
            TargetCountry: p.destinationCountry || 0,
            note: p.note || "",
            variants: Array.isArray(p.variants)
              ? p.variants.map((v) => ({
                name: v.name || "",
                value: v.value || "",
              }))
              : [],
          }))
          : []
      );

      // Optionally set other fields if needed (e.g., step)
      setCurrentStep("products");
      // setStartProduct(true);
    } else {
      // Reset if no selectedItemID
      setMessage(""); // Reset message state
      setProductsList([]);
      setCurrentStep("origin");
      setStartProduct(false);
    }
    // eslint-disable-next-line
  }, [selectedItemID]);

  const isDisabled = () => {
    switch (currentStep) {
      case "origin":
        return !fromCountry;
      case "products":
        return ProductsList.length === 0;
      case "validation":
        break;

      default:
        return true;
        break;
    }
  };
  const handleBack = () => {
    switch (currentStep) {
      case "origin":
        break;
      case "products":
        setCurrentStep("origin");
        break;
      case "validation":
        setCurrentStep("products");
        break;

      default:
        break;
    }
  };

  const handleSubmit = async () => {
    if (loading) return;
    setIsSubmitting(true);
    setSubmitProgress(10); // Start progress

    try {
      const formData = new FormData();
      formData.append("message", message);

      // Simulate progress
      setSubmitProgress(30);

      const transformedProducts = ProductsList.map((product) => ({
        name: product.productName,
        arabicName: product.productArabicName,
        category: product.category,
        productLink: product.productLink,
        variants: product.variants.map((variant) => ({
          name: variant.name,
          value: variant.value,
        })),
        isTest: product.isTested,
        imageUrl: product.imageUrl,
        processMode: product.ProcessingMode,
        originCountry: product.originCountry,
        destinationCountry: product.ProcessingMode === 'Bulk' ? product.TargetCountry : null,
        quantity: product.Qty,
        shippingMethod: product.shippingMethod,
        note: product.note || "",
      }));

      setSubmitProgress(50);

      formData.append("products", JSON.stringify(transformedProducts));
      ProductsList.forEach((product, index) => {
        if (product.productImage) {
          formData.append(`product_${index}_image`, product.productImage);
        }
      });
      setSubmitProgress(70);

      let response;
      if (selectedItemID) {
        // Update sourcing request
        response = await dispatch(
          updateSourcingRequest({ id: selectedItemID.id, updatedData: formData })
        );
      } else {
        // Post new sourcing request
        response = await dispatch(postSourcingRequest(formData));
      }

      setSubmitProgress(90);

      if (
        (selectedItemID && updateSourcingRequest.fulfilled.match(response)) ||
        (!selectedItemID && postSourcingRequest.fulfilled.match(response))
      ) {
        setSubmitProgress(100);
        toast.success(
          selectedItemID
            ? "Sourcing request updated successfully"
            : "Sourcing request submitted successfully"
        );

        setTimeout(() => {
          setIsSubmitting(false);
          setSubmitProgress(0);
          setProductsList([]);
          SetSourcingProducts({
            productName: "",
            productArabicName: "",
            productLink: "",
            productImage: null,
            imageUrl: null,
            category: "",
            shippingMethod: "",
            HasVariants: false,
            isTested: false,
            ProcessingMode: "Bulk",
            Qty: 100,
            originCountry: 4,
            TargetCountry: 0,
            note: "",
            variants: [],
          });
          setMessage("");
          setCurrentStep("origin");
          onClose();
        }, 500);
        await dispatch(getSourcingRequest());
      } else {
        setIsSubmitting(false);
        setSubmitProgress(0);
        const errorMessage =
          response.payload?.message || "Failed to submit request";
        const errorMessages = errorMessage.split(",").map((msg) => msg.trim());
        errorMessages.forEach((msg) => {
          toast.error(msg);
        });
      }
    } catch (error) {
      setIsSubmitting(false);
      setSubmitProgress(0);
      toast.error(error.message || "Failed to submit request");
    }
  };

  const handleNext = () => {

    switch (currentStep) {
      case "origin":
        if (!fromCountry) {
          toast.warning("Please select a source country before proceeding");
          return;
        }
        setCurrentStep("products");
        break;
      case "products":
        setCurrentStep("validation");
        break;
      case "validation":
        handleSubmit();
        break;

      default:
        break;
    }
  };

  const CustomfetchCountries = async (type, page) => {
    // Don't fetch if we're already loading or if we've reached the last page
    if (
      loadingCountries ||
      (type === "origin" && page > CountrFromPage && page > 2) ||
      (type === "destination" && page > CountrToPage && page > 2)
    ) {
      return;
    }

    setLoadingCountries(true);
    try {
      const response = await axios.get(
        `${countriesUrl}?type=${type}&page=${page}`,
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        }
      );

      if (response.data.response !== "success") {
        console.error(response.data.message || "Error fetching orders status");
        return;
      }

      const newCountries = response.data.result || [];

      if (type === "sourcing") {
        setCountrFromPage(page);
        setFromCountries([{ id: 0, code: "CH", name: "CHINA", flag: "CN" }]);
      } else {

        setCountrToPage(page);
        setToCountries((prev) => {
          if (page === 1) {
            return [...newCountries];
          } else {
            const existingIds = new Set(prev.map((c) => c.id));
            const filteredNew = newCountries.filter(
              (c) => !existingIds.has(c.id)
            );
            return [...prev, ...filteredNew];
          }
        });
      }
    } catch (error) {
      console.error(error.response?.data?.message || error.message);
    } finally {
      setLoadingCountries(false);
    }
  };

  useEffect(() => {
    CustomfetchCountries("destination", CountrFromPage);
  }, [isOpen]);

  // Extracted validation function to remove duplication
  const validateProduct = () => {
    if (!SourcingProducts.productName) {
      toast.warning("Please fill in the product name");
      scrollToRef(productNameRef);
      return false;
    }
    if (!SourcingProducts.productLink) {
      toast.warning("Please fill in the product URL");
      scrollToRef(productLinkRef);
      return false;
    }
    if (!urlPattern.test(SourcingProducts.productLink)) {
      toast.warning("Please enter a valid product URL");
      scrollToRef(productLinkRef);
      return false;
    }
    if (!SourcingProducts.category) {
      toast.warning("Please select a category");
      scrollToRef(categoryRef);
      return false;
    }
    if (SourcingProducts.HasVariants) {
      if (SourcingProducts.variants.length === 0) {
        toast.warning("The product has variants ? Please add at least one.");
        scrollToRef(variantsRef);
        return false;
      }
      const hasEmptyVariant = SourcingProducts.variants.some(
        (variant) => !variant.name || !variant.value
      );
      if (hasEmptyVariant) {
        toast.warning("All variants must have a value");
        scrollToRef(variantsRef);
        return false;
      }
      const variantValues = SourcingProducts.variants.map((v) =>
        v.value.toLowerCase()
      );
      const hasDuplicateValues = variantValues.some(
        (value, index) => variantValues.indexOf(value) !== index
      );
      if (hasDuplicateValues) {
        toast.warning("Duplicate variant values are not allowed");
        scrollToRef(variantsRef);
        return false;
      }
    }
    if (!SourcingProducts.imageUrl) {
      toast.warning("Product image is required");
      scrollToRef(productImageRef);
      return false;
    }
    if (SourcingProducts.ProcessingMode === "Bulk") {
      if (!SourcingProducts.TargetCountry) {
        toast.warning("Please select a destination country for bulk processing");
        scrollToRef(processingModeRef);
        return false;
      }
      if (SourcingProducts.Qty < 100) {
        toast.warning("Minimum quantity for bulk processing is 100");
        scrollToRef(processingModeRef);
        return false;
      }
    } else {
      if (SourcingProducts.Qty < 30) {
        toast.warning("Minimum quantity for one-by-one processing is 30");
        scrollToRef(processingModeRef);
        return false;
      }
    }
    if (!SourcingProducts.shippingMethod) {
      toast.warning("Please select a shipping method");
      scrollToRef(shippingMethodRef);
      return false;
    }
    return true;
  };

  const handleAddProduct = () => {
    if (!validateProduct()) return;

    setProductsList((prev) => [...prev, SourcingProducts]);

    SetSourcingProducts({
      productName: "",
      productArabicName: "",
      productLink: "",
      productImage: null,
      imageUrl: null,
      category: "",
      shippingMethod: "",
      HasVariants: false,
      isTested: false,
      ProcessingMode: "Bulk",
      Qty: 100,
      TargetCountry: 0,
      note: "",
      variants: [],
    });
  };

  const handleModifyProduct = (product, index) => {
    SetSourcingProducts({
      productName: product.productName,
      productArabicName: product.productArabicName,
      productLink: product.productLink,
      imageUrl: product.imageUrl,
      category: product.category,
      shippingMethod: product.shippingMethod,
      HasVariants: product.HasVariants,
      isTested: product.isTested,
      ProcessingMode: product.ProcessingMode,
      Qty: product.Qty,
      TargetCountry: product.TargetCountry,
      note: product.note,
      variants: product.variants || [],
    });
    setIsModifying(true);
    setStartProduct(true);
    setModifyingIndex(index);
  };

  const handleAddOrModifyProduct = () => {
    if (!validateProduct()) return;

    if (isModifying) {
      const newProductsList = [...ProductsList];
      newProductsList[modifyingIndex] = SourcingProducts;
      setProductsList(newProductsList);

      setIsModifying(false);
      setModifyingIndex(null);

      SetSourcingProducts({
        productName: "",
        productArabicName: "",
        productLink: "",
        productImage: null,
        imageUrl: null,
        category: "",
        shippingMethod: "",
        HasVariants: false,
        isTested: false,
        ProcessingMode: "Bulk",
        Qty: 100,
        TargetCountry: 0,
        note: "",
        variants: [],
      });
    } else {
      handleAddProduct();
    }
    setStartProduct(false);
  };

  const onModalClose = () => {
    setProductsList([]);
    setIsModifying(false);
    setModifyingIndex(null);
    setMessage("");
    SetSourcingProducts({
      productName: "",
      productArabicName: "",
      productLink: "",
      productImage: null,
      imageUrl: null,
      category: "",
      shippingMethod: "",
      HasVariants: false,
      isTested: false,
      ProcessingMode: "Bulk",
      Qty: 100,
      originCountry: 4,
      TargetCountry: 0,
      note: "",
      variants: [],
    });
    setCurrentStep("origin");
    setIsFromCountryOpen(false);
    setIsToCountryOpen(false);
    setLoadingCountries(false);
    setToCountries([]);
    setFromCountries([]);
    setFromCountry("CHINA");
    setCountrFromPage(1);
    setCountrToPage(1);
    setStartProduct(false);
    setIsSubmitting(false);
    setSubmitProgress(0);
    onClose()
  };

  const handleAbandonRequest = async () => {
    if (loading) return;

    try {
      const response = await dispatch(deleteSourcingRequest(selectedItemID.id));

      if (deleteSourcingRequest.fulfilled.match(response)) {
        toast.success("Sourcing request abandoned successfully");
        await dispatch(getSourcingRequest())
        onModalClose();

      } else {
        const errorMessage =
          response.payload?.message || "Failed to abandon request";
        toast.error(errorMessage);
      }
    } catch (error) {
      toast.error(error.message || "Failed to abandon request");
    }
  };

  useEffect(() => {
    console.log(SourcingProducts.productName === "" ||
      SourcingProducts.productArabicName === "" ||
      SourcingProducts.productLink === "" ||
      SourcingProducts.category === "" ||
      SourcingProducts.shippingMethod === "" ||
      SourcingProducts.note === "" ||
      !SourcingProducts.HasVariants ||
      !SourcingProducts.isTested ||
      SourcingProducts.Qty === 100 ||
      SourcingProducts.TargetCountry === 0 ||
      SourcingProducts.productImage === null ||
      SourcingProducts.imageUrl === null);

  }, [SourcingProducts])

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onModalClose}
      headerAdditionalItem={
        selectedItemID && <Popover
          showArrow
          backdrop="blur"
          placement="bottom">
          <PopoverTrigger>
            <Button

              className="bg-transparent p-2 rounded-lg text-white bg-glb_red">
              <MessageCancel01Icon size={25} />
              Cancel Request
            </Button>
          </PopoverTrigger>
          <PopoverContent>
            {(titleProps) => (
              <div className="px-1 py-2">
                <h3 className="text-small font-bold mb-2 text-glb_red">
                  Abandon Sourcing Request
                </h3>
                <hr className="my-1 border-black/30 dark:border-white/30" />
                <h6 className="text-small font-normal my-2 w-[90%] mx-auto">
                  Are you sure you want to abandon this sourcing request?
                  <p className="text-xs text-glb_red mb-2">
                    All unsaved changes will be lost. This action cannot be undone.
                  </p>
                </h6>
                <hr className="my-1 border-black/30 dark:border-white/30" />
                <div className="flex justify-between items-center gap-2 mt-2">
                  <span size="sm" className="text-gray-500">
                    ESC
                  </span>
                  <Button isLoading={loading} size="sm" color="danger" onClick={handleAbandonRequest}>
                    Abandon
                  </Button>
                </div>
              </div>
            )}
          </PopoverContent>
        </Popover>

      }
      width="max-w-6xl"
      showHeader={true}
      title={selectedItemID ? `Modifing Sourcing Request ${selectedItemID.requestCode}` : "Create New Sourcing Request"}
      headerClassName="px-8 py-4 border-b h-[60px]"
      bodyClassName={`!py-0 !px-0   ${!startProduct ? " h-[calc(100vh-80px)] overflow-y-auto" : ""}`}
      footerClassName="!h-[80px] !p-0"
      showFooter={true}
      footerContent={
        <div className="h-full w-full sm:w-[90%] flex justify-end items-center relative gap-3">
          <div
            className={`absolute top-0  z-50 bg-transparent  ${isModifying ? "backdrop-blur-sm h-full w-full" : "h-0 w-0"
              }`}></div>
          {
            currentStep === 'products' && startProduct &&
            <div className="z-[100] w-full flex justify-end gap-2 items-center">

              <Button
                onClick={() => {
                  setIsModifying(false);
                  setModifyingIndex(null);
                  setStartProduct(false);
                  SetSourcingProducts({
                    productName: "",
                    productArabicName: "",
                    productLink: "",
                    productImage: null,
                    imageUrl: null,
                    category: "",
                    shippingMethod: "",
                    HasVariants: false,
                    isTested: false,
                    ProcessingMode: "Bulk",
                    Qty: 100,
                    TargetCountry: 0,
                    note: "",
                    variants: [],
                  });
                }}
                className="bg-transparent p-2 rounded-lg text-glb_red font-bold ">
                Cancel
              </Button>
              <Button
                onClick={handleAddOrModifyProduct}
                className="bg-transparent p-2 rounded-lg text-white bg-glb_blue">
                <PackageAddIcon size={25} />
                {isModifying ? "Modify Product" : "Add Product"}
              </Button>
            </div>
          }
          <div className="flex justify-center items-center h-full gap-2 pr-4">
            <Button
              isIconOnly
              isDisabled={currentStep === "origin"}
              className={`rounded-full border-2 border-glb_blue bg-transparent text-glb_blue`}
              color="primary"
              onClick={handleBack}>
              <ArrowLeft01Icon />
            </Button>
            {currentStep !== "validation" ? (
              <motion.div
                key="next"
                initial={{ opacity: 0, scale: 0.8, x: -20 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0.8, x: 20 }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 20,
                  duration: 0.2,
                }}>
                <Button
                  isIconOnly
                  isDisabled={isDisabled()}
                  className={`rounded-full border-2 border-glb_blue bg-transparent text-glb_blue`}
                  color="primary"
                  onClick={handleNext}>
                  <ArrowRight01Icon />
                </Button>
              </motion.div>
            ) : (
              <motion.div
                key="submit"
                initial={{ opacity: 0, scale: 0.8, x: -20 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0.8, x: 20 }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 20,
                  duration: 0.2,
                }}>
                <Button
                  isDisabled={isDisabled() || loading}
                  className={`rounded-full border-2 border-glb_blue bg-transparent text-glb_blue`}
                  color="primary"
                  onClick={handleNext}>
                  {loading ? selectedItemID ? "Modifing..." : "Submitting..." : selectedItemID ? "Modify Request" : "Submit Request"}
                </Button>
              </motion.div>
            )}
          </div>
        </div>
      }>
      <div className="w-full flex flex-col">
        <div className="sticky top-0 bg-white h-[100px] dark:bg-transparent z-50 w-full overflow-x-auto">
          <div className="w-[90%] mx-auto px-4  min-w-[600px]">
            <TimeLine steps={steps} currentStep={currentStep} />
          </div>
        </div>
        <div className="w-full my-0 ">
          {currentStep === "origin" && (
            //need this div to be scrollable on y 
            <div className="flex  flex-col justify-center item-center w-full max-w-[500px] 
             md:w-[80%] mx-auto ">
              <div className="text-center mb-8">
                <h2 className="text-lg md:text-xl xl:text-2xl font-semibold mb-4 text-glb_blue">
                  Let Us Source Products for You
                </h2>
                <p className="text-sm xl:text-md text-[#00000070] dark:text-[#FFFFFF] w-[80%] max-w-2xl mx-auto">
                  We specialize in connecting you with reliable suppliers and
                  manufacturers worldwide. Our sourcing experts will help you
                  find the best products at competitive prices, ensuring quality
                  and timely delivery. Whether you're looking for raw materials,
                  finished products, or custom manufacturing solutions, we've
                  got you covered.
                </p>
              </div>
              <div className="flex flex-col gap-2 justify-center items-center">
                <label className="block mt-3 text-sm text-glb_blue mb-1">
                  Source Origin
                </label>
                <div className=" m-auto flex flex-col gap-2 justify-center items-center rounded-xl border-2 border-glb_blue text-glb_blue p-5 w-40 h-40 hover:bg-glb_blue_opacity cursor-pointer">
                  <img
                    alt={`china`}
                    src={`https://flagcdn.com/32x24/cn.png`}
                    className={"inline w-12 rounded-sm"}
                    onError={(e) => {
                      e.target.style.display = "none";
                      e.target.onerror = null;
                    }}
                  />
                  <h2 className="font-bold">CHINA</h2>
                </div>
              </div>
            </div>
          )}
          {currentStep === "products" && (
            //need this div to be scrollable on y 
            <div className="flex flex-col justify-center item-center w-full md:w-[90%] mx-auto">
              {!startProduct ? (
                <div className="flex gap-8 mx-auto w-[80%] flex-col md:flex-row h-[calc(80vh-240px)] items-center justify-center">
                  <div className="flex-1 text-center md:text-start">
                    <h2 className="text-2xl font-semibold mb-4 text-glb_blue">
                      Product Sourcing Details
                    </h2>
                    <p className="text-[#00000070] dark:text-[#FFFFFF70]">
                      Specify the products you wish to source through our
                      platform. You can add multiple products, each with its
                      unique specifications and requirements. Our team of
                      sourcing experts will carefully analyze your needs and
                      connect you with the most suitable suppliers to ensure
                      quality, competitive pricing, and reliable delivery
                      timelines.
                    </p>
                  </div>
                  <div className="flex-1 flex flex-col items-center justify-center">
                    {ProductsList && ProductsList.length > 0 ?
                      <div className="flex flex-row md:flex-col gap-2 pb-4 overflow-y-auto h-full">
                        {
                          ProductsList.map((p, index) => {
                            return (
                              <div className="pl-3 group flex flex-col w-full justify-start items-start " key={index}>
                                <div
                                  className="cursor-pointer w-full max-w-full overflow-hidden text-ellipsis whitespace-nowrap border-2 border-glb_blue p-2 rounded-lg bg-glb_blue_opacity text-glb_blue">
                                  {p.productName}
                                </div>
                                <div className="group-hover:h-auto h-0 overflow-hidden flex w-full justify-between items-center gap-2">
                                  <div
                                    onClick={() => {
                                      const newProductsList = [...ProductsList];
                                      newProductsList.splice(index, 1);
                                      setProductsList(newProductsList);
                                    }}
                                    className="text-glb_red text-start cursor-pointer hover:font-bold text-xs bg-transparent w-fit bg-green-500 p-1">
                                    Remove
                                  </div>
                                  <div
                                    onClick={() => handleModifyProduct(p, index)}
                                    className="text-glb_green text-start cursor-pointer hover:font-bold text-xs bg-transparent w-fit bg-green-500 p-1">
                                    Edit
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        <div
                          onClick={() => setStartProduct(true)}
                          className="pt-3  gap-2 group flex w-full justify-center items-center text-glb_blue cursor-pointer hover:font-bold">
                          <PackageAddIcon size={20} />
                          <h2 className="font-bold">{isModifying ? 'Modify Products' : 'Add Products'}</h2>
                        </div>
                      </div> : <div
                        onClick={() => setStartProduct(true)}
                        className="flex flex-col gap-2 justify-center items-center rounded-xl border-2 border-glb_blue text-glb_blue p-5 w-40 h-40 hover:bg-glb_blue_opacity cursor-pointer">
                        <PackageAddIcon size={35} />
                        <h2 className="font-bold">{isModifying ? 'Modify Products' : 'Add Products'}</h2>
                      </div>}
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-[80%_20%] gap-4 h-[calc(80vh-240px)] mb-24">
                  <div ref={containerRef} className="p-4 rounded-lg overflow-y-auto custom-scrollbar order-2 md:order-1">
                    <div className="grid grid-cols-1 gap-4">
                      <div className="flex flex-col sm:flex-row w-full gap-2">
                        <UnderlinedInput
                          ref={productNameRef}
                          label="Product Name"
                          value={SourcingProducts.productName}
                          onChange={(e) =>
                            SetSourcingProducts({
                              ...SourcingProducts,
                              productName: e.target.value,
                            })
                          }
                          start={true}
                          isRequired
                        />{" "}
                        <Select
                          ref={categoryRef}
                          selectedKeys={[
                            Object.keys(category).find(
                              (key) => key === SourcingProducts.category
                            ),
                          ]}
                          isRequired
                          onChange={(e) => {
                            SetSourcingProducts({
                              ...SourcingProducts,
                              category: e.target.value,
                            });
                          }}
                          variant="underlined"
                          color="primary"
                          label="Category"
                          className="w-full"
                          classNames={{
                            label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],
                          }}>
                          {Object.entries(category).map(([key, value]) => (
                            <SelectItem key={key}>{value}</SelectItem>
                          ))}
                        </Select>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-[30%_70%] gap-1">
                        <UnderlinedInput
                          label="Arabic Name"
                          value={SourcingProducts.productArabicName}
                          onChange={(e) =>
                            SetSourcingProducts({
                              ...SourcingProducts,
                              productArabicName: e.target.value,
                            })
                          }
                          start={true}
                        />
                        <UnderlinedInput
                          ref={productLinkRef}
                          isRequired
                          label="Product URL"
                          value={SourcingProducts.productLink}
                          onChange={(e) => {
                            const url = e.target.value;
                            // Basic URL regex validation
                            const urlPattern = new RegExp(
                              '^(https?:\\/\\/)?' + // protocol
                              '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
                              '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
                              '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
                              '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
                              '(\\#[-a-z\\d_]*)?$', 'i'
                            );

                            // Update the state anyway to show what user typed
                            SetSourcingProducts({
                              ...SourcingProducts,
                              productLink: url,
                              isProductLinkValid: urlPattern.test(url),
                            });
                          }}
                          onBlur={(e) => {
                            if (!SourcingProducts.isProductLinkValid) {
                              e.target.classList.add("error");
                            } else {
                              e.target.classList.remove("error");
                            }
                          }}
                          errorMessage={!SourcingProducts.isProductLinkValid ? "Please enter a valid URL" : ""}
                          isInvalid={!SourcingProducts.isProductLinkValid}
                          start={true}
                        />
                      </div>
                      <div className="flex flex-col justify-start items-start w-full gap-2">
                        <Checkbox
                          ref={variantsRef}
                          isSelected={SourcingProducts.HasVariants}
                          onValueChange={(val) =>
                            SetSourcingProducts({
                              ...SourcingProducts,
                              HasVariants: val,
                            })
                          }>
                          Has Variants ?
                        </Checkbox>
                        {SourcingProducts.HasVariants && (
                          <div className="flex w-full">
                            <div className="overflow-x-auto flex-grow min-h-36 bg-[#00000005] dark:bg-[#ffffff05] rounded-xl">
                              {" "}
                              {SourcingProducts.variants.length === 0 ? (
                                <div className="flex justify-center items-center w-full h-full">
                                  <button
                                    onClick={() => {
                                      SetSourcingProducts({
                                        ...SourcingProducts,
                                        variants: [
                                          ...SourcingProducts.variants,
                                          { name: "", value: "" },
                                        ],
                                      });
                                    }}
                                    className="flex justify-center items-center gap-3 text-white bg-glb_blue p-2 rounded-lg  text-sm transition-colors">
                                    Add new variants
                                    <PackageAddIcon size={15} />
                                  </button>
                                </div>
                              ) : (
                                <table className="box-border border-collapse overflow-auto min-w-[400px] w-full table-fixed">
                                  <thead>
                                    <tr className="border-b border-gray-300 h-11 dark:border-gray-600">
                                      <th className="dark:bg-[#ffffff05] whitespace-nowrap  text-center px-4 py-2 text-[#00000060] dark:text-[#ffffff60] text-xs sm:text-sm md:text-base font-medium w-[45%]">
                                        Name
                                      </th>
                                      <th className="dark:bg-[#ffffff05] whitespace-nowrap text-center px-4 py-2 text-[#00000060] dark:text-[#ffffff60] text-xs sm:text-sm md:text-base font-medium w-[45%]">
                                        Value
                                      </th>
                                      <th className="dark:bg-[#ffffff05] whitespace-nowrap text-center py-2 text-[#00000060] dark:text-[#ffffff60] text-xs sm:text-sm md:text-base font-medium w-[40px]">
                                        Delete
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <>
                                      {SourcingProducts.variants.map(
                                        (variant, index) => (
                                          <tr key={index} className="">
                                            <td className="px-2 py-2 w-[45%] text-xs sm:text-sm md:text-base">
                                              <UnderlinedInput
                                                value={
                                                  SourcingProducts.productName
                                                    ? `${SourcingProducts.productName.replace(
                                                      /\s+/g,
                                                      "-"
                                                    )}-${variant.value}`
                                                    : ""
                                                }
                                                isDisabled={true}
                                                placeholder="Variant Name"
                                              />
                                            </td>
                                            <td className="px-2 py-2 w-[45%] text-xs sm:text-sm md:text-base">
                                              <UnderlinedInput
                                                value={variant.value}
                                                autoFocus
                                                onChange={(e) => {
                                                  const newVariants = [
                                                    ...SourcingProducts.variants,
                                                  ];
                                                  newVariants[index].value =
                                                    e.target.value;
                                                  newVariants[index].name =
                                                    SourcingProducts.productName
                                                      ? `${SourcingProducts.productName.replace(
                                                        /\s+/g,
                                                        "-"
                                                      )}-${e.target.value}`
                                                      : "";
                                                  SetSourcingProducts({
                                                    ...SourcingProducts,
                                                    variants: newVariants,
                                                  });
                                                }}
                                                placeholder="Variant Value"
                                              />
                                            </td>
                                            <td className="py-2 text-center w-[40px]">
                                              <Button
                                                isIconOnly
                                                variant="light"
                                                color="danger"
                                                onClick={() => {
                                                  const newVariants = [
                                                    ...SourcingProducts.variants,
                                                  ];
                                                  newVariants.splice(index, 1);
                                                  SetSourcingProducts({
                                                    ...SourcingProducts,
                                                    variants: newVariants,
                                                  });
                                                }}>
                                                <Delete02Icon size={20} />
                                              </Button>
                                            </td>
                                          </tr>
                                        )
                                      )}
                                      <tr>
                                        <td
                                          colSpan="3"
                                          className="text-center py-4">
                                          <button
                                            onClick={() => {
                                              SetSourcingProducts({
                                                ...SourcingProducts,
                                                variants: [
                                                  ...SourcingProducts.variants,
                                                  { name: "", value: "" },
                                                ],
                                              });
                                            }}
                                            className="text-glb_blue hover:text-glb_blue_opacity text-sm transition-colors">
                                            Add new variants
                                          </button>
                                        </td>
                                      </tr>
                                    </>
                                  </tbody>
                                </table>
                              )}
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="w-full grid grid-cols-1 gap-4">
                        {/* Image uploader with preview */}
                        <label
                          ref={productImageRef}
                          className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800">
                          {SourcingProducts.imageUrl ? (
                            <>
                              <div className="relative w-full h-full">
                                <img
                                  src={SourcingProducts.imageUrl}
                                  alt="Uploaded Preview"
                                  className="h-full w-full object-cover rounded-lg"
                                />
                                <button
                                  onClick={(e) => {
                                    e.preventDefault();
                                    SetSourcingProducts({
                                      ...SourcingProducts,
                                      productImage: null,
                                      imageUrl: null
                                    });
                                  }}
                                  className="absolute top-2 right-2 text-white rounded-full p-1 hover:text-glb_red">
                                  <CancelCircleIcon />
                                </button>
                              </div>
                            </>
                          ) : (
                            <div className="flex flex-col items-center justify-center pt-5 pb-6 m-8">
                              <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                <span className="font-semibold">
                                  Product Image
                                </span>
                              </p>
                              <CloudUploadIcon className="w-10 h-10 text-gray-400" />
                              <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                <span className="font-semibold">
                                  Click to upload
                                </span>{" "}
                                or drag and drop
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                PNG or JPG (max 10MB)
                              </p>
                            </div>
                          )}
                          <input
                            type="file"
                            accept=".png, .jpg, .jpeg"
                            className="hidden"
                            onChange={(e) => {
                              const file = e.target.files[0];
                              if (file && file.size <= 10 * 1024 * 1024) {
                                SetSourcingProducts({
                                  ...SourcingProducts,
                                  productImage: file,
                                  imageUrl: URL.createObjectURL(file), // Store the image URL
                                });
                              } else {
                                alert(
                                  "File must be PNG or JPG and less than 10MB."
                                );
                              }
                            }}
                          />
                        </label>
                        {SourcingProducts.productImage && (
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {SourcingProducts.productImage.name}
                          </span>
                        )}
                      </div>

                      <div className="flex w-full flex-col sm:flex-row justify-start items-start gap-2">
                        <RadioGroup
                          ref={processingModeRef}
                          className="mx-auto"
                          value={SourcingProducts.ProcessingMode}
                          onValueChange={(val) => {
                            SetSourcingProducts({
                              ...SourcingProducts,
                              ProcessingMode: val,
                              Qty: val === "Bulk" ? 100 : 30,
                            });
                          }}
                          description="Select how you want to process your sourcing request"
                          label="Process Mode">
                          {Object.entries(processingMode).map(
                            ([key, value]) => (
                              <CustomRadio
                                description={value}
                                value={key}
                                key={key}>
                                {key}
                              </CustomRadio>
                            )
                          )}
                        </RadioGroup>
                        {SourcingProducts.ProcessingMode === "Bulk" ? (
                          <motion.div
                            key="bulk"
                            className="ml-2 mt-4 flex-1 w-full"
                            initial={{ opacity: 0, scale: 0.8, x: -20 }}
                            animate={{ opacity: 1, scale: 1, x: 0 }}
                            exit={{ opacity: 0, scale: 0.8, x: 20 }}
                            transition={{
                              type: "spring",
                              stiffness: 300,
                              damping: 20,
                              duration: 0.1,
                              mass: 1,
                            }}>
                            <label className="block mt-3 text-sm text-[#00000050] dark:text-[#FFFFFF30] mb-1">
                              Destination Country
                            </label>
                            <CountrySelector
                              open={isToCountryOpen}
                              useAll={false}
                              onToggle={() => {
                                if (isToCountryOpen) {
                                  setCountrToPage(1);
                                }
                                setIsToCountryOpen(!isToCountryOpen);
                              }}
                              COUNTRIES={toCountries}
                              loading={loadingCountries}
                              id="toCountry"
                              onChange={(id) => {
                                const selectedCountry = toCountries.find(
                                  (option) => option.id === id
                                );
                                SetSourcingProducts({
                                  ...SourcingProducts,
                                  TargetCountry: selectedCountry?.id || null,
                                });
                              }}
                              defaultSelectedKeys={toCountries ? toCountries.find(
                                (option) => option.id === 0
                              ) : null}
                              selectedValue={
                                SourcingProducts.TargetCountry
                                  ? toCountries.find(
                                    (option) =>
                                      option.id ===
                                      SourcingProducts.TargetCountry
                                  )
                                  : null
                              }
                              required
                              onEndScroll={() =>
                                CustomfetchCountries(
                                  "destination",
                                  CountrToPage + 1
                                )
                              }
                            />
                            <UnderlinedInput
                              label="Quantity (Min 100)"
                              value={SourcingProducts.Qty}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (/^\d*$/.test(value)) {
                                  SetSourcingProducts({
                                    ...SourcingProducts,
                                    Qty: value ? parseInt(value) : "",
                                  });
                                }
                              }}
                              onBlur={(e) => {
                                const value = parseInt(e.target.value);
                                if (value < 100) {
                                  e.target.classList.add("error");
                                } else {
                                  e.target.classList.remove("error");
                                }
                              }}
                              errorMessage={
                                SourcingProducts.Qty < 100
                                  ? "Minimum quantity is 100"
                                  : ""
                              }
                              isInvalid={SourcingProducts.Qty < 100}
                              isRequired
                              start={true}
                            />
                          </motion.div>
                        ) : (
                          <motion.div
                            key="one-by-one"
                            className="ml-2 mt-4 flex-1 w-full"
                            initial={{ opacity: 0, scale: 0.8, x: -20 }}
                            animate={{ opacity: 1, scale: 1, x: 0 }}
                            exit={{ opacity: 0, scale: 0.8, x: 20 }}
                            transition={{
                              type: "spring",
                              duration: 0.1,
                              stiffness: 300,
                              damping: 20,
                              mass: 1,
                            }}>
                            <UnderlinedInput
                              label="Quantity (Min 30)"
                              value={SourcingProducts.Qty}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (/^\d*$/.test(value)) {
                                  SetSourcingProducts({
                                    ...SourcingProducts,
                                    Qty: value ? parseInt(value) : "",
                                  });
                                }
                              }}
                              onBlur={(e) => {
                                const value = parseInt(e.target.value);
                                if (value < 30) {
                                  e.target.classList.add("error");
                                } else {
                                  e.target.classList.remove("error");
                                }
                              }}
                              errorMessage={
                                SourcingProducts.Qty < 30
                                  ? "Minimum quantity is 30"
                                  : ""
                              }
                              isInvalid={SourcingProducts.Qty < 30}
                              isRequired
                              start={true}
                            />
                          </motion.div>
                        )}
                      </div>
                      <div className="flex w-full justify-between items-end gap-2">
                        <RadioGroup
                          ref={shippingMethodRef}
                          value={SourcingProducts.shippingMethod}
                          onValueChange={(val) => {
                            SetSourcingProducts({
                              ...SourcingProducts,
                              shippingMethod: val,
                            });
                          }}
                          label="Shipping Method"
                          orientation="horizontal">
                          {Object.entries(ShippingMethods).map(([key, value]) => (
                            <Radio value={key} key={key}>
                              {value}
                            </Radio>
                          ))}
                        </RadioGroup>
                        <Checkbox
                          isSelected={SourcingProducts.isTested}
                          onValueChange={(val) =>
                            SetSourcingProducts({
                              ...SourcingProducts,
                              isTested: val,
                            })
                          }>
                          Is Tested
                        </Checkbox>
                      </div>
                      <UnderlinedInput
                        label="Note"
                        maxCharacters={500}
                        value={SourcingProducts.note}
                        onChange={(e) =>
                          SetSourcingProducts({
                            ...SourcingProducts,
                            note: e.target.value,
                          })
                        }
                        start={true}
                      />

                    </div>
                  </div>

                  <div
                    className={`border-b-2 md:border-b-0 md:border-l-2 border-[#00000010] dark:border-[#ffffff10] p-0 md:p-4 flex flex-col justify-between sticky top-0  h-[80px] md:h-[calc(80vh-240px)] order-1 md:order-2`}>
                    <div
                      className={`absolute top-0  z-50 bg-transparent  ${isModifying ? "backdrop-blur-sm h-full w-full" : "h-0 w-0"
                        }`}></div>

                    <div className="flex flex-row md:flex-col gap-2 pb-4 overflow-y-auto h-full">
                      {ProductsList.length > 0 ? (
                        ProductsList.map((p, index) => {
                          return (
                            <div className="pl-3 group flex flex-col w-full justify-start items-startbg-red-500" key={index}>
                              <div
                                className="cursor-pointer w-full max-w-full overflow-hidden text-ellipsis whitespace-nowrap border-2 border-glb_blue p-2 rounded-lg bg-glb_blue_opacity text-glb_blue">
                                {p.productName}
                              </div>
                              <div className="group-hover:h-auto h-0 overflow-hidden flex w-full justify-between items-center gap-2">
                                <div
                                  onClick={() => {
                                    const newProductsList = [...ProductsList];
                                    newProductsList.splice(index, 1);
                                    setProductsList(newProductsList);
                                  }}
                                  className="text-glb_red text-start cursor-pointer hover:font-bold text-xs bg-transparent w-fit bg-green-500 p-1">
                                  Remove
                                </div>
                                <div
                                  onClick={() => handleModifyProduct(p, index)}
                                  className="text-glb_green text-start cursor-pointer hover:font-bold text-xs bg-transparent w-fit bg-green-500 p-1">
                                  Edit
                                </div>
                              </div>
                            </div>
                          );
                        })
                      ) : (
                        <div className="flex flex-col items-center justify-center p-4 text-[#00000050] dark:text-[#FFFFFF50]">
                          <PackageAddIcon size={24} className="mb-2" />
                          <span className="text-sm text-center">
                            No products added
                          </span>
                          <span className="text-xs text-center">
                            Use the form in left to insert products to your
                            sourcing request
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          {currentStep === "validation" && (
            //need this div to be scrollable on y 
            <div className="flex relative flex-col justify-center  item-center w-full md:w-[90%] mx-auto">
              {isSubmitting && (
                <div className="fixed inset-0 flex items-center justify-center z-50 bg-white/70 backdrop-blur-sm">
                  <div className="w-full max-w-md mx-auto flex flex-col items-center">
                    <Progress value={submitProgress} color="primary" className="w-full" />
                    <div className="text-center mt-4 text-glb_blue font-semibold text-lg">
                      {selectedItemID && isDeleting
                        ? "Canceling Sourcing Request..."
                        : selectedItemID && !isDeleting
                          ? "Modifying Sourcing Request..."
                          : "Sending your request..."}
                    </div>
                  </div>
                </div>
              )}
              <div className="flex flex-col gap-8 mx-auto w-[80%] h-[calc(80vh-240px)] items-center justify-center overflow-y-auto custom-scrollbar">
                <div className="flex-1 text-center">
                  <h2 className="text-2xl font-semibold mb-4 text-glb_blue">
                    Leave a Message for Our Team
                  </h2>
                  <p className="text-[#00000070] dark:text-[#FFFFFF70] mb-8">
                    Please provide any additional information or special
                    requirements that our sourcing team should know about. This
                    will help us better understand your needs and provide the
                    best possible service.
                  </p>

                  <div className="w-full max-w-2xl mx-auto">
                    <UnderlinedTextAreaInput
                      label="Message"
                      autoFocus
                      maxCharacters={1000}
                      defaultValue={message}
                      value={message}
                      onValueChange={setMessage}
                      start={true}
                    />
                  </div>
                  <div className="mt-8 p-4 bg-[#00000005] dark:bg-[#ffffff05] rounded-lg w-full max-w-2xl mx-auto">
                    <p className="text-[#00000070] dark:text-[#FFFFFF70] text-sm">
                      <span className="font-semibold text-glb_blue">
                        Important:
                      </span>{" "}
                      Please check your request status periodically in the
                      "Sourcing Requests" section. Our team will review your
                      request and contact you within 24-48 hours. You can track
                      the progress and communicate with our team through the
                      request details page.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </CustomModal>
  );
};

export default CreateNewSourcingRequest;
