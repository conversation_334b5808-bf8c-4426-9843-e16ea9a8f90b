import { Button } from "@heroui/button";
import CustomModal from "@shared/components/CustomModal.jsx";

import codPowerGroupLogo from "@shared/assets/images/cod-power-group-logo.svg";
import codPowerGroupLogoDark from "@shared/assets/images/cod-power-group-logo-dark.svg";
import dropifyLogo from "@shared/assets/images/dropify-logo.svg";
import { useThemeProvider } from "../../../core/providers/ThemeContext";
import { Link02Icon } from "hugeicons-react";
import { useNavigate } from "react-router-dom";
import { RouteNames } from "../../../core/routes/routes";
import { fetchDropifyData, installDropifyData } from "../../../core/redux/slices/leadSource/dropifySlice";
import { Spinner } from "@heroui/react";
import { useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const ConnectToModal = ({ isOpen, setIsOpen, clientId, redirectUri }) => {
  const { currentTheme } = useThemeProvider();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [checking, setChecking] = useState(false);
  const [showError, setShowError] = useState(false);
  const onClose = () => {
    setIsOpen(false);
  };

  const HandleInstall = async (clientId, redirectUri) => {
    const res = await dispatch(installDropifyData({
      clientId,
      redirectUri
    }));
    // Check if the action was fulfilled
    if (installDropifyData.fulfilled.match(res)) {

      console.log(res);
      window.open(res.payload.result.redirect_uri, '_blank');
      onClose();
      toast.success("Dropify integration installed successfully!");
    } else {
      setChecking(false);
      console.error("Error fetching Dropify data:", res.payload);
      toast.error("Failed to install Dropify integration. Please try again.");
    }
  };

  const checkDropify = async (clientId, redirectUri) => {
    if (!isOpen) return; // Prevent multiple checks if already checking
    const res = await dispatch(fetchDropifyData({
      clientId,
      redirectUri
    }));

    // Check if the action was fulfilled
    if (fetchDropifyData.fulfilled.match(res)) {
      setChecking(false);
      return true;
    } else {
      setChecking(false);
      console.error("Error fetching Dropify data:", res.payload);
      return false;
    }
  };

  // Check if both parameters exist
  const redirectUser = async () => {
    if (!isOpen) return; // Prevent multiple checks if already checking
    if (clientId && redirectUri) {
      // Call the checkDropify function and wait for the result
      const res = await checkDropify(clientId, redirectUri);
      if (!res) {
        toast.error("Failed to connect to Dropify. Please try again.");
        setShowError(true);
      } else {
        // No navigation; just show the modal
        setShowError(false);
      }
    } else {
      // If the parameters are missing, show error message and disable Install button
      setShowError(true);
    }
  };

  useEffect(() => {
    if (checking) return;
    redirectUser();
  }, []);

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      showHeader={false}
      height='h-fitt'
      bodyClassName="p-8"
      footerContent={!checking && (
        <div className="flex justify-center items-center gap-2 w-full">
          <Button
            variant="light"
            size="sm"
            className=" px-8 py-2 rounded-lg bg-glb_red text-white flex flex-row items-center justify-center gap-1"
            onClick={() => onClose()}
          >
            Cancel
          </Button>
          <Button
            isLoading={checking}
            variant="light"
            size="sm"
            className={`${showError ? 'bg-gray-300 text-gray-500 dark:bg-gray-600 dark:text-gray-400  cursor-not-allowed' : 'bg-glb_blue text-white'} px-8 py-2 rounded-lg  flex flex-row items-center justify-center gap-1`}
            onClick={() => HandleInstall(clientId, redirectUri)}
            disabled={showError}
          >
            Install
          </Button>
        </div>
      )}
    >
      <div className="flex flex-col justify-center items-center w-full h-full py-4">
        <div className="mx-auto flex justify-evenly items-center gap-2 w-[80%]">
          {currentTheme === "light" ? (
            <img
              src={codPowerGroupLogo}
              alt="power group world logo"
              className="w-20"
            />
          ) : (
            <img
              src={codPowerGroupLogoDark}
              alt="power group world logo"
              className="w-20"
            />
          )}
          <Link02Icon size={30} />
          <img
            src={dropifyLogo}
            alt="dropify logo"
            className="w-20"
          />
        </div>
        <div className="mx-auto flex justify-evenly items-center mt-4 gap-2 w-full h-[1px] opacity-15 bg-black dark:bg-white">
        </div>
        {checking ? (
          <div className="flex justify-center items-center w-full">
            <Spinner size="lg" />
          </div>
        ) : (
          <>
            <div className="mx-auto flex justify-evenly items-center mt-4 gap-2 w-[80%]">
              <h1 className="text-center text-glb_red font-black text-base">
                Authorize Dropify Integration
              </h1>
            </div>
            <div className="mx-auto flex justify-evenly items-center gap-2 w-[80%]">
              <h6 className="text-center text-base opacity-80">
                Dropify has requested access to connect with your account. By authorizing this, you'll enable Dropify to seamlessly sync orders with Power Group. Please grant authorization to proceed.
              </h6>
            </div>
            {showError && (
              <div className="mx-auto mt-4 w-[80%]">
                <h6 className="text-center text-glb_red font-semibold text-base">
                  Error: Missing or invalid parameters. Please ensure the client ID and redirect URI are correct.
                </h6>
              </div>
            )}
          </>
        )}
      </div>
    </CustomModal>
  );
};

export default ConnectToModal;
