import CustomModal from "@shared/components/CustomModal.jsx";
import { <PERSON><PERSON> } from "@heroui/button";
import UnderlinedInput from "@/modules/settings/components/UnderlinedInput.jsx";
import redTrophy from "@shared/assets/images/settings/redCrown.svg";
import mastercard from "@shared/assets/images/settings/masterCard.svg";
import visa from "@shared/assets/images/settings/Visa.svg";
import paypal from "@shared/assets/images/settings/Paypal.svg";
import gpay from "@shared/assets/images/settings/GPay.svg";
import apay from "@shared/assets/images/settings/APay.svg";
import gpaylight from "@shared/assets/images/settings/GPayLight.svg";
import apaylight from "@shared/assets/images/settings/APayLight.svg";
import americanExpress from "@shared/assets/images/settings/AmericanExpress.svg";
import stripe from "@shared/assets/images/settings/Stripe.svg";
import checkCircle from "@shared/assets/images/settings/CheckCircle.svg";
import checkbadge from "@shared/assets/images/settings/badge-check.svg";
import { CheckmarkCircle01Icon, CheckmarkCircle02Icon } from "hugeicons-react";
import { useState } from "react";
import { motion } from "framer-motion";
import { Tab, Tabs } from "@heroui/tabs";
import { Input } from "@heroui/input";
import { useThemeProvider } from "../../../core/providers/ThemeContext";
import { CardNumberInput, CVVInput, ExpiryInput } from "../../shared/components/PaymentInputs";

export default function UpgradePlanModal({ isOpen, onClose }) {

    const { currentTheme } = useThemeProvider();
    const [cardNumber, setCardNumber] = useState('');
    const [expiry, setExpiry] = useState('');
    const [cvv, setCVV] = useState('');
    const methodIcons = [
        mastercard, visa, paypal, currentTheme === 'light' ? gpaylight : gpay, currentTheme === 'light' ? apaylight : apay, americanExpress, stripe
    ]
    const [selectedMethod, setSelectedMethod] = useState(0)
    const [option, setOption] = useState('Monthly plan')
    const proPlan = {
        name: "Pro",
        description: "Detailed plan is here",
        price: 69.0,
        billingCycle: "1 User / Monthly",
        features: [
            "All features of the COD plan",
            "Access to our Affiliate Marketing platform for expanding market reach",
            "Personalized customer support for both COD and affiliate transactions",
            "Advanced analytics and reporting tools for tracking affiliate sales",
            "Customizable affiliate commission rates to suit your business goals",
        ],
        paymentOptions: ["Monthly plan", "Annual plan"],
    };

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={onClose}
            width="max-w-6xl"
            showHeader={true}
            title="Upgrade your plan"
            headerClassName="px-8 py-4 border-b"
            bodyClassName="p-8"
            showFooter={false}
        >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-19 lg:gap-32 my-10 px-4 lg:px-10">
                <div className="flex flex-col gap-2 justify-start items-start">
                    <img src={redTrophy} alt='Upgrade Plan' />
                    <h1 className="text-xl font-medium w-full">Upgrade your plan !</h1>
                    <h3 className="text-base font-medium opacity-50 w-full text-wrap">Connect using your email address, and get directly started to our platforms</h3>
                    <div className="flex flex-col gap-8 mt-3 px-4 lg:px-6 py-6 w-full flex-grow rounded-3xl bg-[#00000005] dark:bg-[#ffffff05] border-1 border-[#00000020] dark:border-[#ECECEC05]">
                        <div className="flex  w-full justify-between items-center">
                            <div className="flex flex-col justify-center items-start">
                                <span className="font-semibold text-lg lg:text-xl">{proPlan.name}</span>
                                <span className="text-xs lg:text-sm font-normal">{proPlan.description}</span>

                            </div>
                            <div className="flex flex-col justify-center items-end">
                                <span className="font-semibold text-lg lg:text-xl">{` $${(proPlan.price * (option === 'Monthly plan' ? 1 : 11)).toFixed(1)}`}</span>
                                <span className="text-xs lg:text-sm font-normal">{option === 'Monthly plan' ? "1 User / Monthly" : "1 User / Annually"}</span>

                            </div>
                        </div>
                        <div className="flex w-full flex-col justify-center items-start mx-3 my-5 gap-4">
                            {
                                proPlan.features.map((i, idx) => (
                                    <div key={idx} className="flex justify-start items-center gap-3 lg:w-[80%]">
                                        <img src={checkCircle} className="w-5" />
                                        <span variant="solid" type="sharp" className="text-xs lg:text-base font-medium flex-1">{i}</span>
                                    </div>
                                ))
                            }
                        </div>

                        <div className="flex justify-center items-center w-full my-5">

                            <Tabs radius='full' aria-label="Tabs radius" color="#0587FF"

                                selectedKey={option}
                                onSelectionChange={setOption}
                                classNames={{
                                    tabList: 'border border-[#0587FF] bg-glb_blue/10 p-2',
                                    cursor: 'bg-[#0587FF] ',

                                }} >
                                {proPlan.paymentOptions.map((i) => (
                                    <Tab key={i} title={i} />
                                ))}
                            </Tabs>


                        </div>

                    </div>
                </div>
                <div className="flex mt-10 flex-col gap-2 justify-start items-start">
                    <h1 className="text-xl font-medium w-full">Payment method !</h1>
                    <h3 className="text-base font-medium opacity-30 w-full text-wrap">Connect using your email address, and get directly started to our platforms</h3>
                    <div className="flex my-5 w-full justify-start items-center gap-[3px] lg:gap-2">
                        {methodIcons.map((i, idx) => (
                            <div key={idx} onClick={() => setSelectedMethod(idx)} className={`border ${selectedMethod === idx ? ' border-[#0587FF] bg-glb_blue/10' : 'border-black dark:border-white bg-transparent'} cursor-pointer flex justify-center items-center p-[2px] lg:p-2 w-14 h-8 lg:w-16 lg:h-11 rounded-md`}>
                                <img src={i} />
                            </div>
                        ))}
                    </div>
                    <Input
                        label="Card Holder"
                        placeholder="Enter card holder name"
                        className="w-full"
                        classNames={{
                            inputWrapper: `rounded-md border border-black/5 dark:border-white/5`
                        }}
                    />

                    <CardNumberInput
                        value={cardNumber}
                        onChange={setCardNumber}
                    />
                    <div className=" my-1 flex w-full justify-between items-center gap-2">
                        <div className="flex flex-col justify-center items-start w-[60%]">
                            <span className="font-medium text-sm">Expiry Date</span>
                            <span className="text-xs font-normal">Enter the expiration date of your card</span>
                        </div>
                        <div className="flex flex-col justify-center items-start w-[40%]">
                            <ExpiryInput
                                value={expiry}
                                onChange={setExpiry}
                            />
                        </div>

                    </div>
                    <div className=" my-1 flex w-full justify-between items-center gap-2">
                        <div className="flex flex-col justify-center items-start w-[60%]">
                            <span className="font-medium text-sm">CVV Number</span>
                            <span className="text-xs font-normal">Enter the 3 numbers back of your card</span>
                        </div>
                        <div className="flex flex-col justify-center items-start w-[40%]">
                            <CVVInput
                                value={cvv}
                                onChange={setCVV}
                            />
                        </div>

                    </div>
                    <div className=" my-1 flex w-full justify-between items-center gap-2">
                        <div className="flex flex-col justify-center items-start w-[60%]">
                            <span className="font-medium text-sm">Total to be paid.</span>
                            <span className="text-xs font-normal">This price is included the VAT</span>
                        </div>
                        <div className="flex flex-col justify-center items-end mr-4">
                            <span className="font-semibold text-lg lg:text-xl">{` $${(proPlan.price * (option === 'Monthly plan' ? 1 : 11)).toFixed(1)}`}</span>
                            <span className="text-xs text-end lg:text-sm font-normal ">{option === 'Monthly plan' ? "1 User / Monthly" : "1 User / Annually"}</span>

                        </div>

                    </div>
                    <Button variant="solid" className="bg-glb_red text-white font-semibold w-full rounded-lg mt-5">
                        Pay Now
                    </Button>
                    <span className=" my-10 text-xs text-center opacity-65  w-[80%] mx-auto ">
                        By clicking on Pay Now Button, you’re agreed all terms and conditions & Privacy data of our platform and website.
                    </span>
                </div>
            </div>

        </CustomModal>
    );
};