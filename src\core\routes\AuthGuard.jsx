import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RouteNames } from './routes';
import { usePermissions } from '../providers/PermissionContext';

const AuthGuard = ({ children, requiresAuth, requiredPermission }) => {
    const isAuthenticated = useSelector((state) => state.auth.isAuthenticated);
    const location = useLocation();
    const { permissions, hasPermission } = usePermissions();

    // Define onboarding routes
    const onboardingRoutes = [
        RouteNames.login,
        RouteNames.signup,
        RouteNames.forgotPassword,
        RouteNames.changePassword,
        RouteNames.recoverEmailSent,
    ];

    const isOnboardingPage = onboardingRoutes.includes(location.pathname);


    // Define user permissions (replace this with actual logic to fetch user permissions)
    const userPermissions = ["some.permission"]; // Example permissions array



    // If the user is not authenticated and requires auth, redirect to login
    if (!isAuthenticated && requiresAuth) {
        return <Navigate to={RouteNames.login} state={{ from: location }} replace />;
    }

    // If the user is authenticated but on an onboarding page, redirect to the dashboard
    if (isAuthenticated && isOnboardingPage) {
        const previousLocation = location.state?.from || RouteNames.dashboard;
        return <Navigate to={previousLocation} replace />;
    }
    // If permission is required and the user doesn't have it, show 404 page
    if (requiredPermission && !hasPermission(requiredPermission)) {

        return <Navigate to="/404" replace />;
    }

    return children;
};

export default AuthGuard;
