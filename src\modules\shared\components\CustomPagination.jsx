// CustomPagination.jsx
import {But<PERSON>} from "@heroui/button";
import {Pagination} from "@heroui/pagination";
import {ArrowLeft02Icon, ArrowRight02Icon} from "hugeicons-react";
import { useEffect, useState } from 'react';

const CustomPagination = ({
                              total,
                              currentPage = 1,
                              onChange,
                              rowsPerPage = 10,
                              showControls = true,
                              classNames = {}
                          }) => {
    // Initialize with the current page
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
    }, []);

    const pages = Math.ceil(total / rowsPerPage);
    const validCurrentPage = Math.max(1, Math.min(currentPage, pages));

    const defaultClassNames = {
        wrapper: "flex flex-row gap-2 items-center w-fit",
        item: "rounded dark:bg-white/5 bg-black/5 mx-0.5 w-10 h-10",
        cursor: `rounded !bg-info dark:!bg-info !text-white w-10 h-10`,
        ...classNames
    };

    if (!mounted) {
        return null; // Return null on first render
    }

    return (
        <div className={defaultClassNames.wrapper}>
            {showControls && (
                <Button
                    variant="flat"
                    color="default"
                    className="rounded text-black dark:text-white dark:bg-white/5"
                    isDisabled={validCurrentPage === 1}
                    onPress={() => onChange(validCurrentPage > 1 ? validCurrentPage - 1 : validCurrentPage)}
                >
                    <ArrowLeft02Icon size={18}/> Previous
                </Button>
            )}

            <Pagination
                key={mounted ? 'mounted' : 'unmounted'}
                total={pages}
                color="primary"
                page={validCurrentPage}
                defaultPage={validCurrentPage}
                className="z-0"
                classNames={{
                    item: defaultClassNames.item,
                    cursor: defaultClassNames.cursor,
                }}
                onChange={onChange}
            />

            {showControls && (
                <Button
                    variant="flat"
                    color="default"
                    className="rounded text-black dark:text-white dark:bg-white/5"
                    isDisabled={validCurrentPage === pages}
                    onPress={() => onChange(validCurrentPage < pages ? validCurrentPage + 1 : validCurrentPage)}
                >
                    Next <ArrowRight02Icon size={18}/>
                </Button>
            )}
        </div>
    );
};

export default CustomPagination;