import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { dropifyCheckUrl, installDropifyUrl } from '../URLs';
import { getToken } from '../../../services/TokenHandler';

// Async thunk to call Dropify API
export const fetchDropifyData = createAsyncThunk(
    'dropify/fetchData',
    async ({ clientId, redirectUri }, { rejectWithValue }) => {
        console.log('fetchDropifyData', clientId, redirectUri);

        try {
            const response = await axios.get(`${dropifyCheckUrl}?client_id=${clientId}&redirect_uri=${redirectUri}`,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue(response.data.message || 'Failed to add Dropify lead source');
            }
            return response.data;
        } catch (error) {
            console.error("Error in fetchDropifyData:", error);

            return rejectWithValue(error.response?.data?.message || error.message);
        }
    }
);
export const installDropifyData = createAsyncThunk(
    'dropify/installDropifyData',
    async ({ clientId, redirectUri }, { rejectWithValue }) => {
        try {
            const encodedClientId = btoa(clientId);
            const encodedRedirectUri = btoa(redirectUri);
            const response = await axios.get(`${installDropifyUrl}?client_id=${encodedClientId}&redirect_uri=${encodedRedirectUri}`,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue(response.data.message || 'Failed to add Dropify lead source');
            }
            return response.data;
        } catch (error) {
            console.error("Error in installDropifyData:", error);

            return rejectWithValue(error.response?.data?.message || error.message);
        }
    }
);

const dropifySlice = createSlice({
    name: 'dropify',
    initialState: {
        loading: false,
        error: null,
    },
    reducers: {
        // ...add any synchronous reducers if needed...
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchDropifyData.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchDropifyData.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(fetchDropifyData.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(installDropifyData.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(installDropifyData.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(installDropifyData.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            });
    },
});

export default dropifySlice.reducer;
