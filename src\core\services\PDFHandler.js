import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import moment from "moment";
import { logopngbase64 } from "../constants/logoCompany";
import { gothamFont } from "../../modules/shared/assets/fonts/GothamBold-bold";
import { gothamMedium } from "../../modules/shared/assets/fonts/GothamMedium-normal";
import { thinFont } from "../../modules/shared/assets/fonts/GothamLight-italic";
import { formatNumber } from "../utils/functions";

var thisInvoiceData = {
  invoiceNum: '',
  customerName: '',
  customerAddress: '',
  postalCode: '',
  city: '',
  country: '',
  vat: '',
}
export const generateInvoicePDF = (invoiceData) => {
  thisInvoiceData.invoiceNum = invoiceData.invoiceNum
  thisInvoiceData.customerName = invoiceData.customerName;
  thisInvoiceData.customerAddress = invoiceData.customerAddress;
  thisInvoiceData.postalCode = invoiceData.postalCode;
  thisInvoiceData.city = invoiceData.city;
  thisInvoiceData.country = invoiceData.country;
  thisInvoiceData.vat = invoiceData.vat;
  return new Promise((resolve, reject) => {
    try {
      var callAddFont = function () {
        this.addFileToVFS('GothamBold-bold.ttf', gothamFont);
        this.addFont('GothamBold-bold.ttf', 'GothamBold', 'bold');
        this.addFileToVFS('GothamMedium-normal.ttf', gothamMedium);
        this.addFont('GothamMedium-normal.ttf', 'GothamMedium', 'normal');
        this.addFileToVFS('GothamLight-italic.ttf', thinFont);
        this.addFont('GothamLight-italic.ttf', 'GothamLight', 'italic');

      };
      jsPDF.API.events.push(['addFonts', callAddFont])



      // Initialize PDF document
      const doc = new jsPDF();

      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;
      const margin = 15;

      // Add company logo and header
      addHeader(doc);

      // Add invoice details
      addInvoiceDetails(doc, invoiceData);

      // Add tables based on invoice type

      // For call-based invoices with outbound calls and delivered sections
      let yPos = 90; // Starting Y position
      const spacing = 20;
      // Add outbound calls table

      invoiceData.tables.filter(t => !['confirmationCalls', 'followupCalls'].includes(t.slug) && t.data && t.data.length > 0).map(t => {

        yPos = addItemsTable(doc, t, yPos);
        yPos += spacing
      })
      invoiceData.tables.filter(t => ['confirmationCalls', 'followupCalls'].includes(t.slug) && t.data && t.data.length > 0).map(t => {

        yPos = addOutboundCallsTable(doc, t, yPos);
        yPos += spacing
      })


      //yPos = addDeliveredTable(doc, invoiceData, yPos + spacing);
      addRemittanceSummary(doc, invoiceData, yPos + spacing);


      // Add remittance summary at the end of the last page
      addRemittanceSummary(doc, invoiceData, 90);


      // Add footer with page numbers after all content has been added
      // This ensures the page count is correct
      addFooter(doc, invoiceData);

      // Save the PDF
      const fileName = `seller_invoice_${invoiceData.invoiceNum || moment().format('YYYYMMDD_HHmmss')}.pdf`;
      doc.save(fileName);
      resolve(fileName);
    } catch (error) {
      reject(error);
    }
  });
};
export const generateSourcingInvoicePDF = (invoiceData) => {
  thisInvoiceData.invoiceNum = invoiceData.invoiceNum
  thisInvoiceData.customerName = invoiceData.seller.legalName;
  thisInvoiceData.customerAddress = invoiceData.seller.address;
  thisInvoiceData.postalCode = invoiceData.postalCode;
  thisInvoiceData.city = invoiceData.city;
  thisInvoiceData.country = invoiceData.country;
  thisInvoiceData.vat = invoiceData.vat;
  return new Promise((resolve, reject) => {
    try {
      var callAddFont = function () {
        this.addFileToVFS('GothamBold-bold.ttf', gothamFont);
        this.addFont('GothamBold-bold.ttf', 'GothamBold', 'bold');
        this.addFileToVFS('GothamMedium-normal.ttf', gothamMedium);
        this.addFont('GothamMedium-normal.ttf', 'GothamMedium', 'normal');
        this.addFileToVFS('GothamLight-italic.ttf', thinFont);
        this.addFont('GothamLight-italic.ttf', 'GothamLight', 'italic');

      };
      jsPDF.API.events.push(['addFonts', callAddFont])



      // Initialize PDF document
      const doc = new jsPDF();

      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;
      const margin = 15;

      // Add company logo and header
      addHeader(doc);

      // Add invoice details
      addInvoiceDetails(doc, invoiceData);

      // Add tables based on invoice type

      // For call-based invoices with outbound calls and delivered sections
      let yPos = 90; // Starting Y position
      const spacing = 20;
      // Add outbound calls table

      yPos = addItemsTable(doc, {
        data: [invoiceData], columns: [
          { label: "Product Name", slug: "product" },
          { label: "Quantity", slug: "quantity" },
          { label: "Selling Price", slug: "sellingPrice" },
          { label: "Total Amount", slug: "amountDue" },

        ]
      }, yPos, false);
      yPos += spacing;


      //yPos = addDeliveredTable(doc, invoiceData, yPos + spacing);
      // addSourcingRemittanceSummary(doc, invoiceData, yPos + spacing);


      // Add remittance summary at the end of the last page
      addSourcingRemittanceSummary(doc, invoiceData, 90);


      // Add footer with page numbers after all content has been added
      // This ensures the page count is correct
      addFooter(doc, invoiceData);

      // Save the PDF
      const fileName = `seller_invoice_${invoiceData.invoiceNum || moment().format('YYYYMMDD_HHmmss')}.pdf`;
      doc.save(fileName);
      resolve(fileName);
    } catch (error) {
      reject(error);
    }
  });
};

const addHeader = (doc) => {
  const pageWidth = doc.internal.pageSize.width;

  // Add the logo to the top left (smaller size)
  doc.addImage(logopngbase64, 'PNG', 15, 10, 30, 15); // Reduced from 40x20 to 30x15

  // Invoice number and title
  doc.setFontSize(10);
  doc.setFont('GothamLight', 'italic');
  doc.setTextColor(35, 39, 85); // #232755
  doc.text('#' + (thisInvoiceData.invoiceNum || '699'), pageWidth - 27, 15, { align: "right" });

  // Invoice title (bigger text)
  doc.setFontSize(30); // Increased from 24 to 30
  doc.setFont('GothamBold', 'bold');
  doc.text("Invoice", pageWidth - 15, 25, { align: "right" });

  // Horizontal line
  doc.setDrawColor(220, 220, 220);
};

const addInvoiceDetails = (doc) => {
  const pageWidth = doc.internal.pageSize.width;

  const radius = 4;
  const textSizeMultiplier = 1.2;
  const topY = 35;
  const margin = 15; // Consistent margin for left and right sides
  const leftX = margin;
  const lineHeight = 3 * textSizeMultiplier;
  const spacing = 5;
  const gapBetweenBoxes = 20; // Increased gap between the two boxes
  const availableWidth = pageWidth - (2 * margin); // Total available width after margins
  const baseBoxWidth = (availableWidth - gapBetweenBoxes) / 2; // Base width for boxes

  // Set common styles
  doc.setDrawColor(0, 0, 0, 0.1); // #0000001A border
  doc.setLineWidth(0.5);

  // Function to calculate box height based on number of lines and header
  const calculateBoxHeight = (numberOfLines) => {
    // Account for: top spacing + header + increased spacing + content lines + bottom spacing
    return (spacing * 2) + lineHeight + (spacing * 1.2) + (lineHeight * numberOfLines);
  };

  // Calculate the left box width (make it smaller than the base width)
  const leftBoxWidth = baseBoxWidth - 5; // Reduce the left box width by 10 units

  // Left rect
  const leftNumberOfLines = 5; // Number of text lines in the left box
  const leftBoxHeight = calculateBoxHeight(leftNumberOfLines);

  doc.roundedRect(leftX, topY, leftBoxWidth, leftBoxHeight, radius, radius, 'S');

  // Left column - Bill from
  doc.setFontSize(10 * textSizeMultiplier);
  doc.setFont('GothamBold', 'bold');
  doc.text("Bill from", leftX + spacing, topY + spacing * 2);

  const billFromTextSpacing = spacing * 1.2; // Increased spacing

  doc.setFont('GothamLight', 'italic');
  doc.setFontSize(7 * textSizeMultiplier);

  // Use splitTextToSize to wrap long text
  const billFromText = [
    "POWER GROUP WORLD",
    "24 op de Selenn",
    "Bascharage - Luxembourg",
    "<EMAIL>",
    "TVA: LU33994352 - RC: B297127"
  ];

  let currentY = topY + spacing * 2 + billFromTextSpacing;
  billFromText.forEach((line) => {
    doc.text(line, leftX + spacing, currentY);
    currentY += lineHeight; // Adjust Y position for the next line
  });

  // Right column - Bill to
  const rightX = pageWidth - margin - (baseBoxWidth + 15); // Adjust the right box position


  doc.setFontSize(10 * textSizeMultiplier);
  doc.setFont('GothamBold', 'bold');
  doc.text("Bill to", rightX + spacing, topY + spacing * 2);

  const billToTextSpacing = spacing * 1.2; // Increased spacing

  doc.setFont('GothamLight', 'italic');
  doc.setFontSize(7 * textSizeMultiplier);

  // // Define the labels for "Bill to"
  // const billToLabels = [
  //   "Company name",
  //   "Address",
  //   "Postal code",
  //   "City",
  //   "Country",
  //   "VAT"
  // ];

  // let billToY = topY + spacing * 2 + billToTextSpacing;
  // billToLabels.forEach((label, index) => {
  //   doc.text(label, rightX + spacing, billToY);
  //   billToY += lineHeight; // Adjust Y position for the next label
  // });

  // Bill to values (use splitTextToSize for wrapping)
  const billToValues = [
    'Company name : ' + thisInvoiceData.customerName || "PSIRROD Inc",
    'Address : ' + thisInvoiceData.customerAddress || "46, BD Zerktouini, Apt 06",
    'Postal code : ' + thisInvoiceData.postalCode || "20500",
    'City : ' + thisInvoiceData.city || "Casablanca",
    'Country : ' + thisInvoiceData.country || "Morocco",
    'VAT : ' + thisInvoiceData.vat || "*********"
  ];

  // Initialize the line counter for wrapped lines
  let lineCount = 0;
  const lineHeight2 = 3 * textSizeMultiplier; // line height used in the loop

  // Function to calculate the number of lines for wrapped text
  billToValues.forEach((value) => {
    const wrappedText = doc.splitTextToSize(value, baseBoxWidth - spacing); // Wrap text based on available width
    lineCount += wrappedText.length; // Increment line count based on the number of wrapped lines
  });

  // Now dynamically calculate the height based on the number of lines
  const rightBoxHeight = calculateBoxHeight(lineCount); // Pass the calculated line count to get height

  // The right box height will now adjust based on wrapped text
  doc.roundedRect(rightX, topY, baseBoxWidth + 15, rightBoxHeight, radius, radius, 'S');

  // Now adjust the Y-position of the "Bill to" text
  let valueY = topY + spacing * 2 + billToTextSpacing;

  // Draw each wrapped line in the Bill to section
  billToValues.forEach((value) => {
    const wrappedText = doc.splitTextToSize(value, baseBoxWidth - spacing); // Wrap text
    wrappedText.forEach((line, i) => {
      doc.text(line, rightX + spacing + 0, valueY + (i * lineHeight2)); // Draw the wrapped line
    });
    valueY += wrappedText.length * lineHeight2; // Increment valueY based on the number of wrapped lines
  });
};


const cellRenderer = (row, slug) => {
  switch (slug) {
    case 'shippedAt':
    case 'createdAt':
      return moment(row[slug]).format('DD/MM/YYYY HH:mm')
    case 'orderPrice':
      return formatNumber(row[slug]) + ' ' + row['currency']
    case "shippingFees":
    case "customSurcharge":
    case "fulfillment":
    case "deliveredFees":
    case "codFees":
    case "fees":
    case "vat":
      return formatNumber(row[slug]) + ' USD'

    default:
      return row[slug]
  }
}

const addItemsTable = (doc, table, posY, hasTitle = true) => {
  let columns = table.columns;
  let tableData = table.data;

  const pageWidth = doc.internal.pageSize.width;
  if (hasTitle) {// Set up the section title
    doc.setFontSize(12);
    doc.setTextColor(35, 39, 85);
    doc.setFont('GothamBold', 'bold');
    doc.text(table.title || '', 15, posY - 4);


    doc.setFillColor(207, 41, 47); // Red color
    doc.roundedRect(pageWidth - 60, posY - 10, 45, 10, 5, 5, 'F');
    doc.setFontSize(10);
    doc.setTextColor(255, 255, 255);
    doc.text(`${tableData.length} Order(s)`, pageWidth - 38, posY - 3.5, { align: "center" });
  }

  // Create columnStyles object dynamically with all columns width set to 'auto'
  let columnStyles = {};
  for (let i = 0; i < columns.length; i++) {
    columnStyles[i] = { cellWidth: 'auto' };
  }


  autoTable(doc, {
    startY: posY += 5,
    head: [columns.map(col => col.label)],
    body: tableData.map(row => columns.map(col => cellRenderer(row, col.slug))),
    showHead: 'firstPage',
    margin: { top: 70, right: 15, bottom: 20, left: 15 },
    styles: {
      overflow: 'linebreak',
      cellPadding: 3,
      lineWidth: 0,
      fontSize: 8,
      halign: 'center',
      lineColor: [220, 220, 220]
    },
    headStyles: {
      fillColor: [35, 39, 85],
      textColor: [255, 255, 255],
      fontStyle: 'bold',
      halign: 'center'
    },
    bodyStyles: {
      textColor: [35, 39, 8] // Apply text color to all body cells
    },
    columnStyles: columnStyles, // use dynamic styles
    tableLineWidth: 0,
    tableLineColor: [220, 220, 220],
    willDrawCell: function (data) {
      // your existing cell drawing logic (if any)
    },
    didDrawPage: (data) => {
      if (data.pageNumber > 0) {
        addHeader(doc);
        data.settings.margin.top = 30;
      }
    }
  });

  const finalY = doc.lastAutoTable?.finalY || doc.previousAutoTable?.finalY || (posY + (tableData.length * 10) + 20);
  return finalY;
};


// Function to add the outbound calls table
const addOutboundCallsTable = (doc, table, startY) => {
  const pageWidth = doc.internal.pageSize.width;

  // Set up the section title
  doc.setFontSize(12);
  doc.setTextColor(35, 39, 85);
  doc.setFont('GothamBold', 'bold');
  doc.text(table.title, 15, startY - 4);


  doc.setFillColor(207, 41, 47); // Red color
  doc.roundedRect(pageWidth - 60, startY - 10, 45, 10, 5, 5, 'F');
  doc.setFontSize(10);
  doc.setTextColor(255, 255, 255);
  doc.text(`${table.data.length} Call(s)`, pageWidth - 38, startY - 3.5, { align: "center" });

  // Define columns for outbound calls
  const columns = table.columns;

  // Create columnStyles object dynamically with all columns width set to 'auto'
  let columnStyles = {};
  for (let i = 0; i < columns.length; i++) {
    columnStyles[i] = { cellWidth: 'auto' };
  }

  // Sample data if not provided
  const tableData = table.data || Array(10).fill({
    orderNum: "2311",
    response: "Delivery",
    date: "12 Nov 2024 - 12:39 PM",
    callFees: "1 USD"
  });

  // Generate table with autotable plugin
  autoTable(doc, {
    startY: startY += 5,
    head: [columns.map(col => col.label)],
    body: tableData.map(row => columns.map(col => row[col.slug] || "-")),
    showHead: 'firstPage',
    margin: { top: 70, right: 15, bottom: 20, left: 15 },
    styles: {
      overflow: 'linebreak',
      cellPadding: 2,
      fontSize: 8,
      textColor: [35, 39, 8],
      halign: 'center',
      lineWidth: 0, // Removed borders between cells
      lineColor: [220, 220, 220]
    },
    headStyles: {
      fillColor: [35, 39, 85], // #232755
      textColor: [255, 255, 255], // White text
      fontStyle: 'bold'
    },
    bodyStyles: {
      textColor: [35, 39, 8] // Apply text color to all body cells
    },
    columnStyles: columnStyles,
    tableLineWidth: 0, // Removed table borders
    tableLineColor: [35, 39, 85],
    // Table styling
    willDrawCell: function (data) {
      const doc = data.doc;
      const rows = data.table.body;

      // Add rounded corners to first and last cells of last body row
      // if (data.row.index === rows.length - 1 && data.section === 'body') {
      //   // First cell in last row (bottom-left corner)
      //   if (data.column.index === 0) {
      //     const x = data.cell.x;
      //     const y = data.cell.y;
      //     const w = data.cell.width;
      //     const h = data.cell.height;
      //     const r = 2; // corner radius

      //     // // Draw rounded corner
      //     doc.setFillColor(255, 255, 255);
      //     doc.roundedRect(x, y, w, h, r, r, 'F');

      //     // Add the text back
      //     doc.setTextColor(0, 0, 0);
      //     doc.setFont('GothamLight', 'italic');
      //     doc.text(data.cell.text, x + w / 2, y + h / 2, { align: 'center', baseline: 'middle' });

      //     // Skip default rendering
      //     return false;
      //   }

      //   // Last cell in last row (bottom-right corner)
      //   if (data.column.index === columns.length - 1) {
      //     const x = data.cell.x;
      //     const y = data.cell.y;
      //     const w = data.cell.width;
      //     const h = data.cell.height;
      //     const r = 2; // corner radius

      //     // Draw rounded corner
      //     doc.setFillColor(255, 255, 255);
      //     doc.roundedRect(x, y, w, h, r, r, 'F');

      //     // Add the text back
      //     doc.setTextColor(0, 0, 0);
      //     doc.setFont('GothamLight', 'italic');
      //     doc.text(data.cell.text, x + w / 2, y + h / 2, { align: 'center', baseline: 'middle' });

      //     // Skip default rendering
      //     return false;
      //   }
      // }
    },
    didDrawPage: (data) => {
      if (data.pageNumber > 0) {
        addHeader(doc);
        data.settings.margin.top = 30;
      }
    }
  });

  // Return the final Y position for the next table
  // Safely handle the case where previousAutoTable might be undefined
  const finalY = doc.lastAutoTable?.finalY || doc.previousAutoTable?.finalY || (startY + (tableData.length * 10) + 20);
  return finalY;
};

// Function to add the delivered table
const addDeliveredTable = (doc, invoiceData, startY) => {
  const pageWidth = doc.internal.pageSize.width;

  // Set up the section title
  doc.setFontSize(12);
  doc.setFont('GothamBold', 'bold');
  doc.setTextColor(20, 27, 77); // Dark blue
  doc.text("DELIVERED", 15, startY - 5);

  // Add orders count in a red button-like element
  const ordersCount = invoiceData.deliveredOrders?.length || 12;
  doc.setFillColor(207, 41, 47); // Red color
  doc.roundedRect(pageWidth - 60, startY - 10, 45, 10, 2, 2, 'F');
  doc.setTextColor(255, 255, 255); // White text
  doc.setFontSize(10);
  doc.text(`${ordersCount} Order(s)`, pageWidth - 38, startY - 3, { align: "center" });

  // Define columns for delivered orders
  const columns = [
    { header: "Order N°", dataKey: "orderNum" },
    { header: "From", dataKey: "from" },
    { header: "To", dataKey: "to" },
    { header: "Order Price", dataKey: "orderPrice" },
    { header: "Delivered Fees", dataKey: "deliveredFees" },
    { header: "COD Fees", dataKey: "codFees" },
    { header: "VAT", dataKey: "vat" }
  ];

  // Sample data if not provided
  const tableData = (invoiceData.deliveredOrders || [
    {
      orderNum: "2388",
      from: "Belgium",
      to: "Belgium",
      orderPrice: "122 USD",
      deliveredFees: "12.9 USD",
      codFees: "COD Fees",
      vat: "0 USD"
    },
    {
      orderNum: "UPSELL 92",
      from: "Luxembourg",
      to: "Luxembourg",
      orderPrice: "122 USD",
      deliveredFees: "12.9 USD",
      codFees: "COD Fees",
      vat: "0 USD"
    },
    {
      orderNum: "2388",
      from: "Belgium",
      to: "Belgium",
      orderPrice: "122 USD",
      deliveredFees: "12.9 USD",
      codFees: "COD Fees",
      vat: "0 USD"
    }
  ]);

  // Generate table with autotable plugin
  autoTable(doc, {
    startY: startY,
    head: [columns.map(col => col.header)],
    body: tableData.map(row => columns.map(col => row[col.dataKey] || "-")),
    margin: { top: 70, right: 15, bottom: 20, left: 15 },
    styles: {
      overflow: 'linebreak',
      cellPadding: 3,
      fontSize: 9,
      textColor: [0, 0, 0],
      halign: 'center',
      lineWidth: 0, // Removed borders between cells
      lineColor: [220, 220, 220]
    },
    headStyles: {
      fillColor: [35, 39, 85], // #232755
      textColor: [255, 255, 255], // White text
      fontStyle: 'bold'
    },
    columnStyles: {
      0: { cellWidth: 30 },
      1: { cellWidth: 30 },
      2: { cellWidth: 30 },
      3: { cellWidth: 30 },
      4: { cellWidth: 30 },
      5: { cellWidth: 30 },
      6: { cellWidth: 20 }
    },
    tableLineWidth: 0.1,
    tableLineColor: [220, 220, 220],
    // Table styling
    willDrawCell: function (data) {
      const doc = data.doc;
      const rows = data.table.body;

      // Add rounded corners to first and last cells of last body row
      if (data.row.index === rows.length - 1 && data.section === 'body') {
        // First cell in last row (bottom-left corner)
        if (data.column.index === 0) {
          const x = data.cell.x;
          const y = data.cell.y;
          const w = data.cell.width;
          const h = data.cell.height;
          const r = 2; // corner radius

          // Draw rounded corner
          doc.setFillColor(255, 255, 255);
          doc.roundedRect(x, y, w, h, r, r, 'F');

          // Add the text back
          doc.setTextColor(0, 0, 0);
          doc.setFont('GothamLight', 'italic');
          doc.text(data.cell.text, x + w / 2, y + h / 2, { align: 'center', baseline: 'middle' });

          // Skip default rendering
          return false;
        }

        // Last cell in last row (bottom-right corner)
        if (data.column.index === columns.length - 1) {
          const x = data.cell.x;
          const y = data.cell.y;
          const w = data.cell.width;
          const h = data.cell.height;
          const r = 2; // corner radius

          // Draw rounded corner
          doc.setFillColor(255, 255, 255);
          doc.roundedRect(x, y, w, h, r, r, 'F');

          // Add the text back
          doc.setTextColor(0, 0, 0);
          doc.setFont('GothamLight', 'italic');
          doc.text(data.cell.text, x + w / 2, y + h / 2, { align: 'center', baseline: 'middle' });

          // Skip default rendering
          return false;
        }
      }
    },
    didDrawPage: (data) => {
      // For pages after the first page, add the header
      if (data.pageNumber > 1) {
        // Add the header (logo and invoice title) to subsequent pages
        addHeader(doc, invoiceData);

        // Adjust the table top margin for subsequent pages to start right after the header
        // This effectively removes the space that would be taken by invoice details
        data.settings.margin.top = 30; // Reduced margin since we don't have invoice details
      }
      // Footer will be added after all content in the generateInvoicePDF function
    }
  });

  // Return the final Y position
  // Safely handle the case where previousAutoTable might be undefined
  const finalY = doc.lastAutoTable?.finalY || doc.previousAutoTable?.finalY || (startY + (tableData.length * 10) + 20);
  return finalY;
};

// Function to add the summary table
const addSummaryTable = (doc, invoiceData, startY) => {
  // Get page width for positioning elements
  const pageWidth = doc.internal.pageSize.width;

  // Set up the section title
  doc.setFontSize(12);
  doc.setFont('GothamBold', 'bold');
  doc.setTextColor(20, 27, 77); // Dark blue
  doc.text("INVOICE SUMMARY", 15, startY - 5);

  // Add a total amount indicator on the right side
  doc.setFillColor(35, 39, 85); // Dark blue
  doc.roundedRect(pageWidth - 60, startY - 10, 45, 10, 2, 2, 'F');
  doc.setTextColor(255, 255, 255); // White text
  doc.setFontSize(10);
  doc.text("TOTAL", pageWidth - 38, startY - 3, { align: "center" });

  // Define columns for summary
  const columns = [
    { header: "Description", dataKey: "description" },
    { header: "Amount", dataKey: "amount" }
  ];

  // Calculate totals from invoice data
  const outboundCallsTotal = (invoiceData.outboundCalls || []).reduce((sum, call) => {
    const fee = parseFloat(call.callFees?.replace(/[^0-9.]/g, '') || 0);
    return sum + fee;
  }, 0);

  const deliveredTotal = (invoiceData.deliveredOrders || []).reduce((sum, order) => {
    const fee = parseFloat(order.deliveredFees?.replace(/[^0-9.]/g, '') || 0);
    return sum + fee;
  }, 0);

  const codFeesTotal = (invoiceData.deliveredOrders || []).reduce((sum, order) => {
    const fee = parseFloat(order.codFees?.replace(/[^0-9.]/g, '') || 0);
    return sum + fee;
  }, 0);

  const vatTotal = (invoiceData.deliveredOrders || []).reduce((sum, order) => {
    const vat = parseFloat(order.vat?.replace(/[^0-9.]/g, '') || 0);
    return sum + vat;
  }, 0);

  // Calculate grand total
  const grandTotal = outboundCallsTotal + deliveredTotal + codFeesTotal + vatTotal;

  // Prepare table data
  const tableData = [
    { description: "Outbound Calls", amount: `${outboundCallsTotal.toFixed(2)} USD` },
    { description: "Delivered Fees", amount: `${deliveredTotal.toFixed(2)} USD` },
    { description: "COD Fees", amount: `${codFeesTotal.toFixed(2)} USD` },
    { description: "VAT", amount: `${vatTotal.toFixed(2)} USD` },
    { description: "Total", amount: `${grandTotal.toFixed(2)} USD` }
  ];

  // Generate table with autotable plugin
  autoTable(doc, {
    startY: startY,
    head: [columns.map(col => col.header)],
    body: tableData.map(row => columns.map(col => row[col.dataKey] || "-")),
    margin: { top: 70, right: 15, bottom: 20, left: 15 },
    styles: {
      overflow: 'linebreak',
      cellPadding: 3,
      fontSize: 9,
      textColor: [0, 0, 0],
      halign: 'center',
      lineWidth: 0, // Removed borders between cells
      lineColor: [220, 220, 220]
    },
    headStyles: {
      fillColor: [35, 39, 85], // #232755
      textColor: [255, 255, 255], // White text
      fontStyle: 'bold'
    },
    columnStyles: {
      0: { cellWidth: 'auto', halign: 'left' },
      1: { cellWidth: 80, halign: 'right' }
    },
    tableLineWidth: 0, // Removed table borders
    tableLineColor: [220, 220, 220],
    // Table styling
    willDrawCell: function (data) {
      const doc = data.doc;
      const rows = data.table.body;

      // Add rounded corners to first and last cells of last body row
      if (data.row.index === rows.length - 1 && data.section === 'body') {
        // First cell in last row (bottom-left corner)
        if (data.column.index === 0) {
          const x = data.cell.x;
          const y = data.cell.y;
          const w = data.cell.width;
          const h = data.cell.height;
          const r = 2; // corner radius

          // Draw rounded corner
          doc.setFillColor(255, 255, 255);
          doc.roundedRect(x, y, w, h, r, r, 'F');

          // Add the text back
          doc.setTextColor(0, 0, 0);
          doc.setFont('GothamBold', 'bold'); // Make the total row bold
          doc.text(data.cell.text, x + 10, y + h / 2, { align: 'left', baseline: 'middle' });

          // Skip default rendering
          return false;
        }

        // Last cell in last row (bottom-right corner)
        if (data.column.index === columns.length - 1) {
          const x = data.cell.x;
          const y = data.cell.y;
          const w = data.cell.width;
          const h = data.cell.height;
          const r = 2; // corner radius

          // Draw rounded corner
          doc.setFillColor(255, 255, 255);
          doc.roundedRect(x, y, w, h, r, r, 'F');

          // Add the text back
          doc.setTextColor(0, 0, 0);
          doc.setFont('GothamBold', 'bold'); // Make the total row bold
          doc.text(data.cell.text, x + w - 10, y + h / 2, { align: 'right', baseline: 'middle' });

          // Skip default rendering
          return false;
        }
      }

      // Highlight the total row
      if (data.row.index === rows.length - 1 && data.section === 'body') {
        // Set bold font for the total row
        doc.setFont('GothamBold', 'bold');
      }
    },
    didDrawPage: (data) => {
      // For pages after the first page, add the header
      if (data.pageNumber > 1) {
        // Add the header (logo and invoice title) to subsequent pages
        addHeader(doc, invoiceData);

        // Adjust the table top margin for subsequent pages to start right after the header
        // This effectively removes the space that would be taken by invoice details
        data.settings.margin.top = 30; // Reduced margin since we don't have invoice details
      }
      // Footer will be added after all content in the generateInvoicePDF function
    }
  });

  // Return the final Y position
  // Safely handle the case where previousAutoTable might be undefined
  const finalY = doc.lastAutoTable?.finalY || doc.previousAutoTable?.finalY || (startY + (tableData.length * 10) + 20);
  return finalY;
};

// Function to add consistent footer on all pages
const addFooter = (doc, invoiceData) => {
  const pageWidth = doc.internal.pageSize.width;
  const totalPages = doc.internal.getNumberOfPages();

  // Loop through each page to add the footer
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);

    // Add page number and date at the bottom
    doc.setFontSize(8);
    doc.setFont('GothamLight', 'italic');
    doc.setTextColor(100, 100, 100);

    const pageInfo = `Page ${i} of ${totalPages}`;
    const dateInfo = `Date: ${invoiceData.invoiceDate && moment(invoiceData.invoiceDate).format("DD/MM/YYYY") || invoiceData.createdAt && moment(invoiceData.createdAt).format("DD/MM/YYYY")}`;
    const invoiceInfo = `Power Group World - Invoice #${invoiceData.invoiceNum || '699'}`;

    doc.text(pageInfo, 15, doc.internal.pageSize.height - 10);
    doc.text(invoiceInfo, pageWidth / 2, doc.internal.pageSize.height - 10, { align: "center" });
    doc.text(dateInfo, pageWidth - 15, doc.internal.pageSize.height - 10, { align: "right" });
  }
};

// Function to add the remittance summary at the end of the last page
const addRemittanceSummary = (doc, invoiceData, posY) => {
  // Get page dimensions
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 15;
  const footerHeight = 10; // Space reserved for footer

  // Calculate the height needed for the summary
  const summaryHeight = 95; // Approximate height needed for the summary

  // Calculate the minimum Y position where the summary should start to fit above footer
  const minYForSummary = pageHeight - footerHeight - summaryHeight;

  // Determine if we need to add a new page or adjust position
  let startY = minYForSummary;

  if (!posY) {
    // If no posY provided, position at the bottom of the current page
    startY = minYForSummary;
  } else if (posY > minYForSummary) {
    // If posY is too far down the page to fit the summary, add a new page
    doc.addPage();
    startY = minYForSummary;
  } else {
    startY = minYForSummary;
  }


  // Calculate the right side starting position (for the second half of the page)
  const rightSideX = pageWidth / 2;

  // Get total amount from invoice data or use default
  const totalAmount = formatNumber(isNaN(Number(invoiceData.totalRemittance)) ? 0.00 : Number(invoiceData.totalRemittance)) + " USD";


  // Add title "TOTAL REMITTANCE"
  doc.setFontSize(14);
  doc.setFont('GothamBold', 'bold');
  doc.setTextColor(35, 39, 85); // #232755
  doc.text("TOTAL REMITTANCE", rightSideX, startY);

  // Add total amount on the right
  doc.text(totalAmount, pageWidth - margin, startY, { align: "right" });

  // Add line items - use invoice data if available, otherwise use defaults
  const lineItems = [
    { label: "First Mille", value: formatNumber(isNaN(Number(invoiceData.firstMile)) ? 0.00 : Number(invoiceData.firstMile)) + " USD" },
    { label: "Last Mille", value: formatNumber(isNaN(Number(invoiceData.lastMile)) ? 0.00 : Number(invoiceData.lastMile)) + " USD" },
    { label: "Custom Surcharge", value: formatNumber(isNaN(Number(invoiceData.customSurcharge)) ? 0.00 : Number(invoiceData.customSurcharge)) + " USD" },
    { label: "Fulfillment", value: formatNumber(isNaN(Number(invoiceData.fulfillment)) ? 0.00 : Number(invoiceData.fulfillment)) + " USD" },
    { label: "Return Fees", value: formatNumber(isNaN(Number(invoiceData.return)) ? 0.00 : Number(invoiceData.return)) + " USD" },
    { label: "Delivered Fees", value: formatNumber(isNaN(Number(invoiceData.deliveredFees)) ? 0.00 : Number(invoiceData.deliveredFees)) + " USD" },
    { label: "Outbound Call Follow up", value: formatNumber(isNaN(Number(invoiceData.followupCalls)) ? 0.00 : Number(invoiceData.followupCalls)) + " USD" },
    { label: "VAT", value: formatNumber(isNaN(Number(invoiceData.vat)) ? 0.00 : Number(invoiceData.vat)) + " USD" },
    {
      label: "Other Charges", value: formatNumber(
        Number(invoiceData.confirmedFees) || 0 + Number(invoiceData.confirmationCalls) || 0 + Number(invoiceData.codFees) || 0 + Number(invoiceData.enteredLeads) || 0 + Number(invoiceData.upsellFees) || 0
      ) + " USD"
    }
  ];

  // Set font for line items
  doc.setFontSize(10);
  doc.setFont('GothamMedium', 'normal');

  // Add each line item with spacing
  const lineHeight = 7;
  let currentY = startY + 7; // Start 20 points below the title

  lineItems.forEach(item => {
    doc.text(item.label, rightSideX, currentY);
    doc.text(item.value, pageWidth - margin, currentY, { align: "right" });
    currentY += lineHeight;
  });

  // Add red background for NET REMITTANCE
  const netRemittanceY = currentY;
  const netRemittanceHeight = 12;

  doc.setFillColor(207, 41, 47); // Red color
  doc.roundedRect(rightSideX, netRemittanceY, pageWidth - rightSideX - margin, netRemittanceHeight, 4, 4, 'F');
  // Add NET REMITTANCE text
  doc.setFontSize(10);
  doc.setFont('GothamBold', 'bold');
  doc.setTextColor(255, 255, 255); // White text
  doc.text("NET REMITTANCE", rightSideX + 5, netRemittanceY + (netRemittanceHeight / 4) + 4);

  // Add net remittance amount
  const netRemittanceAmount = formatNumber(invoiceData.netRemittance) + " USD"; //"2,922.00 USD";
  doc.text(netRemittanceAmount, pageWidth - margin - 5, netRemittanceY + (netRemittanceHeight / 4) + 4, { align: "right" });

  // Return the final Y position after the summary
  return netRemittanceY + netRemittanceHeight;
};

// Function to add the remittance summary at the end of the last page
const addSourcingRemittanceSummary = (doc, invoiceData, posY) => {
  // Get page dimensions
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 15;
  const footerHeight = 10; // Space reserved for footer

  // Calculate the height needed for the summary
  const summaryHeight = 45; // Approximate height needed for the summary

  // Calculate the minimum Y position where the summary should start to fit above footer
  const minYForSummary = pageHeight - footerHeight - summaryHeight;

  // Determine if we need to add a new page or adjust position
  let startY = minYForSummary;

  if (!posY) {
    // If no posY provided, position at the bottom of the current page
    startY = minYForSummary;
  } else if (posY > minYForSummary) {
    // If posY is too far down the page to fit the summary, add a new page
    doc.addPage();
    startY = minYForSummary;
  } else {
    startY = minYForSummary;
  }


  // Calculate the right side starting position (for the second half of the page)
  const rightSideX = pageWidth / 2;

  // Get total amount from invoice data or use default
  const totalAmount = formatNumber(isNaN(Number(invoiceData.amountDue)) ? 0.00 : Number(invoiceData.amountDue)) + " USD";


  // Add title "TOTAL REMITTANCE"
  doc.setFontSize(14);
  doc.setFont('GothamBold', 'bold');
  doc.setTextColor(35, 39, 85); // #232755
  doc.text("TOTAL ", rightSideX, startY);

  // Add total amount on the right
  doc.text(totalAmount, pageWidth - margin, startY, { align: "right" });

  // Add line items - use invoice data if available, otherwise use defaults
  const lineHeight = 7;
  let currentY = startY + 7;


  const lineItems = [
    { label: "Total Qty", value: invoiceData.quantity.toString() || '0' },
    {
      label: "Bulk shipping", value: invoiceData.ddpClient.toString() || '0'
    }
  ];

  // Set font for line items
  doc.setFontSize(10);
  doc.setFont('GothamMedium', 'normal');



  lineItems.forEach(item => {
    doc.text(item.label, rightSideX, currentY);
    doc.text(item.value, pageWidth - margin, currentY, { align: "right" });
    currentY += lineHeight;
  });

  // Add red background for NET REMITTANCE
  const netRemittanceY = currentY;
  const netRemittanceHeight = 12;

  doc.setFillColor(207, 41, 47); // Red color
  doc.roundedRect(rightSideX, netRemittanceY, pageWidth - rightSideX - margin, netRemittanceHeight, 4, 4, 'F');
  // Add NET REMITTANCE text
  doc.setFontSize(10);
  doc.setFont('GothamBold', 'bold');
  doc.setTextColor(255, 255, 255); // White text
  doc.text("TOTAL", rightSideX + 5, netRemittanceY + (netRemittanceHeight / 4) + 4);

  // Add net remittance amount
  const netRemittanceAmount = formatNumber(totalAmount) + " USD"; //"2,922.00 USD";
  doc.text(totalAmount, pageWidth - margin - 5, netRemittanceY + (netRemittanceHeight / 4) + 4, { align: "right" });

  // Return the final Y position after the summary
  return netRemittanceY + netRemittanceHeight;
};