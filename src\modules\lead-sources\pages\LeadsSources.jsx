import DashboardLayout from "../../shared/layouts/DashboardLayout";
import {
    Add01Icon,
    ArrowLeft02Icon,
    ArrowRight02Icon,
    ArrowUpDownIcon,
    Delete01Icon,
    FolderImportIcon,
    PencilEdit01Icon,
    ViewIcon
} from "hugeicons-react";
import { Tab, Tabs } from "@heroui/tabs";
import { Chip } from "@heroui/chip";
import { Button } from "@heroui/button";
import { Pagination } from "@heroui/pagination";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/table";
import FileUploadModal from "@/modules/lead-sources/components/FileUploadModal.jsx";
import CustomTabs from "../../shared/components/CustomTabs";
import CustomTable from "../../shared/components/CustomTable";
import { useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { addShopifySourceLead, deleteLeadSource, getLeadSourceList } from "../../../core/redux/slices/leadSource/leadSourcesSlice";
import moment from "moment";
import { toast } from "sonner";
import { Popover, PopoverContent, PopoverTrigger, Tooltip } from "@heroui/react";
import { formatNumber } from "../../../core/utils/functions";
import ConnectToModal from "../components/ConnectToModal";
import { forceImportOrderFromGoogleSheet } from "../../../core/redux/slices/leadSource/googleSheetSlice";
import { fetchDropifyData } from "../../../core/redux/slices/leadSource/dropifySlice";


const columns = [
    // { key: "checkbox", label: "#" },
    { key: "source", label: "Source" },
    { key: "name", label: "Name" },
    { key: "lastImportLineOrder", label: "Last Import" },
    { key: "lastImportedDate", label: "Last Imported Date" },
    { key: "createdAt", label: "Created At", sortable: true },
    { key: "actions", label: "Actions" },
];

function useQuery() {
    return new URLSearchParams(useLocation().search);
}

export default function LeadsSources() {
    const query = useQuery();
    const sheets = query.get('sheets');
    const [selectedTab, setSelectedTab] = useState('Active');
    const [isFileUploadModalOpen, setIsFileUploadModalOpen] = useState(sheets);
    const [selectedRows, setSelectedRows] = useState([]);
    const [sortAscending, setSortAscending] = useState(true);
    const rowsPerPage = 10;
    const [foringImport, setForingImport] = useState(null);
    const location = useLocation();
    const [loadingRow, setLoadingRow] = useState({})
    const [currentPage, setCurrentPage] = useState(1);
    // Extract query parameters from the URL
    const searchParams = new URLSearchParams(window.location.search);
    const clientId = searchParams.get("client_id");
    const isDropify = searchParams.get("is_dropify");
    const redirectUri = searchParams.get("redirect_uri");
    const [isFConnectToModalOpen, setIsConnectToModalOpen] = useState(isDropify);
    const dispatch = useDispatch();
    const { leadSources, loading, error } = useSelector((state) => state.leadSources);

    const hasAddedShopify = useRef(false);



    useEffect(() => {
        const query = new URLSearchParams(location.search);

        const name = query.get('name');
        const shop = query.get('shop');
        const token = query.get('token');

        if (name === 'shopify' && shop && token && !hasAddedShopify.current) {
            hasAddedShopify.current = true;
            dispatch(addShopifySourceLead({ shop, token }));
        }
    }, [location.search, dispatch]);




    useEffect(() => {
        if (location.state?.openUploader) {
            setIsFileUploadModalOpen(true);
        }
    }, [location.state]);

    useEffect(() => {
        dispatch(getLeadSourceList({ page: currentPage }));
    }, [dispatch, currentPage])


    const removeLeadSource = async (id, type) => {
        setLoadingRow(prev => ({ ...prev, [id]: true }))
        const result = await dispatch(deleteLeadSource({ import_id: id, api_type: type }))
        if (deleteLeadSource.fulfilled.match(result)) {
            toast.success(result.payload.message)
        }
        setLoadingRow(prev => ({ ...prev, [id]: false }))
    }


    const handleForceImport = async (sheet) => {

        setForingImport(sheet.id);
        const res = await dispatch(forceImportOrderFromGoogleSheet({ spreadSheetId: sheet.id }))
        if (forceImportOrderFromGoogleSheet.fulfilled.match(res)) {
            toast.success("Orders imported successfully");

        }
        setForingImport(null);
    }
    const renderCell = useCallback((item, columnKey) => {
        const cellValue = item[columnKey];

        switch (columnKey) {
            case "id":
                return <span>{item.id}</span>;
            case "createdAt":
                return <span>{moment(item.createdAt).format('DD/MM/YYYY HH:mm')}</span>;
            case "source":
                return <span>{item.type}</span>;
            case "name":
                return <span>{item.name}</span>;
            case "lastImportLineOrder":
                return <span>{item.lastImportLine}</span>;
            case "lastImportedDate":
                return <span>{item.lastImportDate ? moment(item.lastImportDate).format('DD/MM/YYYY HH:mm') : ''}</span>;
            case "actions":
                return (
                    <div className="flex flex-row gap-2 justify-center">
                        <Tooltip content="Force orders import">
                            <Button isLoading={foringImport === item.id} onClick={() => handleForceImport(item)} isIconOnly size="sm" className="rounded-full bg-blue-700"  >
                                <FolderImportIcon size={18} className="text-white" />
                            </Button>
                        </Tooltip>
                        <Popover
                            showArrow
                            backdrop="opaque"
                            classNames={{

                            }}
                            placement="right"
                        >
                            <PopoverTrigger>
                                <Button isIconOnly size="sm" className="rounded-full bg-glb_red">
                                    <Delete01Icon size={18} className="text-white" />
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent>
                                {(titleProps) => (
                                    <div className="px-1 py-2 ">
                                        <h3 className="text-small font-bold mb-2 text-glb_red" >
                                            Lead Source Deletion
                                        </h3>
                                        <hr className="my-1 border-black/30 dark:border-white/30" />
                                        <h6 className="text-small font-normal my-2 w-[90%] mx-auto " >
                                            Are you sure you want to delete "{item.name}"?
                                            {item.type === 'google_sheets' && (
                                                <p className="text-xs text-glb_red mb-2">
                                                    Note: If this is the last sheet, your Google account will be disconnected.
                                                </p>
                                            )}

                                        </h6>
                                        <hr className="my-1 border-black/30 dark:border-white/30" />
                                        <div className="flex justify-between items-center gap-2 mt-2">
                                            <span size="sm" className="text-gray-500">
                                                ESC
                                            </span>
                                            <Button isLoading={loadingRow[item.id]} size="sm" color="danger" onClick={() => removeLeadSource(item.id, item.type)}>
                                                Delete
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </PopoverContent>
                        </Popover>


                    </div>
                );
            default:
                return cellValue;
        }
    }, [foringImport]);



    return (
        <>
            <DashboardLayout
                title={`${leadSources && leadSources.paginate && leadSources.paginate.count.toString() ? formatNumber(leadSources.paginate.count) : '...'} Lead Sources`}
                actions={<div className="flex flex-row  gap-2 flex-1 justify-end">
                    <Button color="default" className="rounded-full bg-info text-white"
                        onClick={() => setIsFileUploadModalOpen(true)}>
                        <Add01Icon size={18} /> Connect
                    </Button>
                    {/* <Button color="default" className="rounded-full bg-glb_red text-white">
                        <PencilEdit01Icon size={18} /> Actions
                    </Button> */}
                </div>}
            >
                {/* <div>
                        <CustomTabs
                            activeCount={allRows.filter((row) => row.status === 'Active').length}
                            archivedCount={
                                allRows.filter((row) => row.status === 'Archived').length
                            }
                            selectedTab={selectedTab}
                            onTabChange={setSelectedTab}
                        />
                    </div> */}



                <div className="min-w-full py-4 flex-1 h-full flex-grow rounded">
                    <CustomTable
                        columns={columns}
                        data={leadSources.result}  // Pass filtered products based on the view
                        renderCell={renderCell}
                        setSelectedRows={setSelectedRows}
                        selectedRows={selectedRows} // Pass selected rows state
                        rowsPerPage={rowsPerPage}  // Pass rows per page
                        className="dark:bg-gray-800 dark:text-white" // Dark mode support
                        loading={loading} // Pass loading state
                        error={error} // Pass error state
                        currentPage={currentPage} // Pass current page
                        // Pass setCurrentPage function
                        paginate={leadSources.paginate}
                    />
                </div>
            </DashboardLayout>


            <FileUploadModal isOpen={isFileUploadModalOpen} redirected={sheets}
                setIsOpen={() => setIsFileUploadModalOpen(false)} />
            <ConnectToModal isOpen={isFConnectToModalOpen} store={"Dropify"}
                clientId={clientId} redirectUri={redirectUri}
                setIsOpen={() => {
                    setIsConnectToModalOpen(false);
                    const query = new URLSearchParams(location.search);
                    query.delete('is_dropify');
                    query.delete('client_id');
                    query.delete('redirect_uri');
                    const newUrl = `${location.pathname}?${query.toString()}`;
                    window.history.replaceState(null, '', newUrl);
                }} />
        </>
    );
}
