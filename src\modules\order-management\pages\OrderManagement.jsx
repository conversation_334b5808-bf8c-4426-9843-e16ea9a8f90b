import {
    Add01Icon,
    ArrowLeft02Icon,
    ArrowRight02Icon,
    ArrowUpDownIcon,
    Calendar01Icon,
    CallBlocked02Icon,
    CallEnd01Icon,
    CallOutgoing02Icon,
    CancelCircleHalfDotIcon,
    CancelCircleIcon,
    CheckmarkCircle01Icon,
    CheckmarkCircle02Icon,
    CircleArrowLeft01Icon,
    Clock01Icon,
    Copy01Icon,
    DeliveryTruck01Icon,
    DownloadSquare01Icon,
    Link01Icon,
    PackageIcon,
    PencilEdit01Icon,
    ReloadIcon,
    RepeatIcon,
    ReturnRequestIcon,
    Settings02Icon,
    TestTube01Icon,
    UploadSquare01Icon,
    UserAdd02Icon,
    ViewIcon,
    XVariableCircleIcon
} from "hugeicons-react";
import { Tab, Tabs } from "@heroui/tabs";
import { Chip } from "@heroui/chip";
import { Button } from "@heroui/button";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import OrderInformationModal from "@/modules/order-management/components/OrderInformationModal.jsx";
import DashboardLayout from "../../shared/layouts/DashboardLayout";
import CustomTabs from "../../shared/components/CustomTabs";
import CustomTable from "../../shared/components/CustomTable";
import { useDispatch, useSelector } from "react-redux";
import { getExportedOrders, getOrders, getOrderStatus, getStaticOrders, getStatusCount, resetParams, updateParams } from "../../../core/redux/slices/orders/ordersManagementSlice";
import moment from "moment/moment";
import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger, Spinner, Tooltip } from "@heroui/react";
import ImportOrderModal from "../components/ImportOrderModal";
import { RouteNames } from "../../../core/routes/routes";
import { useNavigate, useSearchParams } from "react-router-dom";
import { formatNumber, STATUS_DESCIPTION_REGEX, statusColorMap, statusIconsMap, URL_PARAMS_CONFIG } from "../../../core/utils/functions";
import PriceRenderer from "../../shared/components/PriceRenderer";


const columns = [
    { key: "checkbox", label: "#" },

    { key: "orderNum", label: "Order Nº", },
    { key: "createdAt", label: "Date", },
    { key: "trackingNumber", label: "Tracking Nº", permission: "sellers.trackingnumber" },
    { key: "store", label: "Store", },
    { key: "goodsDescription", label: "Product" },
    { key: "price", label: "Price" },
    { key: "statusDescription", label: "Status" },
    { key: "followup", label: "Followup", permission: "sellers.followup" },
    { key: "fullname", label: "Full Name" },
    { key: "phone", label: "Phone" },
    { key: "country", label: "Country" },
    { key: "city", label: "City" },
    { key: "shippingCompany", label: "Shipping Company", permission: "sellers.shippingcompany" },
    { key: "actions", label: "Actions" },
];




export default function OrderManagement() {
    const [isOrderInformationModalOpen, setOrderInformationModalOpen] = useState(null);
    const [isImportOrderModalOpen, setImportOrderModalOpen] = useState(false);
    const [selectedRows, setSelectedRows] = useState([]);
    const [searchParams, setSearchParams] = useSearchParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { orders, orderStatus, loading, error, exportLoading, params } = useSelector((state) => state.orders);
    const { filterParams } = useSelector((state) => state.content);

    // Initialize states from URL parameters
    const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get('page')) || 1);
    const [selectedTab, setSelectedTab] = useState(searchParams.get('status') || 'all');

    // Initialize params with URL values on component mount
    useEffect(() => {
        const initialParams = {};
        let hasParams = false;

        Object.entries(URL_PARAMS_CONFIG).forEach(([key, config]) => {
            const urlValue = searchParams.get(config.urlKey);
            if (urlValue !== null) {
                hasParams = true;
                // Special handling for array parameters to convert comma-separated string to array
                if (config.reduxKey === 'excludedStatus' || config.reduxKey === 'listStatus') {
                    initialParams[config.reduxKey] = urlValue.split(',');
                }
                // Handle orderNum (k) as a string
                else if (config.reduxKey === 'orderNum') {
                    initialParams[config.reduxKey] = urlValue.toString(); // Ensure orderNum is a string
                }
                else if (config.reduxKey === 'upsell') {
                    initialParams[config.reduxKey] = urlValue;
                }
                else if (config.reduxKey === 'status') {
                    initialParams[config.reduxKey] = urlValue;
                }
                else if (config.reduxKey === 'startDate' || config.reduxKey === 'endDate') {
                    initialParams[config.reduxKey] = urlValue ? moment(urlValue).toISOString() : null;
                } else if (config.reduxKey === 'productReference' || config.reduxKey === 'sortBy' || config.reduxKey === 'paymentMethod' || config.reduxKey === 'originCountry' || config.reduxKey === 'destinationCountry') {
                    initialParams[config.reduxKey] = urlValue;
                }
                else {
                    // Convert page to number, keep others as is
                    initialParams[config.reduxKey] = config.reduxKey === 'page' ? parseInt(currentPage || urlValue) : currentPage || urlValue;
                }
            }
        });
        // Check for the 'orderId' parameter in the URL (independent of filters)
        const orderIdFromUrl = searchParams.get('od'); // 'orderId' is the new URL param
        if (orderIdFromUrl) {
            // Decode the orderId and open the modal
            const decodedOrderId = atob(orderIdFromUrl);
            setOrderInformationModalOpen({ id: decodedOrderId });
        }

        // Always update params if we have any URL values
        if (hasParams && selectedTab === 'all') {
            dispatch(updateParams({
                ...params,
                ...initialParams
            }));
        }
    }, [dispatch, currentPage, selectedTab])

    // Effect to fetch orders when params change
    useEffect(() => {
        if (selectedTab === 'all') {
            dispatch(getOrders());
        }
    }, [dispatch, params, selectedTab]);

    // Update URL when params change
    useEffect(() => {
        const newParams = new URLSearchParams();

        Object.entries(URL_PARAMS_CONFIG).forEach(([key, config]) => {
            const value = params[config.reduxKey];
            // For status, only add if not 'all'
            if (config.reduxKey === 'status') {
                if (value && value !== 'all') {
                    newParams.set(config.urlKey, config.transform(value));
                }
            }
            // For other params, add if they have a value
            else if (value !== null && value !== undefined && value !== '') {
                newParams.set(config.urlKey, config.transform(value));
            }
            // For array parameters (excludedStatus and listStatus), only add if not empty
            else if (config.reduxKey === 'excludedStatus' || config.reduxKey === 'listStatus') {
                if (Array.isArray(value) && value.length > 0) {
                    newParams.set(config.urlKey, config.transform(value));
                }
            }
            // For other params, add if they have a value
            else if (value !== null && value !== undefined && value !== '') {
                newParams.set(config.urlKey, config.transform(value));
            }
        });

        setSearchParams(newParams);
    }, [params, setSearchParams]);


    useEffect(() => {
        dispatch(getOrderStatus());
    }, [dispatch]);

    // Effect for tab changes
    useEffect(() => {
        if (selectedTab !== 'all') {
            dispatch(getStaticOrders({
                tab: selectedTab,
                currentPage: currentPage
            }));
        }
    }, [dispatch, currentPage, selectedTab]);

    // Handle tab change without updating filter params
    const handleTabsChange = (tab) => {
        setSelectedTab(tab);
        setCurrentPage(1);
    }

    // Enhanced page change handler that resets filter parameters
    const handlePageChange = (newPage) => {
        // Reset filter parameters when changing pages
        dispatch(resetParams());
        setCurrentPage(newPage);
    }

    // Function to get status counts for tabs
    const StatusCount = (orderStatus) => {
        return new Promise((resolve) => {
            // Dispatch the action to get status count
            dispatch(getStatusCount(orderStatus))
                .then(action => {
                    // Check if the action was fulfilled
                    if (getStatusCount.fulfilled.match(action)) {
                        resolve(action.payload);
                    } else {
                        resolve('-');
                    }
                })
                .catch(error => {
                    console.error("Error getting status count:", error);
                    resolve('-');
                });
        });
    }



    const renderCell = useCallback((item, columnKey) => {
        const cellValue = item[columnKey];

        switch (columnKey) {
            case "statusDescription":
                return (
                    <Chip
                        className={`capitalize py-1 px-2 h-fit text-white ${statusColorMap[item.status.toLowerCase().replace(' ', '')]}`}
                        variant="solid">
                        {item.statusDescription.match(STATUS_DESCIPTION_REGEX
                        ) ? <Tooltip color='primary' content={<div className="px-1 py-2 max-w-[300px]">
                            <div className="text-small font-bold">{item.statusDescription.match(STATUS_DESCIPTION_REGEX
                            )[2]}</div>

                        </div>}>

                            <div className="flex flex-row py-2 justify-evenly gap-1 items-center text-black dark:text-white">

                                <span className="inline-block">
                                    {statusIconsMap[item.status.toLowerCase().replace(' ', '')] && React.createElement(statusIconsMap[item.status.toLowerCase().replace(' ', '')], {
                                        className: "mr-2 ml-1",
                                        size: 20,
                                    })}
                                </span>
                                <span className="inline-block">
                                    {
                                        item.statusDescription.match(STATUS_DESCIPTION_REGEX
                                        )[1]
                                    }
                                </span>

                                {/* <div dangerouslySetInnerHTML={{ __html: item.statusDescription }} /> */}
                            </div>

                        </Tooltip> : <div className="flex flex-row py-2 justify-evenly gap-1 items-center text-black dark:text-white">

                            <span className="inline-block">
                                {statusIconsMap[item.status.toLowerCase().replace(' ', '')] && React.createElement(statusIconsMap[item.status.toLowerCase().replace(' ', '')], {
                                    className: "mr-2 ml-1",
                                    size: 20,
                                })}
                            </span>
                            <span className="inline-block">
                                {
                                    item.statusDescription
                                }
                            </span>


                        </div>}
                    </Chip>
                );
            case "followup":
                return cellValue.status ? (
                    <Chip
                        className={`capitalize py-1 px-2 h-fit text-white ${statusColorMap[cellValue.status.toLowerCase().replace(' ', '')] || ''}`}
                        variant="solid"
                    >
                        <div className="flex flex-row p-2 justify-evenly gap-1 items-center text-black dark:text-white">
                            <span className="inline-block">
                                {statusIconsMap[cellValue.status.toLowerCase().replace(' ', '')] &&
                                    React.createElement(statusIconsMap[cellValue.status.toLowerCase().replace(' ', '')], {
                                        className: "mr-2 ml-1",
                                        size: 20,
                                    })
                                }
                            </span>
                            <span className="inline-block">
                                {cellValue.statusDescription?.split('<')[0] || ''}
                            </span>
                        </div>
                    </Chip>
                ) : null;
            case 'createdAt':
                return <span>{moment(item.createdAt).format('DD/MM/YYYY HH:mm')}</span>;
            case "price":
                return item.goodsValue !== 0 && (
                    <div className="flex items-center justify-center gap-2 p-2 ">
                        {item.goodsValue && <PriceRenderer price={item.goodsValue} additional={item.currency} />}

                    </div>
                );

            case "fullname":
                return <span>{item.consignee?.contact || "-"}</span>;

            case "phone":
                return <span>{item.consignee?.mobileNumber || "-"}</span>;

            case "country":
                return <span>{item.consignee?.country || "-"}</span>;

            case "goodsDescription":
                return (
                    <Tooltip color='primary' content={<div className="px-1 py-2 max-w-[300px]">
                        <div className="text-small font-bold">Products :</div>
                        <div dangerouslySetInnerHTML={{ __html: cellValue }}
                        />
                    </div>}>
                        <div
                            className=" truncate cursor-help max-w-[250px] overflow-hidden text-ellipsis"
                        >
                            {cellValue && cellValue.replace(/<br\s*\/?>/g, ' | ')}
                        </div>
                    </Tooltip>
                );
            case "city":
                return <span>{item.consignee?.city || "-"}</span>;
            case "actions":
                return (
                    <div className="flex flex-row gap-2 justify-center">
                        <Button isIconOnly size="sm" className="rounded-full bg-blue-700" onPress={() => setOrderInformationModalOpen(item)}>
                            <ViewIcon size={18} className="text-white" />
                        </Button>

                    </div>
                );
            default:
                return (
                    <div
                        dangerouslySetInnerHTML={{ __html: cellValue }}
                    />
                );
        }
    }, []);

    const gotToImport = () => {

        navigate(RouteNames.leadSources, {
            state: { openUploader: true }
        });

    }


    return (
        <>
            <DashboardLayout
                title={`${orders && orders.paginate && orders.paginate.count.toString() ? formatNumber(orders.paginate.count) : '...'} Orders`}
                actions={<Dropdown placement="bottom-start">
                    <DropdownTrigger>
                        <div className="cursor-pointer px-4 py-2 rounded-full flex justify-center items-center gap-2 bg-glb_red text-white">
                            <Link01Icon size={18} /> Action
                        </div>
                    </DropdownTrigger>
                    <DropdownMenu aria-label="User Actions" variant="flat">
                        <DropdownItem key="export">
                            <Button isLoading={exportLoading} className="w-full bg-transparent flex justify-start items-center gap-2" onClick={() => {

                                dispatch(getExportedOrders(selectedRows))
                            }}><DownloadSquare01Icon /> Export</Button></DropdownItem>
                        <DropdownItem key="import" >
                            <Button className="w-full bg-transparent flex justify-start items-center gap-2" onClick={() => gotToImport()}><UploadSquare01Icon /> Import</Button>
                        </DropdownItem>

                    </DropdownMenu>
                </Dropdown >}
            >

                <div className="hide-scrollbar flex gap-2 justify-start items-center overflow-x-auto">
                    <CustomTabs
                        tabLabels={{ all: "All", ...orderStatus }}
                        tabCounts={StatusCount}
                        selectedTab={selectedTab}
                        initialTab="all"
                        onTabChange={(tab) => handleTabsChange(tab)}
                        params={params.status}
                    />
                </div>


                <div className="min-w-full py-4  rounded">
                    <CustomTable
                        clickableRow={true}
                        clickableRowAction={(row) => {
                            setOrderInformationModalOpen(row);
                        }}
                        columns={columns}
                        data={orders.result}  // Pass filtered products based on the view
                        paginate={orders.paginate}
                        renderCell={renderCell}
                        setSelectedRows={setSelectedRows}
                        selectedRows={selectedRows} // Pass selected rows state
                        className="dark:bg-gray-800 dark:text-white" // Dark mode support
                        loading={loading} // Pass loading state
                        error={error} // Pass error state
                        currentPage={currentPage} // Pass current page
                        setCurrentPage={handlePageChange} // Use enhanced page change handler

                    />
                </div>


            </DashboardLayout >
            <OrderInformationModal
                isOpen={isOrderInformationModalOpen}
                onClose={() => setOrderInformationModalOpen(null)}
            />
            <ImportOrderModal
                isOpen={isImportOrderModalOpen}
                setIsOpen={() => setImportOrderModalOpen(false)}
            />

        </>
    );
}