import { BriefcaseDollarIcon, ChartHistogramIcon, DeliveryBox01Icon, Home01Icon, InvoiceIcon, LayoutTable01Icon, ToolsIcon, WarehouseIcon } from "hugeicons-react"
// We'll use the PermissionContext instead of direct getUserPermissions calls

const RouteNames = {
    dashboard: '/dashboard',
    notfound: '/404',
    login: '/login',
    signup: '/signup',
    forgotPassword: '/forgot-password',
    changePassword: '/change-password',
    recoverEmailSent: '/recover-email-sent',
    SourcingRequest: '/sourcing-request',
    allSourcingRequest: '/all-sourcing-request',
    leadSources: '/lead-sources',
    ordersManagements: '/orders-managements',
    allOrders: '/orders',
    followUp: '/follow-up',
    stock: '/products',
    allProducts: '/products',
    invoices: '/invoices',
    serviceInvoices: '/services-invoices',
    sourcingInvoices: '/sourcing-invoices',
    statistics: '/statistics',
    Funds: '/funds',
    productStatistics: '/product-statistics',
    analytics: '/analytics',
    notification: '/notifications',
    settings: '/settings',
    referrals: '/referrals',
    help: '/help',
    tools: '/tools',
    simulator: '/simulator',
}

// Note: Routes that need permission checks will be handled by components using the PermissionContext


const RoutesConfig = [
    {
        name: 'Dashboard',
        path: RouteNames.dashboard,
        showInSidebar: true,
        icon: Home01Icon
    },
    {
        name: 'Login',
        path: RouteNames.login,
        showInSidebar: false,
    },
    {
        name: 'Help',
        path: RouteNames.help,
        showInSidebar: false,
    },
    {
        name: 'Settings',
        path: RouteNames.settings,
        showInSidebar: false,
    },
    {
        name: 'Referrals',
        path: RouteNames.referrals,
        showInSidebar: false,
    },
    {
        name: 'Notifications',
        path: RouteNames.notification,
        showInSidebar: false,
    },
    {
        name: 'Sign Up',
        path: RouteNames.signup,
        showInSidebar: false,
    },
    {
        name: 'Forgot Password',
        path: RouteNames.forgotPassword,
        showInSidebar: false,
    },
    {
        name: 'Change Password',
        path: RouteNames.changePassword,
        showInSidebar: false,
    },
    {
        name: 'Recover Email Sent',
        path: RouteNames.recoverEmailSent,
        showInSidebar: false,
    },
    {
        name: 'Sourcing Request',
        path: RouteNames.SourcingRequest,
        showInSidebar: true,
        icon: LayoutTable01Icon,

    },
    {
        name: 'Lead Sources',
        path: RouteNames.leadSources,
        showInSidebar: true,
        icon: BriefcaseDollarIcon,

    },
    {
        name: 'Orders Management',
        path: RouteNames.ordersManagements,
        showInSidebar: true,
        icon: DeliveryBox01Icon,
        children: [
            {
                name: 'All Orders',
                path: RouteNames.allOrders,
                showInSidebar: true,
            },
            {
                name: 'Follow Up',
                path: RouteNames.followUp,
                // This will be checked by components using the PermissionContext
                showInSidebar: true,
                requiredPermission: 'sellers.followuporders',
            },
        ],
    },
    {
        name: 'Stock',
        path: RouteNames.stock,
        showInSidebar: true,
        icon: WarehouseIcon,
        children: [
            {
                name: 'All Products',
                path: RouteNames.allProducts,
                showInSidebar: true,
            },
        ],
    },
    {
        name: 'NotFound',
        path: RouteNames.notfound,
        showInSidebar: false
    },

    {
        name: 'Invoices',
        path: RouteNames.invoices,
        showInSidebar: true,
        icon: InvoiceIcon,
        children: [
            {
                name: 'Service Invoices',
                path: RouteNames.serviceInvoices,
                showInSidebar: true,
            },
            {
                name: 'Sourcing Invoices',
                path: RouteNames.sourcingInvoices,
                showInSidebar: true,
            },
        ],
    },
    {
        name: 'Statistics',
        path: RouteNames.statistics,
        showInSidebar: true,
        icon: ChartHistogramIcon,
        children: [
            {
                name: 'Funds',
                path: RouteNames.Funds,
                showInSidebar: false,
            },
            {
                name: 'Product Statistics',
                path: RouteNames.productStatistics,
                showInSidebar: true,
            },
            {
                name: 'Analytics',
                path: RouteNames.analytics,
                showInSidebar: false,
            },
        ],
    },
    {
        name: 'Tools',
        path: RouteNames.tools,
        showInSidebar: true,
        icon: ToolsIcon,
        children: [
            {
                name: 'Simulator',
                path: RouteNames.simulator,
                showInSidebar: true,
            },
        ],
    },
]

export { RouteNames, RoutesConfig };
