import { HeroUIProvider } from "@heroui/system";
import AuthProviderWrapper from "./core/providers/AuthProviderWrapper";
import ThemeProvider from "@/core/providers/ThemeContext.jsx";
import { PermissionProvider } from "./core/providers/PermissionContext";
import RoutersWrapper from "./core/routes";
import { Button } from "@heroui/button";
import { Toaster } from "sonner";
import { useEffect } from "react";
import { getProfile } from "./core/redux/slices/profile/profileSlice";
import { useDispatch } from "react-redux";

export default function App() {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(getProfile())
    }, [dispatch])


    return (
        <AuthProviderWrapper>
            <ThemeProvider>
                <PermissionProvider>
                    <HeroUIProvider>
                        <Toaster closeButton richColors position="bottom-center" />
                        <RoutersWrapper />
                    </HeroUIProvider>
                </PermissionProvider>
            </ThemeProvider>
        </AuthProviderWrapper >
    )
}