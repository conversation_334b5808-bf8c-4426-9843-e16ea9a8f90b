import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { currenciestUrl, invoicesStatusUrl, modelsUrl, processModeUrl, serviceInvoicesListUrl, shippingMethodsUrl } from "../URLs";
import { getToken } from "../../../services/TokenHandler";
import axios from "axios";


const initialState = {
    currencies: [],
    models: [],
    loading: false,
    error: null,
};

// Async thunk for installing Shopify app
export const getCurrencies = createAsyncThunk(
    "currencies/get",
    async (_, { rejectWithValue }) => {
        try {


            const response = await axios.get(currenciestUrl,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }
            return response.data.result;


        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);

// Async thunk for installing Shopify app
export const getModels = createAsyncThunk(
    "models/get",
    async (_, { rejectWithValue }) => {
        try {


            const response = await axios.get(modelsUrl,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching models' });
            }
            return response.data.result;



        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);
// Async thunk for installing Shopify app
export const getShippingMethods = createAsyncThunk(
    "shippingMethods/get",
    async (_, { rejectWithValue }) => {
        try {


            const response = await axios.get(shippingMethodsUrl,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching shippingMethods' });
            }
            return response.data.result;



        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);
// Async thunk for installing Shopify app
export const getProcessMode = createAsyncThunk(
    "processMode/get",
    async (_, { rejectWithValue }) => {
        try {


            const response = await axios.get(processModeUrl,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching processMode' });
            }
            return response.data.result;



        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);



const invoiceSlice = createSlice({
    name: 'general',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(getCurrencies.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getCurrencies.fulfilled, (state, action) => {
                state.loading = false;
                state.currencies = action.payload;
            })
            .addCase(getCurrencies.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(getModels.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getModels.fulfilled, (state, action) => {
                state.loading = false;
                state.models = action.payload;
            })
            .addCase(getModels.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })


    },
});

export default invoiceSlice.reducer;