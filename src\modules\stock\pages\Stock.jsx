import {
  Add01Icon,
  Delete02Icon,
  DeletePutBackIcon,
  DeleteThrowIcon,
  PencilEdit01Icon,
  ViewIcon
} from "hugeicons-react";
import { But<PERSON> } from "@heroui/button";
import { useCallback, useMemo, useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import ViewProductModal from "@/modules/stock/components/ViewProductModal.jsx";
import DashboardLayout from "../../shared/layouts/DashboardLayout";
import CustomTabs from "../../shared/components/CustomTabs";
import CustomTable from "../../shared/components/CustomTable";
import AddProductModal from "../components/AddProductModal";
import EditProductModal from "../components/EditProductModal";
import { DeleteProduct, fetchProducts, fetchProductsCategories, fetchProductsTypes, getProductArchive, UnarchiveProduct, updateParams } from "../../../core/redux/slices/stock/products/productsSlice";
import { Popover, PopoverContent, PopoverTrigger, Spinner, Tooltip } from "@heroui/react";
import { productListUrl } from "../../../core/redux/slices/URLs";
import axios from "axios";
import { getToken } from "../../../core/services/TokenHandler";
import { toast } from "sonner";
import { param } from "framer-motion/client";
import { formatNumber } from "../../../core/utils/functions";

const columns = [
  // { key: "checkbox", label: "#", w: 'w-[60px]' },
  { key: "name", label: "Product" },
  { key: "arabicName", label: "Arabic Name" },
  { key: "sku", label: "SKU" },
  { key: "productType", label: "Type" },
  { key: "category", label: "Category" },
  { key: "actions", label: "Actions", w: 'w-[10%]' },
];

export default function Stock() {
  const dispatch = useDispatch();

  // Get products from the Redux state
  const { products, params, loading, error, productsTypes, loadingDelete, productsCategories, loadingCategory, loadingType } = useSelector((state) => state.products);

  const [isViewProductModalOpen, setIsViewProductModalOpen] = useState(false);
  const [isAddProductModalOpen, setIsAddProductModalOpen] = useState(false);
  const [isEditProductModalOpen, setIsEditProductModalOpen] = useState(false);
  const [editedProduct, setEditedProduct] = useState(null);
  const [selectedTab, setSelectedTab] = useState('active');
  const [selectedRows, setSelectedRows] = useState([]);
  const rowsPerPage = 10;

  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    dispatch(fetchProducts());
  }, [dispatch, params])


  useEffect(() => {
    dispatch(fetchProductsTypes())
    dispatch(fetchProductsCategories())

  }, [dispatch])

  useEffect(() => {
    dispatch(updateParams({ ...{ page: currentPage } }))

  }, [dispatch, currentPage])





  const handleDeleteProduct = async (productId) => {


    const result = await dispatch(DeleteProduct({ id: productId }))
    if (DeleteProduct.fulfilled.match(result)) {
      dispatch(updateParams({ ...{ page: currentPage, archive: 0 } }))
    }
  }
  const handleUnarchiveProduct = async (productId) => {


    const result = await dispatch(UnarchiveProduct({ id: productId }))
    if (UnarchiveProduct.fulfilled.match(result)) {
      dispatch(updateParams({ ...{ page: currentPage, archive: 1 } }))
    }
  }




  // Filter products based on status.
  // Since our products are mapped with a default status of "Active",
  // the "Active" tab will show all products, while the "Deleted" tab will be empty.



  const renderCell = useCallback((item, columnKey) => {
    const cellValue = item[columnKey];

    switch (columnKey) {
      case "id":
        return <span>{item.id}</span>;
      case "name":
        return <span className="max-w-[300px] truncate" >{item.name || "-"}</span>;
      case "arabicName":
        return <span>{item.arabicName || "-"}</span>;
      case "sku":
        return <span>{item.sku || "-"}</span>;
      case "productType":
        return <span>{loadingType ? <Spinner size="sm" /> : productsTypes[item.productType] || "-"}</span>;
      case "category":
        return <span>{loadingCategory ? <Spinner size="sm" /> : productsCategories[item.category] || "-"}</span>;
      case "actions":
        return (
          <div className="flex flex-row gap-2 justify-center">
            <Button isIconOnly size="sm" className="rounded-full bg-glb_blue"
              onClick={() => { setEditedProduct(item); setIsViewProductModalOpen(true) }}>
              <ViewIcon size={18} className="text-white" />
            </Button>
            {params.archive === 0 && <Button
              onClick={() => {
                setEditedProduct(item); setIsEditProductModalOpen(true);
              }}
              isIconOnly size="sm" className="rounded-full bg-glb_red">
              <PencilEdit01Icon size={18} className="text-white" />
            </Button>}

            <Popover
              showArrow
              backdrop="opaque"
              placement="right"
            >
              <PopoverTrigger>
                <Button isLoading={loadingDelete}
                  isIconOnly size="sm" className="rounded-full bg-gray-500">
                  <Tooltip content={params.archive === 1 ? 'Unarchive' : 'Archive'}>
                    <div>
                      {params.archive === 1 ? <DeleteThrowIcon size={18} className="text-white" /> : <DeletePutBackIcon size={18} className="text-white" />}
                    </div></Tooltip>
                </Button>
              </PopoverTrigger>
              <PopoverContent>
                {(titleProps) => (
                  <div className="px-1 py-2 ">
                    <h3 className="text-small font-bold mb-2 text-glb_red" >
                      Product Archiving
                    </h3>
                    <hr className="my-1 border-black/30 dark:border-white/30" />
                    <h6 className="text-small font-normal my-2 w-[90%] mx-auto " >
                      {`Are you sure you want to ${params.archive === 1 ? 'unarchive' : 'archive'} the product ${item.name}?`}
                    </h6>
                    <hr className="my-1 border-black/30 dark:border-white/30" />
                    <div className="flex justify-between items-center gap-2 mt-2">
                      <span size="sm" className="text-gray-500">
                        ESC
                      </span>
                      <Button isLoading={loadingDelete} size="sm" color="danger" onClick={() => {
                        { params.archive === 1 ? handleUnarchiveProduct(item.id) : handleDeleteProduct(item.id); }
                      }}>
                        {params.archive === 1 ? 'Unarchive' : 'Archive'}
                      </Button>
                    </div>
                  </div>
                )}
              </PopoverContent>
            </Popover>
          </div>
        );
      default:
        return cellValue || "-";
    }
  }, [loadingType, loadingCategory, params.archive]);

  const StatusCount = async (status) => {

    const result = await dispatch(getProductArchive(status))
    if (getProductArchive.fulfilled.match(result)) {

      return result.payload;

    } else
      return '-';
  }

  const handleTabsChange = async (tab) => {
    dispatch((updateParams({ ...{ archive: tab === 'archived' ? 1 : 0 } })))

  }




  return (
    <DashboardLayout
      title={`${products && products.paginate && products.paginate.count.toString() ? formatNumber(products.paginate.count) : '...'} Products`}
      actions={
        <div className="flex flex-row gap-2 flex-1 justify-end">
          <Button color="default" onClick={() => setIsAddProductModalOpen(true)} className="rounded-full bg-glb_blue text-white">
            <Add01Icon size={18} /> New Product
          </Button>
        </div>
      }
    >
      <div>
        <CustomTabs
          tabLabels={{ active: 'Active', archived: 'Archived' }}
          tabCounts={StatusCount}
          selectedTab={params.archive ? 'archived' : "active"}
          onTabChange={(tab) => handleTabsChange(tab)}
          onChnageState={params}
        />

      </div>


      <div className="min-w-full py-4 rounded">
        <CustomTable
          clickableRow={true}
          clickableRowAction={(row) => {
            { setEditedProduct(row); setIsViewProductModalOpen(true) };
          }}
          columns={columns}
          data={products.result}  // Use the filtered products from the API
          paginate={products.paginate}
          renderCell={renderCell}
          setSelectedRows={setSelectedRows}
          selectedRows={selectedRows}
          rowsPerPage={rowsPerPage}
          className="dark:bg-gray-800 dark:text-white"
          loading={loading} // Pass loading state
          error={error} // Pass error state
          currentPage={currentPage} // Pass current page
          setCurrentPage={setCurrentPage} // Pass setCurrentPage function
        />
      </div>

      <ViewProductModal
        isOpen={isViewProductModalOpen}
        onClose={() => setIsViewProductModalOpen(false)}
        productId={editedProduct?.id}
        productTypes={productsTypes}
        productCategories={productsCategories}
      />
      <AddProductModal
        productsTypes={productsTypes}
        productsCategories={productsCategories}
        isOpen={isAddProductModalOpen}
        onClose={() => { setEditedProduct(null); setIsAddProductModalOpen(false) }}

      />
      <EditProductModal
        productsTypes={productsTypes}
        productsCategories={productsCategories}
        editedProduct={editedProduct}
        isOpen={isEditProductModalOpen}
        onClose={() => { setEditedProduct(null); setIsEditProductModalOpen(false); }}
      />
    </DashboardLayout>
  );
}
