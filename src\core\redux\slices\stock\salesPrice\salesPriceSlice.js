// src/core/redux/slices/stock/salesPrice/salesPriceSlice.js

import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import {
  getProductUrl,
  createSalesPriceUrl,
  updateSalesPriceUrl,
  deleteSalesPriceUrl,
} from "../../URLs";
import { getToken } from "../../../../services/TokenHandler";
import { toast } from "sonner";

// -----------------------------------------
// Thunks
// -----------------------------------------
export const fetchSalesPrices = createAsyncThunk(
  "salesPrice/fetchSalesPrices",
  async (productId, { rejectWithValue }) => {
    try {
      const response = await axios.get(getProductUrl + productId, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return response.data?.result?.salesPrices || [];
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to fetch sales prices"
      });
    }
  }
);

export const createSalesPrice = createAsyncThunk(
  "salesPrice/createSalesPrice",
  async ({ productId, body }, { rejectWithValue }) => {
    try {
      const response = await axios.post(createSalesPriceUrl + productId, body, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return response.data; // e.g., { result: { ...updatedProduct } }
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to create sales price"
      });
    }
  }
);

export const updateSalesPrice = createAsyncThunk(
  "salesPrice/updateSalesPrice",
  async ({ productId, salesPriceId, body }, { rejectWithValue }) => {
    try {
      console.log(productId, salesPriceId, body);

      const token = getToken();
      const url = updateSalesPriceUrl + productId + "/" + salesPriceId;
      const response = await axios.put(url, body, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data; // e.g., { result: { ...updatedProduct } }
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to update sales price"
      });
    }
  }
);

export const deleteSalesPrice = createAsyncThunk(
  "salesPrice/deleteSalesPrice",
  async ({ productId, salesPriceId }, { rejectWithValue }) => {
    try {
      const token = getToken();
      const url = deleteSalesPriceUrl + productId + "/" + salesPriceId;
      const response = await axios.delete(url, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data; // e.g., { result: { ... } } or { response: "success" }
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to delete sales price"
      });
    }
  }
);

// -----------------------------------------
// Initial State
// -----------------------------------------
const initialState = {
  salesPrices: [],
  loading: false,
  error: null,
};

// -----------------------------------------
// Helper to map each item from API
// -----------------------------------------
function mapApiSalesPrice(sp) {
  return {
    id: sp.id,
    name: sp.name,
    paidQuantity: sp.quantityPaid,
    freeQuantity: sp.quantityFree,
    prices: sp.prices || [],
  };
}

// -----------------------------------------
// Slice
// -----------------------------------------
const salesPriceSlice = createSlice({
  name: "salesPrice",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // -----------------------------
      // fetchSalesPrices
      // -----------------------------
      .addCase(fetchSalesPrices.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSalesPrices.fulfilled, (state, action) => {
        state.loading = false;

        state.salesPrices = action.payload.map(mapApiSalesPrice);
      })
      .addCase(fetchSalesPrices.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch sales prices";
      })
      // -----------------------------
      // createSalesPrice
      // -----------------------------
      .addCase(createSalesPrice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSalesPrice.fulfilled, (state, action) => {
        state.loading = false;
        const updatedProduct = action.payload?.result;
        if (updatedProduct && Array.isArray(updatedProduct.salesPrices)) {
          state.salesPrices = updatedProduct.salesPrices.map(mapApiSalesPrice);
        }
        toast.success('Sales price created succefully')
      })
      .addCase(createSalesPrice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to create sales price";
      })
      // -----------------------------
      // updateSalesPrice
      // -----------------------------
      .addCase(updateSalesPrice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSalesPrice.fulfilled, (state, action) => {
        state.loading = false;
        const updatedProduct = action.payload?.result;
        if (updatedProduct && Array.isArray(updatedProduct.salesPrices)) {
          state.salesPrices = updatedProduct.salesPrices.map(mapApiSalesPrice);
        }
        toast.success('Sales price updated succefully')
      })
      .addCase(updateSalesPrice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to update sales price";
      })
      // -----------------------------
      // deleteSalesPrice
      // -----------------------------
      .addCase(deleteSalesPrice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSalesPrice.fulfilled, (state, action) => {
        state.loading = false;
        const updatedProduct = action.payload?.result;
        if (updatedProduct && Array.isArray(updatedProduct.salesPrices)) {
          state.salesPrices = updatedProduct.salesPrices.map(mapApiSalesPrice);
        } else {
          const { salesPriceId } = action.meta.arg;
          state.salesPrices = state.salesPrices.filter(
            (sp) => sp.id !== salesPriceId
          );
        }
      })
      .addCase(deleteSalesPrice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to delete sales price";
      });
  },
});

export default salesPriceSlice.reducer;
