import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { createProductUrl, deleteProductUrl, getProductCategoriesUrl, getProductInventoryUrl, getProductTypeUrl, getProductUrl, getUpdateProductUrl, MainDomain, productsListUrl, unarchiveProductUrl } from '../../URLs';
import { getToken } from '../../../../services/TokenHandler';
import { toast } from 'sonner';

// Initial state
const initialState = {
  products: [],
  productsTypes: [],
  productsCategories: [],
  params: {
    archive: 0,
    keyword: null,
    sku: null,
    productType: null,
    category: null,
    page: 1,
  },
  paramsCount: 0,
  loading: false,
  loadingType: false,
  loadingDelete: false,
  loadingCategory: false,
  error: null,
};



export const updateParams = createAsyncThunk(
  "products/updateParams",
  async (newParams, { getState, rejectWithValue }) => {
    try {
      Object.keys(newParams).forEach(key => {
        if (newParams[key] === '') {
          newParams[key] = null;
        }
      });
      const { params } = getState().products;
      if (!newParams.hasOwnProperty('page')) {
        newParams.page = 1;
      }
      return { ...params, ...newParams };
    } catch (error) {
      return rejectWithValue("Failed to update params");
    }
  }
);

export const resetParams = createAsyncThunk(
  "products/resetParams",
  async (_, { getState, rejectWithValue }) => {
    try {
      const { params } = getState().orders;
      const resetParams = Object.keys(params).reduce((acc, key) => {
        acc[key] = null;
        return acc;
      }, {});
      return resetParams;
    } catch (error) {
      return rejectWithValue("Failed to reset params");
    }
  }
);



// Async thunk for fetching products
export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { params } = getState().products;
      let query = `${productsListUrl}?page=${params.page}`;

      // Add filter parameters to the query
      params.archive !== undefined && (query += `&archive=${params.archive}`);
      params.keyword && (query += `&keyword=${encodeURIComponent(params.keyword)}`);
      params.sku && (query += `&sku=${encodeURIComponent(params.sku)}`);
      params.productType && (query += `&productType=${encodeURIComponent(params.productType)}`);
      params.category && (query += `&category=${encodeURIComponent(params.category)}`);

      console.log('Fetching products with query:', query);

      const response = await axios.get(query,
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        }
      );

      if (response.data.response !== 'success') {
        return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching products' });
      }
      return response.data;
    } catch (error) {

      return rejectWithValue({ status: error.response?.status, message: error.response?.data || 'Something went wrong' });
    }
  }
);

// Async thunk for installing Shopify app
export const getProductArchive = createAsyncThunk(
  "getProductArchive/get",
  async (status, { getState, dispatch, rejectWithValue }) => {


    try {
      const { params } = getState().products;
      let query = `${productsListUrl}?count=1&page=${params.page}&archive=${status === 'archived' ? 1 : 0}`;

      const response = await axios.get(query, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });

      if (response.data.response !== 'success') {
        return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
      }

      return response.data.result;
    } catch (error) {
      return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
    }
  }
);
// Async thunk for fetching products
export const fetchProductDetails = createAsyncThunk(
  'products/fetchProductsDetails',
  async ({ id }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${getProductUrl}${id}`,
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        }
      );

      if (response.data.response !== 'success') {
        return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching products' });
      }
      return response.data.result;
    } catch (error) {

      return rejectWithValue({ status: error.response?.status, message: error.response?.data || 'Something went wrong' });
    }
  }
);

// Async thunk for fetching products
export const fetchProductInventory = createAsyncThunk(
  'products/fetchProductInventory',
  async ({ id }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${getProductInventoryUrl}${id}`,
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        }
      );

      if (response.data.response !== 'success') {
        return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching products' });
      }
      console.log(response.data);
      return response.data.result;

    } catch (error) {

      return rejectWithValue({ status: error.response?.status, message: error.response?.data || 'Something went wrong' });
    }
  }
);

// Async thunk for fetching products
export const DeleteProduct = createAsyncThunk(
  'products/deleteProduct',
  async ({ id }, { rejectWithValue, getState }) => {
    try {
      const response = await axios.put(`${deleteProductUrl}${id}`,
        {}, // Empty body
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        }
      );

      if (response.data.response !== 'success') {
        toast.error(response.data.message)
        return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching products' });
      }
      return id; // Return just the ID instead of the entire response
    } catch (error) {
      console.log("Error deleting product:", error);

      return rejectWithValue({ status: error.response?.status, message: error.response?.data || 'Something went wrong' });
    }
  }
);
// Async thunk for fetching products
export const UnarchiveProduct = createAsyncThunk(
  'products/unarchiveProduct',
  async ({ id }, { rejectWithValue, getState }) => {
    try {
      const response = await axios.put(`${unarchiveProductUrl}${id}`,
        {}, // Empty body
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        }
      );

      if (response.data.response !== 'success') {
        toast.error(response.data.message)
        return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching products' });
      }
      return id; // Return just the ID instead of the entire response
    } catch (error) {
      console.log("Error unarchive product:", error);

      return rejectWithValue({ status: error.response?.status, message: error.response?.data || 'Something went wrong' });
    }
  }
);


// Async thunk for updating products
export const createProduct = createAsyncThunk(
  'products/createProduct',
  async ({ formData }, { rejectWithValue }) => {
    try {
      const formDataToSend = new FormData();
      formDataToSend.append('_method', 'POST');
      formDataToSend.append('name', formData.productName || '');
      formDataToSend.append('arabicName', formData.arabicName || '');
      formDataToSend.append('productType', formData.productType || '');
      formDataToSend.append('category', formData.category || '');
      formDataToSend.append('descriptionCallcenter', formData.noteForCallCenter || '');
      formDataToSend.append('sku', formData.sku || '');
      formDataToSend.append('weight', formData.weight || '');
      formDataToSend.append('width', formData.width || '');
      formDataToSend.append('height', formData.height || '');
      formDataToSend.append('length', formData.length || '');
      formDataToSend.append('hscode', formData.hscode || '');
      formDataToSend.append('productVideo', formData.productVideo || '');
      formDataToSend.append('productLink', formData.productLink || '');

      if (formData.image) {
        formDataToSend.append('image', formData.image);
      }


      const response = await axios.post(`${createProductUrl}`, formDataToSend, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.response !== 'success') {
        return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching products' });
      }
      return response.data.result;
    } catch (error) {

      return rejectWithValue({ status: error.response?.status, message: error.response?.data.message || 'Something went wrong' });
    }
  }
);

// Async thunk for updating products
export const UpdateProduct = createAsyncThunk(
  'products/UpdateProduct',
  async ({ product }, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('_method', 'POST');
      formData.append('name', product.name || '');
      formData.append('arabicName', product.arabicName || '');
      formData.append('productType', product.productType || '');
      formData.append('category', product.category || '');
      formData.append('descriptionCallcenter', product.descriptionCallcenter || '');
      formData.append('sku', product.sku || '');
      formData.append('weight', product.weight || '');
      formData.append('width', product.width || '');
      formData.append('height', product.height || '');
      formData.append('length', product.length || '');
      formData.append('hscode', product.hscode || '');
      formData.append('productVideo', product.productVideo || '');
      formData.append('productLink', product.productLink || '');

      if (product.image) {
        formData.append('image', product.image);
      }


      const response = await axios.post(`${getUpdateProductUrl}${product.id}`, formData, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.response !== 'success') {
        return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching products' });
      }


      return response.data.result;
    } catch (error) {
      console.log(error);

      return rejectWithValue({ status: error.response?.status, message: error.response?.data.message || 'Something went wrong' });
    }
  }
);



// Async thunk for fetching products
export const fetchProductsTypes = createAsyncThunk(
  'products/fetchProductsType',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${getProductTypeUrl}`,
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        }
      );

      if (response.data.response !== 'success') {
        return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching products types' });
      }

      return response.data.result;
    } catch (error) {

      return rejectWithValue({ status: error.response?.status, message: error.response?.data || 'Something went wrong' });
    }
  }
);

// Async thunk for fetching products
export const fetchProductsCategories = createAsyncThunk(
  'products/fetchProductsCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${getProductCategoriesUrl}`,
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        }
      );

      if (response.data.response !== 'success') {
        return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching products types' });
      }

      console.log(response.data.result);

      return response.data.result;
    } catch (error) {

      return rejectWithValue({ status: error.response?.status, message: error.response?.data || 'Something went wrong' });
    }
  }
);



const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    // Synchronous reducers for direct state updates
    updateParamsSync: (state, action) => {
      state.params = { ...state.params, ...action.payload };

      // Calculate paramsCount
      let count = 0;
      if (state.params.keyword) count++;
      if (state.params.sku) count++;
      if (state.params.productType) count++;
      if (state.params.category) count++;
      if (state.params.archive !== 0) count++;

      state.paramsCount = count;
    },
    resetParamsSync: (state) => {
      state.params = initialState.params;
      state.paramsCount = 0;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading = false;

        state.products = action.payload;

      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch products';
      })
      .addCase(createProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createProduct.fulfilled, (state, action) => {
        state.loading = false;
        if (state.products.result) {
          state.products.result.unshift(action.payload);
        } else {
          state.products.result = [action.payload];
        }
        toast.success("Product created successfully")
        // state.products = action.payload;

      })
      .addCase(createProduct.rejected, (state, action) => {
        state.loading = false;
        // state.error = action.payload || 'Failed to create products';
        console.log(action.payload);

        const errorMessages = action.payload?.message?.split(',') || action.payload?.split(',') || [];
        errorMessages.forEach((message) => toast.error(message));
      })
      .addCase(UpdateProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(UpdateProduct.fulfilled, (state, action) => {
        state.loading = false;
        if (state.products.result) {
          state.products.result.unshift(action.payload);
        } else {
          state.products.result = [action.payload];
        }

        toast.success("Product updated successfully")
      })
      .addCase(UpdateProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch products';
      })
      .addCase(DeleteProduct.pending, (state) => {
        state.loadingDelete = true;

      })
      .addCase(DeleteProduct.fulfilled, (state, action) => {
        state.loadingDelete = false;
        const productId = action.payload;

        // Find the product before updating it
        const product = state.products.result.find(p => p.id === productId);


        // Use the stored product name in the toast
        toast.success(`Product: ${product.name}`, { description: "Product archived successfully" })
        // Mark the product as archived instead of removing it from the state
        const updatedProducts = state.products.result.filter((p) => p.id !== productId);

        state.products.result = updatedProducts;
      })
      .addCase(DeleteProduct.rejected, (state, action) => {
        state.loadingDelete = false;
      })
      .addCase(UnarchiveProduct.pending, (state) => {
        state.loadingDelete = true;

      })
      .addCase(UnarchiveProduct.fulfilled, (state, action) => {
        state.loadingDelete = false;
        const productId = action.payload;

        // Find the product before updating it
        const product = state.products.result.find(p => p.id === productId);


        // Use the stored product name in the toast
        toast.success(`Product: ${product.name}`, { description: "Product unarchived successfully" })
        // Mark the product as archived instead of removing it from the state
        const updatedProducts = state.products.result.filter((p) => p.id !== productId);

        state.products.result = updatedProducts;
      })
      .addCase(UnarchiveProduct.rejected, (state, action) => {
        state.loadingDelete = false;
      })
      .addCase(fetchProductsTypes.fulfilled, (state, action) => {
        state.loadingType = false;
        state.productsTypes = action.payload;
      })
      .addCase(fetchProductsTypes.rejected, (state, action) => {
        state.loadingType = false;
      })
      .addCase(fetchProductsTypes.pending, (state, action) => {
        state.loadingType = true;
      })
      .addCase(fetchProductsCategories.rejected, (state, action) => {
        state.loadingCategory = false;
      })
      .addCase(fetchProductsCategories.pending, (state, action) => {
        state.loadingCategory = true;
      })
      .addCase(fetchProductsCategories.fulfilled, (state, action) => {
        state.productsCategories = action.payload;
        state.loadingCategory = false;
      })
      .addCase(updateParams.pending, (state) => {
        state.paramsLoading = true;
        state.error = null;
      })
      .addCase(updateParams.fulfilled, (state, action) => {
        state.paramsLoading = false;
        state.params = action.payload;


      })
      .addCase(updateParams.rejected, (state, action) => {
        state.paramsLoading = false;
        state.error = action.payload;
      });
  },
});

export const { updateParamsSync, resetParamsSync } = productsSlice.actions;

export default productsSlice.reducer;
