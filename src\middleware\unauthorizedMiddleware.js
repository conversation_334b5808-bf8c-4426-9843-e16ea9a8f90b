import { logoutUser } from "../core/redux/slices/authSlice";


const unauthorizedMiddleware = (store) => (next) => (action) => {
  // Check if the action is a rejected async thunk
  if (
    action.type.endsWith('/rejected') &&
    action.payload &&
    action.payload.status === 401
  ) {
    // Dispatch logout action
    store.dispatch(logoutUser());
  }

  return next(action);
};

export default unauthorizedMiddleware;
