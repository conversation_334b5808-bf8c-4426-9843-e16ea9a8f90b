import { Link } from "react-router-dom";
import codPowerGroupLogo from "@shared/assets/images/cod-power-group-logo.svg";
import codPowerGroupLogoDark from "@shared/assets/images/cod-power-group-logo-dark.svg";

const SidebarHeader = ({ currentTheme }) => {
    return (
        <div className="flex justify-between my-4 px-3">
            <Link to="/" className="flex items-center">
                <img
                    src={currentTheme === 'light' ? codPowerGroupLogo : codPowerGroupLogoDark}
                    alt="power group world logo"
                    className="w-20"
                />
            </Link>
        </div>
    );
};

export default SidebarHeader;