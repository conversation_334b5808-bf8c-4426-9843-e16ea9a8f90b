import {
  Calendar01Icon,
  CallBlocked02Icon,
  CallEnd01Icon,
  CallOutgoing04Icon,
  CancelCircleHalfDotIcon,
  CancelCircleIcon,
  CheckmarkCircle02Icon,
  Clock01Icon,
  Copy01Icon,
  DeliveredSentIcon,
  DeliveryTruck01Icon,
  Files01Icon,
  Layers01Icon,
  PackageIcon,
  RepeatIcon,
  ReturnRequestIcon,
  SentIcon,
  Settings02Icon,
  TestTube01Icon,
  UserAdd02Icon,
  XVariableCircleIcon,
} from "hugeicons-react";


export const toSlug = (str) => {
  return str
    .toLowerCase() // Convert to lowercase
    .trim() // Remove any leading/trailing spaces
    .replace(/&/g, "and") // Replace '&' with 'and'
    .replace(/[^a-z0-9\s-]/g, "") // Remove non-alphanumeric characters except spaces and hyphens
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-"); // Collapse multiple hyphens into one
};
export function copyTextToClipboard(text) {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      console.log("Text copied to clipboard!");
    })
    .catch((err) => {
      console.error("Failed to copy text: ", err);
    });
}

export const isValidString = (str) => {
  return str !== null && str.trim() !== "";
};

export const turnStringIntoDate = (dateString) => {
  // Check if the string is empty or null
  if (!dateString) {
    return null;
  }
  // Split date and time
  const [datePart, timePart] = dateString.split(" ");
  const [day, month, year] = datePart.split(".");
  // const [hours, minutes] = timePart.split(":");

  // Create a Date object (note: month is 0-indexed in JS)
  const dateObj = new Date(year, month - 1, day);
  return dateObj;
};

export const STATUS_DESCIPTION_REGEX =
  /^(.+?)\s*(?:<[^>]+>\s*)?(\d{2}\.\d{2}\.\d{4})/;

export const formatNumber = (value) => {
  // Replace spaces with nothing and commas with dots to handle European-style number format
  value = value?.toString().replace(/\s/g, '').replace(',', '.');

  // Check if the input is a valid number
  if (isNaN(value)) {
    return "0";
  }

  // If fixed is provided, format the number to the specified decimal places
  const numStr = Number.isInteger(Number(value)) ? value.toString() : parseFloat(value).toFixed(2);

  // Use regex to add spaces for thousands separators
  return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, " ");
};


export const urlPattern = new RegExp(
  '^https?:\\/\\/' +                  // protocol
  '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
  '((\\d{1,3}\\.){3}\\d{1,3}))' +    // OR ipv4
  '(\\:\\d+)?' +                     // optional port
  '(\\/[-a-z\\d%_.~+]*)*' +          // path
  '(\\?[;&a-z\\d%_.~+=-]*)?' +       // query string
  '(\\#[-a-z\\d_\\/]*$)?', 'i'       // fragment with slashes allowed
);

// Status configurations
export const statusColorMap = {
  newlead: "bg-[#0085FF30]", // Blue for new leads
  confirmed: "bg-[#00FF2930]", // Green for confirmed
  noanswer: "bg-[#FF004D30]", // Red for no answer
  "no-answer": "bg-[#FF004D30]", // Red for no answer
  canceled: "bg-[#FF000030]", // Bright red for canceled
  schedule: "bg-[#5902E843]", // Purple for scheduled
  scheduled: "bg-[#5902E843]", // Purple for scheduled
  doubleorder: "bg-[#FFA50030]", // Orange for duplicate orders
  duplicateorder: "bg-[#FFA50030]", // Orange for duplicate orders
  placed: "bg-[#FFA50030]", // Orange for duplicate orders
  parked: "bg-[#5902E843]", // Orange for duplicate orders
  shipped: "bg-[#0085FF4D]", // Orange for duplicate orders
  pending: "bg-[#FFFF0030]", // Yellow for pending
  wrongphonenumber: "bg-[#81818130]", // Gray for wrong numbers
  WrongPhoneNumber: "bg-[#81818130]", // Gray for wrong numbers
  test: "bg-[#A020F030]", // Special color for tests
  processing: "bg-[#00BFFF30]", // Light blue for processing
  intransit: "bg-[#0000FF30]", // Blue for in transit
  delivered: "bg-[#00FF0030]", // Bright green for delivered
  delivery: "bg-[#5902E86E]", // Bright green for delivered
  deliveryagain: "bg-[#5902E86E]", // Bright green for delivered
  return: "bg-[#D3233030]", // Dark red for returns
  returntoclient: "bg-[#D3233030]", // Dark red for returns
  rtc: "bg-[#D3233030]", // Dark red for returns
  abandon: "bg-[#81818130]", // Dark red for returns
  callback: "bg-[#0085FF4D]", // Dark red for returns
};

export const statusIconsMap = {
  newlead: UserAdd02Icon,
  "no-answer": CallEnd01Icon,
  confirmed: CheckmarkCircle02Icon,
  noanswer: CallEnd01Icon,
  canceled: XVariableCircleIcon,
  schedule: Calendar01Icon,
  scheduled: Calendar01Icon,
  doubleorder: Files01Icon,
  duplicateorder: Files01Icon,
  pending: Clock01Icon,
  wrongphonenumber: CallBlocked02Icon,
  WrongPhoneNumber: CallBlocked02Icon,
  callback: CallOutgoing04Icon,
  test: TestTube01Icon,
  processing: Settings02Icon,
  intransit: DeliveryTruck01Icon,
  delivered: PackageIcon,
  delivery: RepeatIcon,
  deliveryagain: RepeatIcon,
  return: ReturnRequestIcon,
  abandon: CancelCircleIcon,
  rtc: CancelCircleIcon,
  returntoclient: CancelCircleIcon,
  shipped: DeliveredSentIcon,
  placed: SentIcon,
  parked: Layers01Icon,
};

export
  // Configuration for URL parameters
  const URL_PARAMS_CONFIG = {
    // Current parameters
    page: {
      reduxKey: 'page',
      urlKey: 'page',
      defaultValue: 1,
      transform: (value) => value.toString()
    },
    status: {
      reduxKey: 'status',
      urlKey: 'status',
      defaultValue: 'all',
      transform: (value) => value
    },
    status: {
      reduxKey: 'followupStatus',
      urlKey: 'followups',
      defaultValue: 'all',
      transform: (value) => value
    },
    excludedStatus: {
      reduxKey: 'excludedStatus',
      urlKey: 'ex',
      defaultValue: [],
      transform: (value) => Array.isArray(value) ? value.join(',') : value
    },
    listStatus: {
      reduxKey: 'listStatus',
      urlKey: 'ls',
      defaultValue: [],
      transform: (value) => Array.isArray(value) ? value.join(',') : value
    },
    // Example of future parameters (commented out for now)

    startDate: {
      reduxKey: 'startDate',
      urlKey: 'start',
      defaultValue: null,
      transform: (value) => value
    },
    endDate: {
      reduxKey: 'endDate',
      urlKey: 'end',
      defaultValue: null,
      transform: (value) => value
    },
    productReference: {
      reduxKey: 'productReference',
      urlKey: 'product',
      defaultValue: null,
      transform: (value) => value
    },
    sortBy: {
      reduxKey: 'sortBy',
      urlKey: 'sort',
      defaultValue: null,
      transform: (value) => value
    },
    paymentMethod: {
      reduxKey: 'paymentMethod',
      urlKey: 'payment',
      defaultValue: null,
      transform: (value) => value
    },
    /*consigneeContact: {
            reduxKey: 'consigneeContact',
            urlKey: 'contact',
            defaultValue: null,
            transform: (value) => value
        },
        consigneePhone: {
            reduxKey: 'consigneePhone',
            urlKey: 'phone',
            defaultValue: null,
            transform: (value) => value
        },*/
    originCountry: {
      reduxKey: 'originCountry',
      urlKey: 'oc',
      defaultValue: null,
      transform: (value) => value
    },
    destinationCountry: {
      reduxKey: 'destinationCountry',
      urlKey: 'dc',
      defaultValue: null,
      transform: (value) => value
    },
    orderNum: {
      reduxKey: 'orderNum',
      urlKey: 'k',
      defaultValue: null,
      transform: (value) => value ? value.toString() : '', // Force it to be a string (even if empty)    
    },
    upsell: {
      reduxKey: 'upsell',
      urlKey: 'upsell',
      defaultValue: null,
      transform: (value) => value
    },

  };
