import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import App from './App.jsx'
import { Provider } from 'react-redux'
import './index.css'

import '@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Black.otf';
import '@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Bold.otf';
import '@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Book.otf';
import '@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Light.otf';
import '@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Medium.otf';
import '@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Thin.otf';
import store from './core/redux/store.js'

createRoot(document.getElementById('root')).render(

    <Provider store={store}>
        <App />
    </Provider>
)
