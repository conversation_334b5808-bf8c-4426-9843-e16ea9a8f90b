import Header from "@/modules/dashboard/components/partials/Navbar.jsx";
import { But<PERSON> } from "@heroui/button";
import { useDispatch, useSelector } from "react-redux";
import {
  setShowReSidebar,
  setSidebarEpingled,
} from "../../../core/redux/slices/sidebarSlice";
import { ArrowLeft01Icon } from "hugeicons-react";

import { setOpenFilterModal } from "../../../core/redux/slices/contentSlice";

import OrderFilterModal from "./components/OrderFilterModal";
import InvoiceFilterModal from "./components/InvoiceFilterModal";
import ProductFilterModal from "./components/ProductFilterModal";
import FollowupFilterModal from "./components/FollowupFilterModal";
import DashboardFilterModal from "../../dashboard/components/DashboardFilterModal";
import { useLocation, useNavigate } from "react-router-dom";
import { RouteNames } from "../../../core/routes/routes";
import SourcingFilterModal from "./components/SourcingFilterModal";

export default function DashboardLayout({
  children,
  title,
  actions,
  hasReturnLink = false,
  bgColor = "",
}) {
  // reduuuux
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const sidebarEpingled = useSelector((state) => state.sidebar.sidebarEpingled);
  const isFilterModalOpen = useSelector(
    (state) => state.content.openFilterModal
  );
  const showReSidebar = useSelector((state) => state.sidebar.showReSidebar);
  const filterApplyed = useSelector((state) => state.content.filterApplyed);
  const { filterParams } = useSelector((state) => state.content);

  // Determine which filter modal to show based on the current path
  const isInvoicesPage =
    location.pathname === RouteNames.serviceInvoices ||
    location.pathname === RouteNames.sourcingInvoices;
  const isOrdersPage = location.pathname === RouteNames.allOrders;
  const isFollowupPage = location.pathname === RouteNames.followUp;
  const isProductsPage = location.pathname === RouteNames.allProducts;
  const isDashboardPage = location.pathname === RouteNames.dashboard;
  const isSourcingPage = location.pathname === RouteNames.SourcingRequest;

  const closeFilterModal = () => {
    console.log('in the in the layout');

    // Close the modal and reset its state
    dispatch(setOpenFilterModal(false));

  };

  const openFilterModal = () => {
    dispatch(setOpenFilterModal(true));
  };

  return (
    <>
      <div
        className={`  relative flex flex-col flex-grow min-h-screen overflow-y-scroll w-full ${bgColor} dark:bg-transparent`}>
        {/* ${sidebarEpingled
                    ? "lg:w-[calc(100%-20rem)]"
                    : showSidebar
                        ? "lg:w-[calc(100%-20rem)]"
                        : "lg:w-[calc(100%-3.5rem)]"
                    } */}
        <Header
          epingled={sidebarEpingled}
          setEpingled={(v) => dispatch(setSidebarEpingled(v))}
          showSidebar={showReSidebar}
          setShowSidebar={(v) => dispatch(setShowReSidebar(v))}
          isFilterOpen={isFilterModalOpen}
          SetFilterOpen={openFilterModal}
          filterApplyed={filterApplyed}
          filterParamsSearch={filterParams?.orderNum}
        />
        <div className={`flex-1 flex flex-col w-full `}>
          <div className="flex  flex-row flex-wrap justify-between px-4 md:px-8 items-center gap-4 my-4">
            {hasReturnLink && (
              <Button
                isIconOnly
                variant="light"
                className=""
                onClick={() => navigate(-1)}>
                <ArrowLeft01Icon />
              </Button>
            )}
            <h1 className="text-nowrap text-2xl font-bold flex-1">{title}</h1>
            {actions}
          </div>

          <div className=" flex-1 flex-grow px-2 "> {children}</div>
        </div>

        {/*  Site Footer */}
        <footer className={`  mx-auto my-8 text-center`}>
          <span className="text-sm text-gray-600">
            Copyright © {new Date().getFullYear()}. Power Group World, All rights
            reserved.
          </span>
        </footer>
      </div>

      {isFilterModalOpen && isOrdersPage && (
        <OrderFilterModal
          isOpen={isFilterModalOpen}
          onClose={closeFilterModal}
        />
      )}
      {isFilterModalOpen && isFollowupPage && (
        <FollowupFilterModal
          isOpen={isFilterModalOpen}
          onClose={closeFilterModal}
        />
      )}
      {isFilterModalOpen && isInvoicesPage && (
        <InvoiceFilterModal
          isOpen={isFilterModalOpen}
          onClose={closeFilterModal}
        />
      )}
      {isFilterModalOpen && isProductsPage && (
        <ProductFilterModal
          isOpen={isFilterModalOpen}
          onClose={closeFilterModal}
        />
      )}
      {isFilterModalOpen && isDashboardPage && (
        <DashboardFilterModal
          isOpen={isFilterModalOpen}
          onClose={closeFilterModal}
        />
      )}
      {isFilterModalOpen && isSourcingPage && (
        <SourcingFilterModal
          isOpen={isFilterModalOpen}
          onClose={closeFilterModal}
        />
      )}
      {/* <div className="fixed bottom-4 right-4 z-50 transition-opacity">
                <Button className="rounded-full p-2 h-14 w-14 bg-blue-700 shadow" isIconOnly>
                    <img src={ChatBotMessage} alt="chatbot message" className="" />
                </Button>
            </div> */}
    </>
  );
}
