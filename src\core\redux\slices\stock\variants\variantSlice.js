
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import {
  getProductUrl,
  createSalesPriceUrl,
  updateSalesPriceUrl,
  deleteSalesPriceUrl,
  createVariantsUrl,
  updateVariantseUrl,
  deleteVariantsUrl,
  getAttributesUrl,
  AddAttributeUrl,
  updateAttributeUrl,
  deleteAttributeUrl,
} from "../../URLs";
import { getToken } from "../../../../services/TokenHandler";
import { toast } from "sonner";

// -----------------------------------------
// Thunks
// -----------------------------------------
export const fetchVaraints = createAsyncThunk(
  "variant/fetchVariants",
  async (productId, { rejectWithValue }) => {
    try {
      const response = await axios.get(getProductUrl + productId, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return response.data?.result?.variants || [];
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to fetch variants"
      });
    }
  }
);
export const fetchAttributes = createAsyncThunk(
  "variant/fetchAttributes",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(getAttributesUrl, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      if (response.data.response === "error") {
        return rejectWithValue({ status: response.data?.status, message: response.data.message || "Error fetching attributes" });

      }
      return response.data.result;
    } catch (error) {
      console.log(error);

      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to fetch variants"
      });
    }
  }
);

export const createAttributes = createAsyncThunk(
  "variant/createAttributes",
  async ({ body }, { rejectWithValue }) => {
    try {
      const response = await axios.post(AddAttributeUrl, body, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });


      return response.data.result; // e.g., { result: { ...updatedProduct } }
    } catch (error) {

      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to create vairants"
      });
    }
  }
);

export const updateAttributes = createAsyncThunk(
  "variant/updateAttributes",
  async ({ id, body }, { rejectWithValue }) => {
    try {
      const response = await axios.put(updateAttributeUrl + id, body, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return response.data.result; // e.g., { result: { ...updatedProduct } }
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to create vairants"
      });
    }
  }
);

export const deleteAttributes = createAsyncThunk(
  "variant/deleteAttributes",
  async ({ id }, { rejectWithValue }) => {
    try {
      const response = await axios.delete(deleteAttributeUrl + id, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return id;
    } catch (error) {

      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to create vairants"
      });
    }
  }
);

export const createVaraints = createAsyncThunk(
  "variant/createVariants",
  async ({ productId, body }, { rejectWithValue }) => {
    try {
      const response = await axios.post(createVariantsUrl + productId, { parent: productId.toString(), ...body }, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return response.data; // e.g., { result: { ...updatedProduct } }
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to create vairants"
      });
    }
  }
);

export const addFromAttributes = createAsyncThunk(
  "variant/createVariantsFromAttr",
  async ({ productId, body }, { rejectWithValue }) => {
    try {
      const response = await axios.post(createVariantsUrl + productId + "/combine", { combinations: body }, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return response.data;
    } catch (error) {
      console.log(error);
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to create vairants"
      });
    }
  }
);

export const updateVariant = createAsyncThunk(
  "variant/updateVariant",
  async ({ productId, variantId, body }, { rejectWithValue }) => {
    try {

      const token = getToken();
      const url = updateVariantseUrl + productId + "/" + variantId;
      const response = await axios.put(url, { parent: productId.toString(), ...body }, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data; // e.g., { result: { ...updatedProduct } }
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to update variant"
      });
    }
  }
);

export const deleteVariant = createAsyncThunk(
  "variant/deleteVariant",
  async ({ productId, variantId }, { rejectWithValue }) => {
    try {
      const token = getToken();
      const url = deleteVariantsUrl + productId + "/" + variantId;
      const response = await axios.delete(url, {
        headers: { Authorization: `Bearer ${token}` },
      });

      return { variantId };
    } catch (error) {
      console.log(error);

      return rejectWithValue({

        status: error.response?.status, message:
          error.response?.data || "Failed to delete variant"
      });
    }
  }
);

// -----------------------------------------
// Initial State
// -----------------------------------------
const initialState = {
  variants: [],
  attributes: [],
  loading: false,
  error: null,
};



// -----------------------------------------
// Slice
// -----------------------------------------
const variantSlice = createSlice({
  name: "salesPrice",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder

      .addCase(fetchAttributes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAttributes.fulfilled, (state, action) => {
        state.loading = false;

        state.attributes = action.payload;
      })
      .addCase(fetchAttributes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch sales prices";
      })
      .addCase(createAttributes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createAttributes.fulfilled, (state, action) => {
        state.loading = false;

        state.attributes.unshift(action.payload);
      })
      .addCase(createAttributes.rejected, (state, action) => {
        state.loading = false;

        state.error = action.payload.message || "Failed to fetch sales prices";
        action.payload.message.message.split(',').forEach((message) => toast.error(message));
      })
      .addCase(updateAttributes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateAttributes.fulfilled, (state, action) => {
        state.loading = false;
        state.attributes = state.attributes.map(attr =>
          attr.id === action.payload.id ? action.payload : attr
        );
      })
      .addCase(updateAttributes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch sales prices";
      })
      .addCase(deleteAttributes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteAttributes.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(deleteAttributes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch sales prices";
      })
      // -----------------------------
      // fetchVaraints
      // -----------------------------
      .addCase(fetchVaraints.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVaraints.fulfilled, (state, action) => {
        state.loading = false;

        state.variants = action.payload;
      })
      .addCase(fetchVaraints.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch sales prices";
      })
      // -----------------------------
      // createVaraints
      // -----------------------------
      .addCase(createVaraints.pending, (state) => {
        state.loading = true;
        state.error = null;
      })

      .addCase(createVaraints.fulfilled, (state, action) => {
        state.loading = false;
        const updatedProduct = action.payload?.result;
        if (updatedProduct && Array.isArray(updatedProduct.variants)) {
          state.variants = updatedProduct.variants;
        }
        toast.success('Variant created succefully')
      })
      .addCase(createVaraints.rejected, (state, action) => {
        state.loading = false;

        state.error = action.payload || "Failed to create variants";
      })
      .addCase(addFromAttributes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })

      .addCase(addFromAttributes.fulfilled, (state, action) => {
        state.loading = false;
        const updatedProduct = action.payload?.result;
        if (updatedProduct && Array.isArray(updatedProduct.variants)) {
          state.variants = updatedProduct.variants;
        }
        toast.success('Variants created succefully')
      })
      .addCase(addFromAttributes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to create variants";
      })
      // -----------------------------
      // updateVariant
      // -----------------------------
      .addCase(updateVariant.pending, (state) => {
        state.error = null;
      })
      .addCase(updateVariant.fulfilled, (state, action) => {
        const updatedProduct = action.payload?.result;
        if (updatedProduct && Array.isArray(updatedProduct.variants)) {
          state.variants = updatedProduct.variants;
        }
        toast.success('Variant updated succefully')
      })
      .addCase(updateVariant.rejected, (state, action) => {


        state.error = action.payload || "Failed to update variants";
      })
      // -----------------------------
      // deleteVariant
      // -----------------------------
      .addCase(deleteVariant.pending, (state) => {
        state.error = null;
      })
      .addCase(deleteVariant.fulfilled, (state, action) => {
        // When the delete request is successful, filter out the deleted variant
        state.variants = state.variants.filter(
          (variant) => variant.id !== action.payload.variantId
        );
        state.loading = false;  // Set loading to false after the deletion
        toast.success('Variant deleted succefully')
      })
      .addCase(deleteVariant.rejected, (state, action) => {


        state.error = action.payload || "Failed to delete sales price";
      });
  },
});

export default variantSlice.reducer;
