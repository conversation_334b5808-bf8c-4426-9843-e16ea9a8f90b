import { Doughn<PERSON> } from 'react-chartjs-2';
import { ArcElement, Chart as ChartJS, Legend, Tooltip } from 'chart.js';
import { But<PERSON> } from "@heroui/button";
import { ArrowUpDownIcon, MoreVerticalCircle01Icon } from "hugeicons-react";
import { useRef, useState } from 'react';
import Chart from "react-apexcharts"
import { useThemeProvider } from "@/core/providers/ThemeContext.jsx";


ChartJS.register(ArcElement, Tooltip, Legend);

const TotalFeesChart = () => {
    const data = {
        labels: [
            "Call Confirmation",
            "COD",
            "Upsell",
            "Shipping & Fulfillment",
            "Return",
            "VAT",
        ],
        datasets: [
            {
                label: 'Total',
                data: [1298, 2300, 500, 0, 2500, 0], // Example values, replace with actual data
                backgroundColor: [
                    "#CA45E8", // Call Confirmation - Purple
                    "#3094FC", // COD - Blue
                    "#33C47F", // Upsell - Green
                    "#A2591C", // Shipping & Fulfillment - Brown
                    "#B22121", // Return - Red
                    "#F8CE3A", // VAT - Yellow
                ],
                borderWidth: 0, // Remove border for a cleaner look

                spacing: 0,
                hoverOffset: 40,
                borderRadius: 0,
            },
        ],
    };



    // const options = {
    //     plugins: {
    //         legend: {
    //             display: false,
    //         },
    //         tooltip: {
    //             enabled: true,
    //             backgroundColor: '#1f1f1f',
    //             titleColor: '#fff',
    //             bodyColor: '#fff',
    //             padding: 12,
    //             displayColors: false,
    //             callbacks: {
    //                 label: (context) => {
    //                     return `${context.parsed}%`;
    //                 },
    //                 title: (context) => {
    //                     return context[0].label;
    //                 }
    //             },
    //         },
    //     },
    //     cutout: '75%',
    //     responsive: true,
    //     maintainAspectRatio: true,
    //     hover: {
    //         mode: 'nearest',
    //         intersect: true,
    //     },
    //     animation: {
    //         duration: 200
    //     },
    // };
    const thickness = {
        id: 'thickness', beforeDraw(chart, plugins) {
            let sliceThecknessPixel = [300, 250, 400, 450, 450, 300];
            sliceThecknessPixel.forEach((thickness, index) => {
                chart.getDatasetMeta(0).data[index].outerRadius =
                    (chart.chartArea.width / thickness) * 100
            })

        }
    }
    const chartRef = useRef(null);
    const [centerText, setCenterText] = useState("1,298");

    var options = {
        series: [44, 55, 41, 17, 15],
        plotOptions: {
            pie: {
                donut: {
                    size: '55px',
                    labels: {
                        show: true,
                        total: {
                            show: true,
                            showAlways: true,
                        }
                    }
                }
            }
        }

    };

    const { currentTheme } = useThemeProvider();


    const [SelectedData, setSelectedData] = useState('1298')

    return (
        <div className="w-full p-2 lg:p-6 border bg-white dark:bg-transparent rounded-xl min-h-[450px] border-gray-200 dark:border-zinc-800">
            <div className="">
                <div className="flex justify-between items-center mb-4">
                    <div>
                        <h2 className="text-lg font-semibold">Total Fees</h2>
                        <p className="text-sm text-gray-400">Delivered Rate</p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            className="rounded-full text-xs p-2 md:text-base md:p-4 border-[#444444] border-1"
                        >
                            <ArrowUpDownIcon className="h-4 w-4" />
                            Sort
                        </Button>
                        <Button isIconOnly variant="light" >
                            <MoreVerticalCircle01Icon size={18} />
                        </Button>
                    </div>
                </div>

                <div className="relative w-64 h-64 mx-auto">
                    <div className="absolute inset-0 flex items-center justify-center flex-col">
                        <span className="text-4xl font-bold text-gray-400">$</span>
                        <span className="text-2xl font-bold">{SelectedData}</span>
                    </div>
                    {/* <Doughnut ref={chartRef} data={data} options={options} /> */}
                    <Chart options={{
                        series: [1298, 2300, 500, 0, 2500, 0],
                        legend: {
                            show: false
                        },
                        colors: [
                            "#CA45E8", // Call Confirmation - Purple
                            "#3094FC", // COD - Blue
                            "#33C47F", // Upsell - Green
                            "#A2591C", // Shipping & Fulfillment - Brown
                            "#B22121", // Return - Red
                            "#F8CE3A", // VAT - Yellow
                        ],
                        labels: [
                            "Call Confirmation",
                            "COD",
                            "Upsell",
                            "Shipping & Fulfillment",
                            "Return",
                            "VAT",
                        ],
                        chart: {
                            type: 'donut',
                            foreColor: '#ffffff',
                            events: {

                                dataPointSelection: (event, chartContext, config) => {
                                    const selectedIndex = config.dataPointIndex; // Index of selected slice
                                    //const selectedLabel = config.w.config.labels[selectedIndex]; // Label of selected slice
                                    const selectedValue = config.w.config.series[selectedIndex]; // Value of selected slice
                                    setSelectedData(selectedValue)
                                },
                            }
                        },

                        grid: {
                            borderColor: "#EF3252"
                        },
                        dataLabels: {
                            enabled: false,

                        },
                        plotOptions: {
                            pie: {
                                expandOnClick: false,
                                donut: {
                                    labels: {
                                        show: true,
                                        total: {
                                            show: false,
                                            showAlways: true,
                                            label: '',
                                            formatter: function (w) {

                                                return w.globals.seriesTotals.reduce((a, b) => {
                                                    return `${a}/${b}`
                                                })
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        stroke: {
                            colors: [currentTheme === 'light' ? '#fff' : '#000']
                        },
                        responsive: [{
                            breakpoint: 480,
                            options: {
                                chart: {
                                    width: '100%'
                                },
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }],
                        datasets: [{
                            borderColor: ["#EF3252"]
                        }]
                    }
                    }
                        series={[1298, 2300, 500, 0, 2500, 0]} type='donut' width='100%' height={500} />
                </div>

                <div className="mt-4">
                    <div className="flex flex-row flex-wrap justify-center gap-2">
                        {data.labels.map((label, index) => (
                            <div key={label} className="flex items-center">
                                <div
                                    className="w-3 h-3 mr-2"
                                    style={{ backgroundColor: data.datasets[0].backgroundColor[index] }}
                                />
                                <span className="text-sm text-gray-400">{label}</span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TotalFeesChart;