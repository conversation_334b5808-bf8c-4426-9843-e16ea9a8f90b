import { useState } from "react";
import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import Mastercard from '@shared/assets/images/onboarding/Mastercard.svg'
import Visa from '@shared/assets/images/onboarding/Visa.svg'
import Paypal from '@shared/assets/images/onboarding/Paypal.svg'
import GPay from '@shared/assets/images/onboarding/G Pay.svg'
import ApplePay from '@shared/assets/images/onboarding/apple pay.svg'
import AmericanExpress from '@shared/assets/images/onboarding/American Express.svg'
import Stripe from '@shared/assets/images/onboarding/Stripe.svg'
import { useDispatch } from "react-redux";
import { nextStep, prevStep } from "@/core/states/signup.js";
import { Checkbox } from "@heroui/checkbox";
import { CardNumberInput, CVVInput, ExpiryInput } from "@shared/components/PaymentInputs.jsx";


const PaymentPage = () => {
    const [selectedPlan, setSelectedPlan] = useState(null);
    const dispatch = useDispatch();
    const [cardNumber, setCardNumber] = useState('');
    const [expiry, setExpiry] = useState('');
    const [cvv, setCVV] = useState('');

    const plans = [
        {
            name: "Basic",
            price: "$39.0",
            details: "Detailed plan is here",
            features: [
                "The best one ever",
                "Data-Included of 600 GB",
                "Early access and full day methods"
            ],
            period: "1 User / Monthly"
        },
        {
            name: "Pro",
            price: "$69.0",
            period: "1 User / Monthly"
        }
    ];

    const paymentMethods = [
        { name: "Mastercard", icon: Mastercard },
        { name: "Visa", icon: Visa },
        { name: "PayPal", icon: Paypal },
        { name: "Google Pay", icon: GPay },
        { name: "Apple Pay", icon: ApplePay },
        { name: "American Express", icon: AmericanExpress },
        { name: "Stripe", icon: Stripe }
    ];
    const [selectedMethod, setSelectedMethod] = useState({ name: "Mastercard", icon: Mastercard })

    return (
        <div className="flex flex-col md:flex-row w-full max-w-7xl mx-auto gap-16 p-6">
            {/* Left Section - Plan Selection */}
            <div className="w-full md:w-1/2">
                <h1 className="text-2xl font-bold text-primary dark:text-white mb-2">Choose your plan !</h1>
                <p className="text-gray-400 mb-8">
                    Connect using your email address, and get directly started to our platforms
                </p>

                <div className="mb-8 mt-24 w-full">

                    <div className="flex flex-col gap-3 relative">
                        <span
                            className="shadow bg-red-500 absolute right-6 -top-5 z-20 text-white min-w-fit inline-block px-3 py-1 rounded-full text-sm">
                            Recommended
                        </span>
                        {plans.map((plan, index) => (
                            <Checkbox
                                key={index}
                                radius="full"
                                aria-label="Basic plan"
                                classNames={{
                                    base: `min-w-full px-4 py-2 inline-flex mb-4 cursor-pointer transition-all w-full max-w-md ${selectedPlan === index ? 'bg-transparent' : 'bg-black/5 dark:bg-white/5 dark:hover:bg-white/10 hover:bg-white/10'} items-center justify-start cursor-pointer rounded-lg gap-2 border-2 border-black/5 dark:border-white/5 data-[selected=true]:border-info`,
                                    label: "w-full",
                                    wrapper: 'after:bg-info',
                                    icon: "dark:text-info",
                                }}
                                isSelected={selectedPlan === index}
                                onValueChange={() => {
                                    setSelectedPlan(index);
                                }}
                            >
                                <div className="py-5 px-4">
                                    <div className="flex justify-between items-center">
                                        <div>
                                            <h3 className="text-xl font-bold">{plan.name}</h3>
                                            {selectedPlan === index && plan.details &&
                                                <p className="text-gray-500">{plan.details}</p>}
                                        </div>
                                        <div className="text-right">
                                            <p className="text-xl font-bold">{plan.price}</p>
                                            <p className={`transition-all duration-500 ease-in-out text-sm text-gray-500 ${selectedPlan === index ? 'block' : 'hidden'}`}>{plan.period}</p>
                                        </div>
                                    </div>
                                    <div
                                        className={`transition-all duration-300 ease-in-out ${selectedPlan === index ? 'block' : 'hidden'}`}>
                                        {plan.features && (
                                            <ul className="space-y-2 mt-2">
                                                {plan.features.map((feature, idx) => (
                                                    <li key={idx} className="flex items-center gap-2 text-gray-600">
                                                        <div className="w-2 h-2 bg-gray-300 rounded-full" />
                                                        {feature}
                                                    </li>
                                                ))}
                                            </ul>
                                        )}
                                    </div>
                                </div>
                            </Checkbox>
                        ))}
                    </div>
                </div>
                <div className="flex flex-row gap-4 mx-auto">
                    <Button variant="bordered"
                        className="my-4 font-bold rounded py-4 text-gray-700 dark:text-white px-12 self-end"
                        onClick={() => dispatch(prevStep())}>
                        Back
                    </Button>
                    <Button
                        className={`my-4 font-bold text-white dark:text-gray-500 rounded ${(selectedPlan === null) ? 'bg-gray-300 dark:bg-zinc-800 cursor-not-allowed' : 'bg-info dark:text-white'}  py-4 px-12 self-end`}
                        disabled={selectedPlan === '' || selectedPlan === null} onClick={() => dispatch(nextStep())}>
                        Next
                    </Button>
                </div>
            </div>

            {/* Right Section - Payment Details */}
            <div className="w-full md:w-1/2">
                <h2 className="text-2xl font-bold text-primary dark:text-white mb-2">Payment method !</h2>
                <p className="text-gray-400 mb-6">
                    Connect using your email address, and get directly started to our platforms
                </p>

                <div className="flex flex-row gap-2 mb-6">
                    {paymentMethods.map((method, index) => (
                        <Button
                            onClick={() => setSelectedMethod(method)}
                            key={index}
                            size="sm"
                            isIconOnly
                            variant="bordered"
                            className={`h-12 w-full flex items-center justify-center ${index === 0 ? 'border-[#338AF3] bg-[#0587FF]/20' : 'border-black/10 dark:border-white/10'}`}
                        >
                            <img src={method.icon} alt="Payment Method Icon" className="w-8 h-8" />
                        </Button>
                    ))}
                </div>

                <div className="space-y-4">
                    <Input
                        label="Card Holder"
                        placeholder="Enter card holder name"
                        className="w-full"
                        classNames={{
                            inputWrapper: `rounded-md border border-black/5 dark:border-white/5`
                        }}
                    />

                    <CardNumberInput
                        icon={selectedMethod.icon}
                        value={cardNumber}
                        onChange={setCardNumber}
                    />

                    <div className="">
                        <div className="grid lg:grid-cols-2 gap-4 py-2">
                            <div>
                                <h4 className="font-medium text-primary dark:text-white text-lg">Expiry Date</h4>
                                <p className="text-xs text-gray-500">Enter the expiration date of your card</p>
                            </div>
                            <ExpiryInput
                                value={expiry}
                                onChange={setExpiry}
                            />
                        </div>
                        <div className="grid lg:grid-cols-2 gap-4 py-2">
                            <div>
                                <h4 className="font-medium text-primary dark:text-white text-lg">CVV Number</h4>
                                <p className="text-xs text-gray-500">Enter the CVV numbers back of your card</p>
                            </div>
                            <CVVInput
                                value={cvv}
                                onChange={setCVV}
                            />
                        </div>
                    </div>

                    <div className="mt-6">
                        <div className="flex justify-between items-center mb-4">
                            <div className="flex flex-col">
                                <span className="font-bold">Total to be paid.</span>
                                <span className="text-zinc-400 text-sm">This price is included the VAT</span>
                            </div>
                            <div className="text-right">
                                <p className="text-xl font-bold">$69.0</p>
                                <p className="text-sm text-gray-500">1 User / Monthly</p>
                            </div>
                        </div>

                        <Button
                            className="w-full bg-glb_red text-white h-12 rounded-lg mb-4"
                        >
                            Pay Now
                        </Button>

                        <p className="text-center text-sm text-gray-500">
                            By clicking on Pay Now Button, you&#39;re agreed all terms and conditions & Privacy data of
                            our platform and website.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PaymentPage;