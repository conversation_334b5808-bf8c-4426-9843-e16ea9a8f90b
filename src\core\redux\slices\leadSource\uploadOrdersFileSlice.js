// fileImportSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { uploadExcelFileUrl } from '../URLs';
import { getToken } from '../../../services/TokenHandler';
import { toast } from 'sonner';

// Async thunk for file import
export const importExcelFile = createAsyncThunk(
    'fileImport/importExcelFile',
    async (file, { rejectWithValue }) => {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await axios.post(uploadExcelFileUrl,
                formData,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                        'Content-Type': 'multipart/form-data',
                    },
                }
            );

            return response.data;

        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response.data });
        }
    }
);

const uploadOrdersFileSlice = createSlice({
    name: 'ordersFileImport',
    initialState: {
        loading: false,
        success: false,
        error: null,
        responseData: null,
    },
    reducers: {
        resetImportState: (state) => {
            state.loading = false;
            state.success = false;
            state.error = null;
            state.responseData = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(importExcelFile.pending, (state) => {
                state.loading = true;
                state.success = false;
                state.error = null;
                state.responseData = null;
            })
            .addCase(importExcelFile.fulfilled, (state, action) => {
                state.loading = false;
                state.success = true;
                toast.success('Order(s) added successfully')
                state.responseData = action.payload;
            })
            .addCase(importExcelFile.rejected, (state, action) => {
                state.loading = false;
                console.log('action.payload', action.payload);

                if (action.payload.message?.message.includes(' ... ')) {
                    const messages = action.payload.message.message.split(' ... ');
                    messages.forEach(msg => toast.error(msg.trim()));
                } else {
                    toast.error(action.payload.message.message);
                }
                if (action.payload.response === 'error') {
                    state.error = action.payload.message;
                    toast.error(action.payload.message)
                }
                console.log('error upload', action.payload);

            });
    },
});

export const { resetImportState } = uploadOrdersFileSlice.actions;
export default uploadOrdersFileSlice.reducer;