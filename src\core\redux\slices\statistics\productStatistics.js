import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { productFundsUrl, productStatisticsUrl } from '../URLs';
import { getToken } from '../../../services/TokenHandler';



export const updateStatisticsParams = createAsyncThunk(
    "productStatistics/updateParams",
    async (newParams, { getState, rejectWithValue }) => {
        try {
            Object.keys(newParams).forEach(key => {
                if (newParams[key] === '') {
                    newParams[key] = null;
                }
            });
            const { params } = getState().productStatistics;

            return { ...params, ...newParams };
        } catch (error) {
            return rejectWithValue("Failed to update params");
        }
    }
);

export const resetStatisticsParams = createAsyncThunk(
    "productStatistics/resetParams",
    async (_, { getState, rejectWithValue }) => {
        try {
            const { params } = getState().productStatistics;
            const resetParams = Object.keys(params).reduce((acc, key) => {
                acc[key] = null;
                return acc;
            }, {});
            return resetParams;

        } catch (error) {
            return rejectWithValue("Failed to reset params");
        }
    }
);

// Thunk for fetching product statistics
export const fetchProductStatistics = createAsyncThunk(
    'productStatistics/fetchProductStatistics',
    async ({ type, product }, { rejectWithValue, getState }) => {
        try {
            const { params } = getState().productStatistics;
            let query = `${productStatisticsUrl}?type=${type}&productReference=${product}`;
            params.startDate && (query += `&startDate=${params.startDate}`);
            params.endDate && (query += `&endDate=${params.endDate}`);
            params.destinationCountry && (query += `&destinationCountry=${params.destinationCountry}`);

            const response = await axios.get(query,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            return response.data.result;
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Failed to fetch product statistics');
        }
    }
);

export const fetchProductFunds = createAsyncThunk(
    'productStatistics/fetchProductFunds',
    async ({ type, product }, { getState, rejectWithValue }) => {
        try {
            const { params } = getState().productStatistics;
            const query = `${productFundsUrl}?type=${type}&productReference=${product}`;
            params.startDate && (query += `&startDate=${params.startDate}`);
            params.endDate && (query += `&endDate=${params.endDate}`);

            const response = await axios.get(query,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );
            return response.data.result;
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Failed to fetch product statistics');
        }
    }
);



// Slice for product statistics
const productStatisticsSlice = createSlice({
    name: 'productStatistics',
    initialState: {
        data: null,
        loading: false,
        error: null,
        params: {
            startDate: null,
            endDate: null,
            destinationCountry: null,
        },
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchProductStatistics.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchProductStatistics.fulfilled, (state, action) => {
                state.loading = false;
                state.data = action.payload;
            })
            .addCase(fetchProductStatistics.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(fetchProductFunds.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchProductFunds.fulfilled, (state, action) => {
                state.loading = false;
                state.data = action.payload;
            })
            .addCase(fetchProductFunds.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(updateStatisticsParams.fulfilled, (state, action) => {
                state.params = action.payload;
            })
            .addCase(updateStatisticsParams.rejected, (state, action) => {
                state.error = action.payload;
            })
            .addCase(resetStatisticsParams.fulfilled, (state, action) => {
                state.params = action.payload;
            })
            .addCase(resetStatisticsParams.rejected, (state, action) => {
                state.error = action.payload;
            });


    },
});

export default productStatisticsSlice.reducer;