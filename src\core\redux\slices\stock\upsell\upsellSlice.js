// src/core/redux/slices/stock/upsell/upsellSlice.js

import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import {
  getProductUrl,
  createUpsellUrl,
  updateUpsellUrl,
  deleteUpsellUrl,
} from "../../URLs";
import { getToken } from "../../../../services/TokenHandler";
import { toast } from "sonner";

// -----------------------------------------
// Thunks (using upsell endpoints)
// -----------------------------------------
export const fetchUpsells = createAsyncThunk(
  "upsell/fetchUpsells",
  async (productId, { rejectWithValue }) => {
    try {
      const token = getToken();
      const response = await axios.get(getProductUrl + productId, {
        headers: { Authorization: `Bearer ${token}` },
      });
      // We only care about result.upsell from the product details
      return response.data?.result?.upsell || [];
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to fetch upsells"
      });
    }
  }
);

export const createUpsell = createAsyncThunk(
  "upsell/createUpsell",
  async ({ productId, body }, { rejectWithValue }) => {
    try {
      const token = getToken();
      const response = await axios.post(
        createUpsellUrl + productId,
        body,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data; // e.g., { response: "success", result: {...updatedProduct} }
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to create upsell"
      });
    }
  }
);

export const updateUpsell = createAsyncThunk(
  "upsell/updateUpsell",
  async ({ productId, upsellId, body }, { rejectWithValue }) => {
    try {
      const token = getToken();
      const url = updateUpsellUrl + productId + "/" + upsellId;
      const response = await axios.put(url, body, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data; // e.g., { response: "success", result: {...updatedProduct} }
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to update upsell"
      });
    }
  }
);

export const deleteUpsell = createAsyncThunk(
  "upsell/deleteUpsell",
  async ({ productId, upsellId }, { rejectWithValue }) => {
    try {
      const token = getToken();
      const url = deleteUpsellUrl + productId + "/" + upsellId;
      const response = await axios.delete(url, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data; // e.g., { response: "success", result: {...} }
    } catch (error) {
      return rejectWithValue({
        status: error.response?.status, message:
          error.response?.data || "Failed to delete upsell"
      });
    }
  }
);

// -----------------------------------------
// Initial State
// -----------------------------------------
const initialState = {
  upsells: [],
  loading: false,
  error: null,
};

// -----------------------------------------
// Helper to map each item from API
// -----------------------------------------
function mapApiUpsell(u) {
  return {
    id: u.id,
    name: u.name,
    paidQuantity: u.quantityPaid,
    freeQuantity: u.quantityFree,
    prices: u.prices || [],
  };
}

// -----------------------------------------
// Slice
// -----------------------------------------
const upsellSlice = createSlice({
  name: "upsell",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // -----------------------------
      // fetchUpsells
      // -----------------------------
      .addCase(fetchUpsells.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUpsells.fulfilled, (state, action) => {
        state.loading = false;
        state.upsells = action.payload.map(mapApiUpsell);
      })
      .addCase(fetchUpsells.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch upsells";
      })
      // -----------------------------
      // createUpsell
      // -----------------------------
      .addCase(createUpsell.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createUpsell.fulfilled, (state, action) => {
        state.loading = false;
        const updatedProduct = action.payload?.result;
        if (updatedProduct && Array.isArray(updatedProduct.upsell)) {
          state.upsells = updatedProduct.upsell.map(mapApiUpsell);
        }
        toast.success('Upsell created succefully')
      })
      .addCase(createUpsell.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to create upsell";
      })
      // -----------------------------
      // updateUpsell
      // -----------------------------
      .addCase(updateUpsell.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUpsell.fulfilled, (state, action) => {
        state.loading = false;
        const updatedProduct = action.payload?.result;
        if (updatedProduct && Array.isArray(updatedProduct.upsell)) {
          state.upsells = updatedProduct.upsell.map(mapApiUpsell);
        }
        toast.success('Upsell updated succefully')
      })
      .addCase(updateUpsell.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to update upsell";
      })
      // -----------------------------
      // deleteUpsell
      // -----------------------------
      .addCase(deleteUpsell.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteUpsell.fulfilled, (state, action) => {
        state.loading = false;
        const updatedProduct = action.payload?.result;
        if (updatedProduct && Array.isArray(updatedProduct.upsell)) {
          state.upsells = updatedProduct.upsell.map(mapApiUpsell);
        } else {
          const { upsellId } = action.meta.arg;
          state.upsells = state.upsells.filter((u) => u.id !== upsellId);
        }
      })
      .addCase(deleteUpsell.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to delete upsell";
      });
  },
});

export default upsellSlice.reducer;
