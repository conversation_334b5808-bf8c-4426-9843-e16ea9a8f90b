import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { addShopifyLeadSourceUrl, deleteLeadSourcesUrl, getLeadSourcesUrl, serviceInvoicesListUrl } from "../URLs";
import { getToken } from "../../../services/TokenHandler";
import axios from "axios";
import { toast } from "sonner";


const initialState = {
    leadSources: [],
    loading: false,
    error: null,
};


export const addShopifySourceLead = createAsyncThunk(
    'leadsources/addShopifySourceLead',
    async ({ shop, token }, { rejectWithValue }) => {
        try {
            const formData = new URLSearchParams();
            formData.append('shop', shop);
            formData.append('token', token);
            const response = await axios.post(addShopifyLeadSourceUrl,
                formData,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue(response.data.message || 'Failed to add Shopify lead source');
            }
            toast.success(response.data.message)
            return response.data;
        } catch (error) {
            console.error("Error in addShopifySourceLead:", error);

            return rejectWithValue(error.response?.data?.message || error.message);
        }
    }
);

// Async thunk for installing Shopify app
export const getLeadSourceList = createAsyncThunk(
    "leadSourceList/get",
    async ({ page }, { rejectWithValue }) => {
        try {
            let query = `${getLeadSourcesUrl}?page=${page}`;

            const response = await axios.get(query,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );
            if (response.data.response !== 'success') {
                toast.error(response.data.message)
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching lead sources' });
            }


            return response.data;

        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);

// Async thunk for installing Shopify app
export const deleteLeadSource = createAsyncThunk(
    "leadSourceList/delete",
    async ({ import_id, api_type }, { rejectWithValue }) => {
        try {
            let query = `${deleteLeadSourcesUrl}?import_id=${import_id}&api_type=${api_type}`;

            const response = await axios.delete(query,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );
            if (response.data.response !== 'success') {
                toast.error(response.data.message)
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching lead sources' });
            }


            return { ...response.data, id: import_id };

        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);


const leadSourcesSlice = createSlice({
    name: 'leadSources',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(getLeadSourceList.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getLeadSourceList.fulfilled, (state, action) => {
                state.loading = false;

                state.leadSources = action.payload;
            })
            .addCase(getLeadSourceList.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(addShopifySourceLead.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(addShopifySourceLead.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(addShopifySourceLead.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            .addCase(deleteLeadSource.fulfilled, (state, action) => {
                state.leadSources = action.payload;
            })


    },
});

export default leadSourcesSlice.reducer;