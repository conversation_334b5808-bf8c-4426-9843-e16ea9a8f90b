import React, { forwardRef } from 'react';
import UnderlinedInput from '../../settings/components/UnderlinedInput';

/**
 * A wrapper component that uses a range slider input
 * Allows users to adjust values using a slider while still maintaining
 * the ability to click and edit the input directly.
 */
const RangeSliderInput = forwardRef(({ value, onChange, id, step = 1, min = 0, max = 100, ...props }, ref) => {
  // Parse numeric value from input (removing % if present)
  const parseInputValue = (val) => {
    if (typeof val === 'string' && val.includes('%')) {
      return parseFloat(val.replace('%', ''));
    }
    return parseFloat(val);
  };

  // Format value back to string with % if needed
  const formatValue = (val) => {
    if (id === 'cDomestic' || id === 'dDomestic') {
      return `${val}%`;
    }
    return val.toString();
  };

  // Handle slider change
  const handleSliderChange = (e) => {
    const newValue = e.target.value;
    if (id === 'cDomestic' || id === 'dDomestic') {
      onChange({ target: { id, value: `${newValue}%` } });
    } else {
      onChange({ target: { id, value: newValue.toString() } });
    }
  };

  return (
    <div className="flex flex-col gap-2">

      <UnderlinedInput
        ref={ref}
        id={id}
        value={value}
        onChange={onChange}
        {...props}
      />
      <input
        type="range"
        id={id}
        value={parseInputValue(value)}
        onChange={handleSliderChange}
        step={step}
        min={min}
        max={max}
        className="w-full cursor-pointer"
      />
    </div>
  );
});

export default RangeSliderInput;
