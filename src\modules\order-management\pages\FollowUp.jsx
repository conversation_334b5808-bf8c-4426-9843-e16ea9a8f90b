import {
    Arrow<PERSON>eft02Icon,
    Arrow<PERSON><PERSON>02Icon,
    ArrowUpDownIcon,
    Calendar01Icon,
    CallBlocked02Icon,
    CallEnd01Icon,
    CallOutgoing02Icon,
    CancelCircleHalfDotIcon,
    CancelCircleIcon,
    CheckmarkCircle01Icon,
    CheckmarkCircle02Icon,
    CircleArrowLeft01Icon,
    Clock01Icon,
    Copy01Icon,
    DeliveryTruck01Icon,
    DownloadSquare01Icon,
    Link01Icon,
    PackageIcon,
    PencilEdit01Icon,
    ReloadIcon,
    RepeatIcon,
    ReturnRequestIcon,
    Settings02Icon,
    TestTube01Icon,
    UploadSquare01Icon,
    UserAdd02Icon,
    ViewIcon,
    XVariableCircleIcon
} from "hugeicons-react";
import { Tab, Tabs } from "@heroui/tabs";
import { Chip } from "@heroui/chip";
import { Button } from "@heroui/button";
import { Pagination } from "@heroui/pagination";
import React, { useCallback, useEffect, useState } from "react";
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/table";
import OrderInformationModal from "@/modules/order-management/components/OrderInformationModal.jsx";
import DashboardLayout from "../../shared/layouts/DashboardLayout";
import CustomTabs from "../../shared/components/CustomTabs";
import CustomTable from "../../shared/components/CustomTable";
import ImportOrderModal from "../components/ImportOrderModal";
import { useDispatch, useSelector } from "react-redux";
import { getExportedOrders, getFollowup, getFollowupStatus, getFollowupStatusCount, getOrders, getOrderStatus, getStaticOrders, resetParams, updateParams } from "../../../core/redux/slices/orders/ordersManagementSlice";
import moment from "moment";
import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from "@heroui/dropdown";
import { ordersListUrl } from "../../../core/redux/slices/URLs";
import { getToken } from "../../../core/services/TokenHandler";
import axios from "axios";
import { Tooltip } from "@heroui/react";
import { formatNumber, STATUS_DESCIPTION_REGEX, statusColorMap, statusIconsMap, URL_PARAMS_CONFIG } from "../../../core/utils/functions";
import PriceRenderer from "../../shared/components/PriceRenderer";
import { useSearchParams } from "react-router-dom";




const columns = [
    { key: "checkbox", label: "#" },

    { key: "orderNum", label: "Order Nº", },
    { key: "createdAt", label: "Date", },
    { key: "trackingNumber", label: "Tracking Nº", permission: "sellers.trackingnumber" },
    { key: "store", label: "Store", },
    { key: "goodsDescription", label: "Product" },
    { key: "price", label: "Price" },
    { key: "statusDescription", label: "Status" },
    { key: "followup", label: "Followup", permission: "sellers.followup" },
    { key: "fullname", label: "Full Name" },
    { key: "phone", label: "Phone" },
    { key: "country", label: "Country" },
    { key: "city", label: "City" },
    { key: "shippingCompany", label: "Shipping Company", permission: "sellers.shippingcompany" },
    { key: "actions", label: "Actions" },
];


export default function FollowUp() {
    const [isOrderInformationModalOpen, setOrderInformationModalOpen] = useState(null);
    const [isImportOrderModalOpen, setImportOrderModalOpen] = useState(false);
    const [searchParams, setSearchParams] = useSearchParams();
    const [selectedRows, setSelectedRows] = useState([]);
    const rowsPerPage = 10;
    const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get('page')) || 1);

    // Initialize selectedTab with "all" as default
    const [selectedTab, setSelectedTab] = useState(searchParams.get('followups') || 'all');

    const dispatch = useDispatch();
    const { followup, params, loading, error, followupStatus, exportLoading } = useSelector((state) => state.orders);
    const { filterParams } = useSelector((state) => state.content);

    // Initialize params with URL values on component mount
    useEffect(() => {
        const initialParams = {};
        let hasParams = false;

        Object.entries(URL_PARAMS_CONFIG).forEach(([key, config]) => {
            const urlValue = searchParams.get(config.urlKey);
            if (urlValue !== null) {
                hasParams = true;
                // Special handling for array parameters to convert comma-separated string to array
                if (config.reduxKey === 'excludedStatus' || config.reduxKey === 'listStatus') {
                    initialParams[config.reduxKey] = urlValue.split(',');
                }
                // Handle orderNum (k) as a string
                else if (config.reduxKey === 'orderNum') {
                    initialParams[config.reduxKey] = urlValue.toString(); // Ensure orderNum is a string
                }
                else if (config.reduxKey === 'upsell') {
                    initialParams[config.reduxKey] = urlValue;
                }
                else if (config.reduxKey === 'status') {
                    initialParams[config.reduxKey] = urlValue;
                }
                else if (config.reduxKey === 'followupStatus') {
                    initialParams[config.reduxKey] = urlValue;
                }
                else if (config.reduxKey === 'startDate' || config.reduxKey === 'endDate') {
                    initialParams[config.reduxKey] = urlValue ? moment(urlValue).toISOString() : null;
                } else if (config.reduxKey === 'productReference' || config.reduxKey === 'sortBy' || config.reduxKey === 'paymentMethod' || config.reduxKey === 'originCountry' || config.reduxKey === 'destinationCountry') {
                    initialParams[config.reduxKey] = urlValue;
                }
                else {
                    // Convert page to number, keep others as is
                    initialParams[config.reduxKey] = config.reduxKey === 'page' ? parseInt(currentPage || urlValue) : currentPage || urlValue;
                }
            }
        });
        // Check for the 'orderId' parameter in the URL (independent of filters)
        const orderIdFromUrl = searchParams.get('od'); // 'orderId' is the new URL param
        if (orderIdFromUrl) {
            // Decode the orderId and open the modal
            const decodedOrderId = atob(orderIdFromUrl);
            setOrderInformationModalOpen({ id: decodedOrderId });
        }

        // Always update params if we have any URL values
        if (hasParams && selectedTab === 'all') {
            dispatch(updateParams({
                ...params,
                ...initialParams
            }));
        }
    }, [dispatch, currentPage, selectedTab])

    useEffect(() => {

        dispatch(getFollowupStatus());

    }, [dispatch]);

    // Fetch followup data when component mounts or params change


    useEffect(() => {
        if (selectedTab === 'all') {
            dispatch(updateParams({ ...{ page: currentPage } }))

        }

    }, [dispatch, currentPage])


    // Effect to fetch orders when params change
    useEffect(() => {
        if (selectedTab === 'all') {
            dispatch(getFollowup());
        }
    }, [dispatch, params, selectedTab]);
    // Effect for tab changes
    useEffect(() => {
        if (selectedTab !== 'all') {
            dispatch(getStaticOrders({
                tab: selectedTab,
                currentPage: currentPage,
                isFollowUp: true
            }));
        }
    }, [dispatch, currentPage, selectedTab]);
    // Function to get status counts for tabs
    const getFollowStatusCount = (followupStatus) => {
        return new Promise((resolve) => {
            // Dispatch the action to get status count
            dispatch(getFollowupStatusCount(followupStatus))

                .then(action => {
                    // Check if the action was fulfilled
                    if (getFollowupStatusCount.fulfilled.match(action)) {
                        resolve(action.payload);
                    } else {
                        resolve('-');
                    }
                })
                .catch(error => {
                    console.error("Error getting status count:", error);
                    resolve('-');
                });
        });
    }

    // Handle tab change without updating filter params
    const handleTabsChange = async (tab) => {
        // setSelectedTab(tab);
        // dispatch(resetParams());
        // dispatch(getFollowup(tab));
        setSelectedTab(tab);
        setCurrentPage(1);
    }

    // Enhanced page change handler that resets filter parameters
    const handlePageChange = (newPage) => {
        // Reset filter parameters when changing pages
        dispatch(resetParams());
        setCurrentPage(newPage);
    }



    const renderCell = useCallback((item, columnKey) => {
        const cellValue = item[columnKey];

        switch (columnKey) {
            case "statusDescription":
                return (
                    <Chip
                        className={`capitalize py-1 px-2 h-fit text-white ${statusColorMap[item.followup.status.toLowerCase().replace(' ', '')]}`}
                        variant="solid">
                        {item.followup.statusDescription.match(STATUS_DESCIPTION_REGEX) ?
                            <Tooltip color='primary' content={<div className="px-1 py-2 max-w-[300px]">
                                <div className="text-small font-bold">{item.followup.statusDescription.match(STATUS_DESCIPTION_REGEX
                                )[2]}</div>

                            </div>}>

                                <div className="flex flex-row py-2 justify-evenly gap-1 items-center text-black dark:text-white">

                                    <span className="inline-block">
                                        {statusIconsMap[item.followup.status.toLowerCase().replace(' ', '')] && React.createElement(statusIconsMap[item.followup.status.toLowerCase().replace(' ', '')], {
                                            className: "mr-2 ml-1",
                                            size: 20,
                                        })}
                                    </span>
                                    <span className="inline-block">
                                        {
                                            item.followup.statusDescription.match(STATUS_DESCIPTION_REGEX)[1]
                                        }
                                    </span>

                                    {/* <div dangerouslySetInnerHTML={{ __html: item.statusDescription }} /> */}
                                </div>

                            </Tooltip> : <div className="flex flex-row py-2 justify-evenly gap-1 items-center text-black dark:text-white">

                                <span className="inline-block">
                                    {statusIconsMap[item.followup.status.toLowerCase().replace(' ', '')] && React.createElement(statusIconsMap[item.followup.status.toLowerCase().replace(' ', '')], {
                                        className: "mr-2 ml-1",
                                        size: 20,
                                    })}
                                </span>
                                <span className="inline-block">
                                    {
                                        item.followup.statusDescription
                                    }
                                </span>


                            </div>}
                    </Chip>
                );
            case "followup":
                return item.followup.status ? (
                    <Chip
                        className={`capitalize py-1 px-2 h-fit text-white ${statusColorMap[item.followup.status.toLowerCase().replace(' ', '')]}`}
                        variant="solid">
                        <div className="flex flex-row py-2 justify-evenly gap-1 items-center text-black dark:text-white">
                            <span className="inline-block">
                                {statusIconsMap[item.followup.status.toLowerCase().replace(' ', '')] && React.createElement(statusIconsMap[item.followup.status.toLowerCase().replace(' ', '')], {
                                    className: "mr-2 ml-1",
                                    size: 20,
                                })}
                            </span>
                            <span className="inline-block">
                                {
                                    item.followup.statusDescription?.split('<')[0]
                                }
                            </span>
                        </div>
                    </Chip>
                ) : null;
            case 'createdAt':
                return <span>{moment(item.createdAt).format('DD/MM/YYYY HH:mm')}</span>;
            case "price":
                return item.goodsValue !== 0 && (
                    <div className="flex items-center justify-center gap-2 p-2 ">
                        {item.goodsValue && <PriceRenderer price={item.goodsValue} additional={item.currency} />}

                    </div>
                );

            case "fullname":
                return <span>{item.consignee?.contact || "-"}</span>;

            case "phone":
                return <span>{item.consignee?.mobileNumber || "-"}</span>;

            case "country":
                return <span>{item.consignee?.country || "-"}</span>;

            case "goodsDescription":
                return (
                    <Tooltip color='primary' content={<div className="px-1 py-2 max-w-[300px]">
                        <div className="text-small font-bold">Products :</div>
                        <div dangerouslySetInnerHTML={{ __html: cellValue }}
                        />
                    </div>}>
                        <div
                            className=" truncate cursor-help max-w-[250px] overflow-hidden text-ellipsis"
                        >
                            {cellValue && cellValue.replace(/<br\s*\/?>/g, ' | ')}
                        </div>
                    </Tooltip>
                );
            case "city":
                return <span>{item.consignee?.city || "-"}</span>;
            case "actions":
                return (
                    <div className="flex flex-row gap-2 justify-center">
                        <Button isIconOnly size="sm" className="rounded-full bg-blue-700" onPress={() => setOrderInformationModalOpen(item)}>
                            <ViewIcon size={18} className="text-white" />
                        </Button>

                    </div>
                );
            default:
                return (
                    <div
                        dangerouslySetInnerHTML={{ __html: cellValue }}
                    />
                );
        }
    }, []);









    return (
        <>
            <DashboardLayout
                title={`${followup && followup.paginate && followup.paginate.count.toString() ? formatNumber(followup.paginate.count) : '...'} Follow Up`}
                actions={<Dropdown placement="bottom-start">
                    <DropdownTrigger>
                        <div className="cursor-pointer px-4 py-2 rounded-full flex justify-center items-center gap-2 bg-glb_red text-white">
                            <Link01Icon size={18} /> Action
                        </div>
                    </DropdownTrigger>
                    <DropdownMenu aria-label="User Actions" variant="flat">
                        <DropdownItem key="export">
                            <Button isLoading={exportLoading} className="w-full bg-transparent flex justify-start items-center gap-2" onClick={() => dispatch(getExportedOrders(selectedRows))}><DownloadSquare01Icon /> Export</Button></DropdownItem>
                        <DropdownItem key="import" >
                            <Button className="w-full bg-transparent flex justify-start items-center gap-2" onPress={() => setImportOrderModalOpen(true)}><UploadSquare01Icon /> Import</Button>
                        </DropdownItem>

                    </DropdownMenu>
                </Dropdown>}
            >
                <div>
                    <CustomTabs
                        tabLabels={{ all: "All", ...followupStatus }}
                        tabCounts={getFollowStatusCount}
                        selectedTab={selectedTab}
                        initialTab="all"
                        onTabChange={(tab) => handleTabsChange(tab)}
                        params={params.followupStatus}
                    />
                </div>

                <div className="min-w-full py-4 rounded">
                    <CustomTable
                        clickableRow={true}
                        clickableRowAction={(row) => {
                            setOrderInformationModalOpen(row);
                        }}
                        columns={columns}
                        data={followup.result}  // Pass filtered products based on the view
                        paginate={followup.paginate}
                        renderCell={renderCell}
                        setSelectedRows={setSelectedRows}
                        selectedRows={selectedRows} // Pass selected rows state
                        rowsPerPage={rowsPerPage}  // Pass rows per page
                        className="dark:bg-gray-800 dark:text-white" // Dark mode support
                        loading={loading} // Pass loading state
                        error={error} // Pass error state
                        currentPage={currentPage} // Pass current page
                        setCurrentPage={handlePageChange} // Use enhanced page change handler
                    />
                </div>
            </DashboardLayout>
            <OrderInformationModal
                isOpen={isOrderInformationModalOpen}
                onClose={() => setOrderInformationModalOpen(null)}
            />
            <ImportOrderModal
                isOpen={isImportOrderModalOpen}
                setIsOpen={() => setImportOrderModalOpen(false)}
            />
        </>
    );
}
