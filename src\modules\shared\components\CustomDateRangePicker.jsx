import React, { useRef } from 'react';
import { DateRangePicker } from "@heroui/react";
import { I18nProvider } from '@react-aria/i18n';
import { Cancel01Icon } from 'hugeicons-react';
import moment from 'moment';
import { endOfMonth, endOfWeek, getLocalTimeZone, startOfMonth, startOfWeek, today } from '@internationalized/date';
import { Button, ButtonGroup } from "@heroui/button";

/**
 * A custom DateRangePicker component that works with HeroUI
 * 
 * @param {Object} props - Component props
 * @param {Object} props.value - Current date range value with start and end properties
 * @param {Function} props.onChange - Function to call when date range changes
 * @param {string} props.label - Label for the date range picker
 * @param {string} props.variant - Variant of the input (e.g., 'underlined')
 * @param {string} props.color - Color of the input (e.g., 'primary')
 * @param {string} props.locale - Locale for the date picker (default: 'en-GB')
 * @returns {JSX.Element} CustomDateRangePicker component
 */
const CustomDateRangePicker = ({
  value,
  onChange,
  label = "Date Range",
  variant = "underlined",
  color = "primary",
  locale = "en-GB",
  ...props
}) => {
  const datePickerRef = useRef(null);

  // Format the date range for display
  const formatDate = (dateObj) => {
    if (!dateObj) return ""; 
    if (!dateObj.start || !dateObj.end) return "Click to select a date range";
    
    const formattedStart = dateObj?.start
      ? moment(dateObj.start.toDate()).format("DD/MM/YYYY")
      : "";
    const formattedEnd = dateObj?.end
      ? moment(dateObj.end.toDate()).format("DD/MM/YYYY")
      : "";

    return `${formattedStart} - ${formattedEnd}`;
  };

  // Handle opening the calendar
  const handleOpenCalendar = () => {
    if (datePickerRef.current) {
      // Look for the internal selector button
      const button = datePickerRef.current.querySelector('button[data-slot="selector-button"]');
      if (button) button.click();
    }
  };

  // Date presets
  let now = today(getLocalTimeZone());
  let lastWeek = {
    start: startOfWeek(now.subtract({ weeks: 1 }), locale),
    end: endOfWeek(now.subtract({ weeks: 1 }), locale),
  };
  let lastMonth = {
    start: startOfMonth(now.subtract({ months: 1 }), locale),
    end: endOfMonth(now.subtract({ months: 1 }), locale),
  };
  let thisMonth = {
    start: startOfMonth(now, locale),
    end: endOfMonth(now, locale),
  };

  return (
    <div ref={datePickerRef} className="cursor-pointer w-full flex justify-center items-center">
      <I18nProvider locale={locale}>
        <DateRangePicker
          onClick={(e) => {
            // Prevent event from bubbling up to parent elements
            e.stopPropagation();
          }}
          calendarProps={{
            classNames: {
              base: "bg-background",
              headerWrapper: "pt-4 bg-background",
              prevButton: "border-1 border-default-200 rounded-small",
              nextButton: "border-1 border-default-200 rounded-small",
              gridHeader: "bg-background shadow-none border-b-1 border-default-100",
              cellButton: [
                "data-[today=true]:bg-default-100 data-[selected=true]:bg-transparent rounded-small",
                // start (pseudo)
                "data-[range-start=true]:before:rounded-l-small",
                "data-[selection-start=true]:before:rounded-l-small",
                // end (pseudo)
                "data-[range-end=true]:before:rounded-r-small",
                "data-[selection-end=true]:before:rounded-r-small",
                // start (selected)
                "data-[selected=true]:data-[selection-start=true]:data-[range-selection=true]:rounded-small",
                // end (selected)
                "data-[selected=true]:data-[selection-end=true]:data-[range-selection=true]:rounded-small",
              ],
            },
            onPress: (e) => {
              // Prevent calendar button clicks from bubbling up
              e.stopPropagation();
            }
          }}
          firstDayOfWeek="mon"
          classNames={{
            label: ["!text-[#00000050] dark:!text-[#FFFFFF30] "],
            selectorButton: 'justify-center',
            input: 'hidden',
            separator: 'hidden',
            innerWrapper: 'cursor-pointer'
          }}
          CalendarTopContent={
            <ButtonGroup
              fullWidth
              className="px-3 pb-2 pt-3 bg-content1 [&>button]:text-default-500 [&>button]:border-default-200/60"
              radius="full"
              size="sm"
              variant="bordered"
              onClick={(e) => e.stopPropagation()}
            >
              <Button
                onPress={(e) => {
                  e.stopPropagation();
                  onChange(lastMonth);
                }}
              >
                Last Month
              </Button>
              <Button
                onPress={(e) => {
                  e.stopPropagation();
                  onChange(lastWeek);
                }}
              >
                Last week
              </Button>
              <Button
                onPress={(e) => {
                  e.stopPropagation();
                  onChange(thisMonth);
                }}
              >
                This month
              </Button>
            </ButtonGroup>
          }
          startContent={
            <div
              className='w-full flex justify-start items-center gap-1'
              onClick={(e) => {
                e.stopPropagation();
                handleOpenCalendar();
              }}
            >
              {!(!value.end || !value.start) && (
                <div 
                  onClick={(e) => {
                    e.stopPropagation();
                    onChange({
                      start: null,
                      end: null,
                    });
                  }}
                  className="flex justify-center items-center p-1 bg-transparent cursor-pointer"
                >
                  <Cancel01Icon size={16} />
                </div>
              )}
              <span className={`${!value.end || !value.start ? 'text-sm text-[#00000050] dark:text-[#FFFFFF30]' : 'text-medium text-black dark:text-white'}`}>
                {formatDate(value)}
              </span>
            </div>
          }
          className="flex-grow w-full"
          value={value}
          onChange={onChange}
          color={color}
          label={label}
          variant={variant}
          {...props}
        />
      </I18nProvider>
    </div>
  );
};

export default CustomDateRangePicker;
