import { Link, useNavigate } from 'react-router-dom';
import {
    ArrowLeft01Icon,
    Database01Icon,
    DatabaseIcon,
    DeliveryTruck02Icon,
    File01Icon,
    Idea01Icon,
    LockIcon,
    ProductLoadingIcon,
    ShoppingCart01Icon,
    Wallet01Icon
} from "hugeicons-react";
import { Button } from "@heroui/button";
import { useState } from "react";
import DashboardLayout from '../../shared/layouts/DashboardLayout';

const notifications = [
    {
        id: 1,
        title: "Stock Alert",
        message: "To start work with data, please create your first components templates.",
        actionText: "Learn More",
        icon: DatabaseIcon,
    },
    {
        id: 2,
        title: "Your Sourcing Quotation is ready!",
        message: "To start work with data, please create your first components templates.",
        actionText: "Learn More",
        icon: Database01Icon,
    },
    {
        id: 3,
        title: "You received a New message from your Agent",
        message: "To start work with data, please create your first components templates.",
        actionText: "Learn More",
        icon: DatabaseIcon,
    },
    {
        id: 4,
        title: "A New Sourcing Invoice/ Quote is ready!",
        message: "To start work with data, please create your first components templates.",
        actionText: "Learn More",
        icon: ShoppingCart01Icon,
    },
    {
        id: 5,
        title: "Product Back in Stock",
        message: "One of your saved products is now back in stock. Don't miss out!",
        actionText: "Check Now",
        icon: ProductLoadingIcon,
    },
    {
        id: 6,
        title: "Order Shipped",
        message: "Your recent order has been shipped and is on its way to you.",
        actionText: "Track Order",
        icon: DeliveryTruck02Icon,
    },
    {
        id: 7,
        title: "New Feature Update",
        message: "Explore the new features added to enhance your experience.",
        actionText: "Learn More",
        icon: Idea01Icon,
    },
    {
        id: 8,
        title: "Security Alert",
        message: "Unusual activity detected on your account. Please review.",
        actionText: "Review Now",
        icon: LockIcon,
    },
    {
        id: 9,
        title: "Payment Reminder",
        message: "Your next payment is due soon. Don't miss the deadline.",
        actionText: "Pay Now",
        icon: Wallet01Icon,
    },
    {
        id: 10,
        title: "Weekly Summary",
        message: "Here’s your summary of activities and updates from this week.",
        actionText: "View Summary",
        icon: File01Icon,
    }
];

const Notifications = () => {
    const navigate = useNavigate();

    const [visibleNotifications, setVisibleNotifications] = useState(4);

    // Function to load more notifications
    const loadMoreNotifications = () => {
        setVisibleNotifications((prevCount) => prevCount + 4);
    };
    return (
        <DashboardLayout hasReturnLink={true}
            title="Notifications"

        >
            <div className="w-full">

                <div className="flex-1 overflow-y-auto flex flex-col gap-4 w-fit mx-auto">
                    {notifications.slice(0, visibleNotifications).map((item, index) => (
                        <div key={index}
                            className="group w-full hover:bg-[#0258E8]/10 dark:hover:bg-[#0258E8]/10 bg-gray-50 
                            dark:bg-[#121113] shadow-sm py-4 min-h-[100px]
                            rounded-xl flex flex-row justify-between gap-2 md:gap-10 lg:gap-24 items-center px-2 md:px-6">
                            <div className="flex flex-row justify-around gap-2 flex-1  items-center">
                                <div className="bg-[#FCF4E9] max-w-fit p-3 rounded-xl">
                                    {item.icon && <item.icon className="text-yellow-600" />}
                                </div>
                                <div className=' flex-grow'>
                                    <h3 className="font-medium text-[12px] md:text-base lg:text-lg">{item.title}</h3>
                                    <p className="text-gray-500 text-[10px] md:text-sm lg:text-base">
                                        {item.message}
                                    </p>
                                </div>
                            </div>
                            <Link to="" className="font-medium text-[12px] md:text-base lg:text-lg group-hover:text-blue-500">
                                Learn More
                            </Link>
                        </div>
                    ))}
                </div>
                {visibleNotifications < notifications.length && (
                    <Button variant="bordered"
                        onClick={loadMoreNotifications}
                        className="mt-12 mx-auto block rounded-full dark:border-white border-1 px-6">
                        Load More
                    </Button>
                )}
            </div>
        </DashboardLayout>
    );
};

export default Notifications;