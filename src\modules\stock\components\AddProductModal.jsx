import CustomModal from "@shared/components/CustomModal.jsx";
import DataTile from "@shared/components/DataTile.jsx";
import CountryFlag from "@shared/components/CountryFlag.jsx";
import React, { useCallback, useEffect, useRef, useState } from "react";
import UnderlinedInput from "../../settings/components/UnderlinedInput";
import { Select, SelectItem } from "@heroui/select";
import { Cancel01Icon, Cancel02Icon, CancelCircleIcon, CloudUploadIcon, Upload04Icon, ArrowLeft01Icon, ArrowRight01Icon } from "hugeicons-react";
import { motion } from "framer-motion";
import { Input } from "@heroui/input";
import { isValidString } from "../../../core/utils/functions";
import { GetCategoryFromSet, GetCategorySetByName, GetTypeFromSet, GetTypeSetByName, ProductCategory, ProductType } from "../../../core/utils/ProductData";
import { Button } from "@heroui/button";
import { Spin<PERSON>, <PERSON><PERSON><PERSON> } from "@heroui/react";
import { useDispatch, useSelector } from "react-redux";
import { createProduct } from "../../../core/redux/slices/stock/products/productsSlice";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";




const Step01 = ({ formData, handleInputChange, IsDesabled, productsTypes, productsCategories }) => (



    <div className={`${!IsDesabled(0) ? '' : 'pointer-events-none'} md:w-fit mt-10 px-0 md:px-10 xl:px-20 mx-auto flex items-start gap-6 flex-col max-w-[1000px]`}>
        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4">
            <UnderlinedInput label="Product Name"
                value={formData.productName}
                onChange={(e) => handleInputChange("productName", e.target.value, e)}
                start={true}
                isRequired
            />

            <UnderlinedInput
                label="Arabic Name"
                value={formData.arabicName}
                onChange={(e) => handleInputChange("arabicName", e.target.value)}
                start={true}

            />
        </div>
        <div className="w-full grid grid-cols-1 gap-4">
            <UnderlinedInput label="SKU" value={formData.sku}
                onChange={(e) => handleInputChange("sku", e.target.value)} start={true}
                isRequired />

        </div>
        <div className=" w-full grid grid-cols-1 gap-4">

            <Select
                // isDisabled={loading}
                // startContent={loading && <Spinner size="sm" />}
                selectedKeys={[
                    Object.keys(productsTypes).find(
                        (key) => key === formData.productType
                    ),
                ]}
                isRequired
                onChange={(e) => {
                    handleInputChange("productType", e.target.value);
                }}
                variant="underlined"
                color="primary"
                label="Select Product Type"
                className="w-full"
                classNames={{
                    label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],
                }}
            >
                {Object.entries(productsTypes).map(([key, value]) => (
                    <SelectItem key={key}>{value}</SelectItem>
                ))}
            </Select>


        </div>
        <div className="w-full grid grid-cols-1 gap-4">
            <Select
                // isDisabled={loading}
                // startContent={loading && <Spinner size="sm" />}
                selectedKeys={[
                    Object.keys(productsCategories).find(
                        (key) => key === formData.category
                    ),
                ]}
                isRequired
                onChange={(e) => {
                    handleInputChange("category", e.target.value);
                }}
                variant="underlined"
                color="primary"
                label="Select Product category"
                className="w-full"
                classNames={{
                    label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],
                }}
            >
                {Object.entries(productsCategories).map(([key, value]) => (
                    <SelectItem key={key}>{value}</SelectItem>
                ))}
            </Select>

        </div>


        <div className="w-full text-center text-glb_red text-xs my-10">
            {!IsDesabled(0) ? '' : 'Complete the previous step first'}
        </div>

    </div>
);
const Step02 = ({ formData, handleInputChange, IsDesabled }) => (
    <div className={`${!IsDesabled(1) ? '' : 'pointer-events-none'} md:w-full max-w-lg mt-10 px-0 md:px-10 xl:px-20 mx-auto flex items-start gap-6 flex-col `}>

        <div className="w-full  grid grid-cols-1 gap-4">
            <UnderlinedInput start={true} label="HS Code" value={formData.hscode}
                onChange={(e) => handleInputChange("hscode", e.target.value)} />

        </div>
        <div className="w-full grid grid-cols-1 gap-4">
            <UnderlinedInput start={true} label="Product Video" value={formData.productVideo}
                onChange={(e) => handleInputChange("productVideo", e.target.value)} />

        </div>
        <div className=" w-full grid grid-cols-1 gap-4">
            <UnderlinedInput start={true} label="Product Link" value={formData.productLink}
                onChange={(e) => handleInputChange("productLink", e.target.value)} />

        </div>


        <div className="hidden w-full text-center text-glb_red text-xs my-10">
            Error please fill all informations data for each step to continue
        </div>
        <div className="w-full text-center text-glb_red text-xs my-10">
            {!IsDesabled(1) ? '' : 'Complete the previous step first'}
        </div>
    </div>
);
const Step03 = ({ formData, handleInputChange, IsDesabled, isValide }) => (


    <div className={`${!IsDesabled(2) ? '' : 'pointer-events-none'} md:w-fit mt-10 px-0 md:px-10 xl:px-20 mx-auto flex items-start gap-6 flex-col max-w-[1000px]`}>
        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4">
            <UnderlinedInput label="Weight (Kg)"
                start={true}
                value={formData.weight}
                isInvalid={isValide['weight']}
                onChange={(e) => handleInputChange("weight", e.target.value)} />
            <UnderlinedInput start={true}
                label="Width (Cm)" value={formData.width}
                isInvalid={isValide['width']}
                onChange={(e) => handleInputChange("width", e.target.value)}
            />
        </div>
        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4">
            <UnderlinedInput label="Height (Cm)" value={formData.height}
                isInvalid={isValide['height']} start={true}
                onChange={(e) => handleInputChange("height", e.target.value)} />
            <UnderlinedInput start={true}
                label="Length (Cm)" value={formData.length}
                isInvalid={isValide['length']}
                onChange={(e) => handleInputChange("length", e.target.value)}
            />
        </div>
        <div className="w-full grid grid-cols-1 gap-4">
            <UnderlinedInput start={true} label="Note for Call Center" value={formData.noteForCallCenter}
                onChange={(e) => handleInputChange("noteForCallCenter", e.target.value)} />

        </div>


        <div className="hidden text-center text-glb_red text-xs my-10">
            Error please fill all informations data for each step to continue
        </div>
        <div className="w-full text-center text-glb_red text-xs my-10">
            {!IsDesabled(2) ? '' : 'Complete the previous step first'}
        </div>
    </div>
);

const Step04 = ({ file, handleFileUpload }) => {





    return (
        <div className={` md:w-fit mt-10 px-0 md:px-10 xl:px-20 mx-auto flex items-start gap-6 flex-col max-w-[1000px]`}>
            <div className="w-full grid grid-cols-1 gap-4">
                {/* Image uploader with preview */}
                <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800">
                    {file ? (
                        <>
                            <div className="relative w-full h-full">
                                <img
                                    src={URL.createObjectURL(file)}
                                    alt="Uploaded Preview"
                                    className="h-full w-full object-cover rounded-lg"
                                />
                                <button
                                    onClick={() => handleFileUpload(null)}
                                    className="absolute top-2 right-2  text-white rounded-full p-1 hover:text-glb_red"
                                >
                                    <CancelCircleIcon />
                                </button>
                            </div>

                        </>
                    ) : (
                        <div className="flex flex-col items-center justify-center pt-5 pb-6 m-8">
                            <CloudUploadIcon className="w-10 h-10 text-gray-400" />
                            <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                <span className="font-semibold">Click to upload</span> or drag and drop
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">PNG or JPG (max 10MB)</p>
                        </div>
                    )}
                    <input
                        type="file"
                        accept=".png, .jpg, .jpeg"
                        className="hidden"
                        onChange={(e) => {
                            const file = e.target.files[0];
                            if (file && file.size <= 10 * 1024 * 1024) {
                                handleFileUpload(file);
                            } else {
                                alert("File must be PNG or JPG and less than 10MB.");
                            }
                        }}
                    />
                </label>
                {file && <span className="text-sm text-gray-500 dark:text-gray-400">
                    {file.name}
                </span>}
            </div>



        </div>
    );
};


const Navigator = ({ steps, CurrentStep, currentTab, setCurrentTabs }) => {

    const containerRef = useRef(); // Ref for the parent container
    const itemRefs = useRef([]); // Ref array for each nav item

    const handleTabClick = (step) => {
        setCurrentTabs(step)
        // Scroll the clicked tab into view
        const element = itemRefs.current[step];
        if (element) {
            element.scrollIntoView({
                behavior: "smooth", // Smooth scrolling
                block: "center", // Ensures the element is centered vertically
                inline: "center", // Ensures the element is centered horizontally
            });
        }
    };


    return (
        <div ref={containerRef} className="w-full max-w-[600px] mx-auto hide-scrollbar flex justify-between items-start gap-2">
            {steps.map((i, index) => {
                return (
                    <motion.div
                        initial={{ scale: 1 }}
                        whileTap={{ scale: 0.9 }}
                        transition={{ duration: 0.1 }}
                        ref={(el) => (itemRefs.current[i.step] = el)} // Store the ref for each item
                        onClick={() => handleTabClick(i.step)}
                        key={i.step}
                        className={`cursor-pointer relative  rounded-full text-nowrap transition 
                            ${currentTab === index ? 'bg-glb_red text-white px-9 py-2' : '!bg-transparent border-1 !border-black !text-black dark:!border-white dark:!text-white px-8 py-1'}
                          `}
                    >
                        <span className={`text-sm md:text-base lg:text-lg`}>
                            {i.label}
                        </span>

                    </motion.div>
                );
            })}
        </div>
    );
};


const AddProductModal = ({ isOpen, onClose, productsTypes, productsCategories }) => {
    const [formData, setFormData] = useState({
        productName: "",
        arabicName: "",
        sku: "",
        productType: null,
        category: null,
        hscode: "",
        productVideo: "",
        productLink: null,
        weight: "",
        width: "",
        height: "",
        length: "",
        noteForCallCenter: "",
        mainImage: '',
        image: null, // For file/image upload
    });
    const [imageFile, setImageFile] = useState(null)
    const [isValide, setIsValide] = useState({})
    const dispatch = useDispatch()
    const { loading, error, } = useSelector((state) => state.products);

    const handleInputChange = React.useCallback((field, value) => {
        if (['weight', 'length', 'width', 'height'].includes(field)) {
            if (value === "." || /^\d*\.?\d*$/.test(value)) {
                setFormData((prev) => ({ ...prev, [field]: value }));
                setIsValide((prev) => {
                    if (field in prev) {
                        const updated = { ...prev };
                        delete updated[field];
                        return updated;
                    }
                    return prev;
                });
            } else {
                setIsValide((prev) => ({
                    ...prev,
                    [field]: 'Number is required',
                }));
            }
        }
        else {
            setFormData((prev) => ({ ...prev, [field]: value }));
        }
    }, []);


    const handleFileUpload = (file) => {

        setImageFile(file)
        setFormData((prevData) => ({
            ...prevData,
            image: file ? file.name : null, // Update the image field with the file name
        }));

        if (!file) {
            setFormData((prev) => ({
                ...prev,
                image: null

            }));
        } else {
            setFormData((prev) => ({
                ...prev,
                image: file,
                mainImage: file.name,

            }));
        }

    };

    const [currentTabs, setCurrentTabs] = useState(0)
    const [steps, setSteps] = useState([
        { step: 0, label: 'Step 01', isAccomplished: false },
        { step: 1, label: 'Step 02', isAccomplished: false },
        { step: 2, label: 'Step 03', isAccomplished: false },
        { step: 3, label: 'Step 04', isAccomplished: false },
    ]);
    const [curentStep, setCurentStep] = useState({ step: 0, label: 'Step 01', isAccomplished: false })

    // Navigation handlers
    const handleBack = () => {
        if (currentTabs > 0) setCurrentTabs(currentTabs - 1);
    };
    const handleNext = () => {
        if (currentTabs < steps.length - 1) setCurrentTabs(currentTabs + 1);
    };

    // Function to move to the next step
    const nextStep = () => {
        setSteps((prevSteps) => {
            const nextIndex = prevSteps.findIndex((s) => !s.isAccomplished);

            // Check if the last step is reached
            if (nextIndex === prevSteps.length - 1) {
                // Call the function when the last step is completed
                console.log('last one');

                return prevSteps; // No need to modify the state further
            }

            // Update the state for the current step
            if (nextIndex !== -1) {
                return prevSteps.map((step, idx) =>
                    idx === nextIndex
                        ? { ...step, isAccomplished: true }
                        : step
                );
            }

            return prevSteps;
        });
    };


    // Function to move back to the previous step
    const backStep = () => {
        setSteps((prevSteps) => {
            const lastCompletedIndex = prevSteps.findLastIndex((s) => s.isAccomplished);
            if (lastCompletedIndex !== -1) {
                return prevSteps.map((step, idx) =>
                    idx === lastCompletedIndex
                        ? { ...step, isAccomplished: false }
                        : step
                );
            }
            return prevSteps;
        });
    };


    const getCurrentStep = () => {
        const firstUnaccomplished = steps.find((s) => !s.isAccomplished);
        return firstUnaccomplished || steps[steps.length - 1]; // Fallback to the last step if all are accomplished
    };

    const OnCloseModal = () => {
        setFormData({
            productName: "",
            arabicName: "",
            sku: "",
            productType: null,
            category: null,
            hscode: "",
            productVideo: "",
            productLink: null,
            weight: "",
            width: "",
            height: "",
            length: "",
            noteForCallCenter: "",
            mainImage: '',
            image: null, // Reset the image field
        });
        setImageFile(null);
        setCurrentTabs(0);
        setSteps([
            { step: 0, label: 'Step 01', isAccomplished: false },
            { step: 1, label: 'Step 02', isAccomplished: false },
            { step: 2, label: 'Step 03', isAccomplished: false },
            { step: 3, label: 'Step 04', isAccomplished: false },
        ]);
        setCurentStep({ step: 0, label: 'Step 01', isAccomplished: false });
        onClose();
    };

    useEffect(() => {
        const step = getCurrentStep();
        if (step) setCurentStep(step);
    }, [steps]);
    // pointer-events-none

    const IsDesabled = (step) => {
        // console.log(steps.filter(s => s.isAccomplished === true).map(s => s.step).includes(step), step === curentStep.step
        //     , steps.filter(s => s.isAccomplished === true).map(s => s.step), step, steps);

        if (steps.filter(s => s.isAccomplished === true).map(s => s.step).includes(step)) {
            return false
        }
        if (step === curentStep.step) {
            return false
        }

        return true;
    }



    const handleAddProduct = async () => {
        // Validate the form data before dispatching
        const urlRegex = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})?(\/[a-zA-Z0-9-._~:?#%&=]*)?$/;

        if (formData.productLink && urlRegex.test(formData.productLink.trim()) === false && formData.productLink !== null) {
            toast.error("Product link is not valid, please enter a valid URL.");
            setCurrentTabs(1);
            return;
        }

        if (formData.productVideo && urlRegex.test(formData.productVideo.trim()) === false && formData.productVideo !== null) {
            toast.error("Product video link is not valid, please enter a valid URL.");
            setCurrentTabs(1);
            return;
        }

        const res = await dispatch(createProduct({ formData }));

        if (createProduct.fulfilled.match(res)) {

            OnCloseModal();
            console.log("Product added successfully");
        }

    }



    const renderComponent = () => {
        switch (currentTabs) {
            case 0:
                return <Step01 formData={formData} handleInputChange={handleInputChange} IsDesabled={IsDesabled} productsTypes={productsTypes} productsCategories={productsCategories} />;
            case 1:
                return <Step02 formData={formData} handleInputChange={handleInputChange} IsDesabled={IsDesabled} />;
            case 2:
                return <Step03 isValide={isValide} formData={formData} handleInputChange={handleInputChange} IsDesabled={IsDesabled} />;
            case 3:
                return <Step04
                    file={imageFile}
                    handleFileUpload={handleFileUpload}

                />;
            default:
                return <div>Select a tab</div>;
        }
    };

    const validateStep = (step) => {
        switch (step) {
            case 0:
                return isValidString(formData.productName) && isValidString(formData.sku) && formData.productType !== null && formData.category !== null;
            case 1:
                return true;//isValidString(formData.hscode) && isValidString(formData.productVideo) && formData.productLink !== null;
            case 2:
                return true;//isValidString(formData.weight) && isValidString(formData.width) && isValidString(formData.height) && isValidString(formData.length) && isValidString(formData.noteForCallCenter);
            case 3:
                return true;//isValidString(formData.image);
            default:
                return true;
        }
    };

    useEffect(() => {
        setSteps((prev) =>
            prev.map((step, index) => ({
                ...step,
                isAccomplished: !!validateStep(index),
            }))
        );

    }, [formData]);

    const AddProductFooter = () => {
        const errors = {
            productName: !formData.productName ? "Product Name is required" : null,
            sku: !formData.sku ? "SKU is required" : null,
            productType: !formData.productType ? "Product Type is required" : null,
            category: !formData.category ? "Category is required" : null,
        };

        const hasErrors = Object.values(errors).some((error) => error !== null);

        if (currentTabs !== 3) {
            return (
                <div className="flex justify-end items-center gap-2 w-full">
                    <Button
                        isIconOnly
                        isDisabled={currentTabs === 0}
                        className="rounded-full border-2 border-glb_blue bg-transparent text-glb_blue"
                        color="primary"
                        onClick={handleBack}
                    >
                        <ArrowLeft01Icon />
                    </Button>
                    <Button
                        isIconOnly
                        isDisabled={currentTabs === steps.length - 1}
                        className="rounded-full border-2 border-glb_blue bg-transparent text-glb_blue"
                        color="primary"
                        onClick={handleNext}
                    >
                        <ArrowRight01Icon />
                    </Button>
                </div>
            );
        }

        return (
            <div className="flex justify-end items-center gap-2 w-full">
                <Button
                    isIconOnly
                    isDisabled={currentTabs === 0}
                    className="rounded-full border-2 border-glb_blue bg-transparent text-glb_blue"
                    color="primary"
                    onClick={handleBack}
                >
                    <ArrowLeft01Icon />
                </Button>
                <Tooltip
                    content={
                        hasErrors && (
                            <div className="text-sm p-2">
                                {Object.entries(errors).map(
                                    ([key, error]) =>
                                        error && (
                                            <div key={key} className="text-white">
                                                {error}
                                            </div>
                                        )
                                )}
                            </div>
                        )
                    }
                    color="danger"
                    placement="top"
                    isDisabled={!hasErrors}
                    isVisible={hasErrors}
                >
                    <div className={`${hasErrors ? 'cursor-not-allowed' : ''} w-fit`}>
                        <Button
                            variant="solid"
                            onClick={handleAddProduct}
                            isDisabled={hasErrors}
                            isLoading={loading}
                            className="text-xs p-2 md:text-base md:p-4 px-8 rounded-full bg-glb_blue text-white"
                        >
                            Add Product
                        </Button>
                    </div>
                </Tooltip>
            </div>
        );
    };

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={OnCloseModal}
            width="max-w-6xl"
            showHeader={true}
            title="Add New Product"
            headerClassName="px-0 lg:px-8 py-4 border-b"
            bodyClassName="p-0 lg:p-8"
            showFooter={true}
            footerContent={<AddProductFooter />}
        >
            <div className="w-full">
                <Navigator steps={steps} CurrentStep={curentStep} currentTab={currentTabs} setCurrentTabs={setCurrentTabs} />
                <div className="w-full my-10">
                    {renderComponent()}
                </div>
            </div>
        </CustomModal>
    );
};

export default AddProductModal;