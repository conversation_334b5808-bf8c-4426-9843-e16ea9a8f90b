import React, { useState } from 'react'

export default function CODTable({ columns, filteredData, enablePagination = true, }) {
    const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

    // Sorting function
    const sortedData = React.useMemo(() => {
        if (sortConfig.key) {
            return [...filteredData].sort((a, b) => {
                const aValue = a[sortConfig.key];
                const bValue = b[sortConfig.key];

                if (aValue < bValue) {
                    return sortConfig.direction === 'asc' ? -1 : 1;
                }
                if (aValue > bValue) {
                    return sortConfig.direction === 'asc' ? 1 : -1;
                }
                return 0;
            });
        }
        return filteredData;
    }, [filteredData, sortConfig]);

    // Handle click on a sortable column header
    const handleSort = (key) => {
        let direction = 'asc';
        if (sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
    };

    return (
        <div className="w-full mx-4 lg:mx-0">
            {/* Wrapper div for table and pagination to control the fixed height */}
            <div className="min-h-[calc(3rem_*_12)] flex flex-col justify-between">
                {/* Add overflow-x-auto to enable horizontal scrolling */}
                <div ref={tableRef} className="overflow-x-auto flex-grow">
                    <table>
                        <thead>
                            <tr>
                                {columns.map((column) => (
                                    <th
                                        key={column.key}
                                        onClick={() => column.sortable && handleSort(column.key)}
                                        style={{ cursor: column.sortable ? 'pointer' : 'default' }}
                                    >
                                        {column.label}
                                        {column.sortable &&
                                            (sortConfig.key === column.key
                                                ? sortConfig.direction === 'asc'
                                                    ? ' 🔼'
                                                    : ' 🔽'
                                                : '')}
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody>
                            {sortedData.map((row, index) => (
                                <tr key={index}>
                                    {columns.map((column) => (
                                        <td key={column.key}>{row[column.key]}</td>
                                    ))}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {enablePagination && (
                    <div className="pagination-container flex justify-center items-center my-4 space-x-2">
                        <button
                            className="px-3 py-1 bg-gray-200 dark:bg-[#1a1a1a] dark:text-white rounded flex items-center space-x-1 text-sm"
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 1}
                        >
                            <ArrowLeft01Icon size={ICON_SIZE} /> <span>Previous</span>
                        </button>

                        {[...Array(totalPages)].map((_, index) => (
                            <button
                                key={index}
                                className={`px-3 py-1 text-sm ${currentPage === index + 1
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-gray-200 dark:bg-gray-600 dark:text-white'
                                    } rounded`}
                                onClick={() => handlePageChange(index + 1)}
                            >
                                {index + 1}
                            </button>
                        ))}

                        <button
                            className="px-3 py-1 bg-gray-200 dark:bg-[#1a1a1a] dark:text-white rounded flex items-center space-x-1 text-sm"
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages}
                        >
                            <span>Next</span> <ArrowRight01Icon size={ICON_SIZE} />
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};
