import React, { useState, useEffect } from "react";
import VerificationInput from "react-verification-input";
import PropTypes from "prop-types";

/**
 * Custom OTP Input component that wraps react-verification-input
 * @param {Object} props - Component props
 * @param {number} props.length - Number of OTP digits (default: 6)
 * @param {function} props.onChange - Callback function when OTP changes
 * @param {string} props.value - Current OTP value
 * @param {string} props.placeholder - Placeholder character (default: "•")
 * @param {boolean} props.autoFocus - Whether to autofocus the input (default: true)
 * @param {string} props.validChars - Valid characters for the input (default: "0-9")
 * @param {string} props.errorMessage - Error message to display
 * @param {boolean} props.isInvalid - Whether the input is invalid
 * @returns {JSX.Element} InputOTP component
 */
const InputOTP = ({
  length = 6,
  onChange,
  value = "",
  placeholder = "•",
  autoFocus = true,
  validChars = "0-9",
  errorMessage,
  isInvalid = false,
  ...props
}) => {
  const [code, setCode] = useState(value);

  useEffect(() => {
    if (value !== code) {
      setCode(value);
    }
  }, [value]);

  const handleChange = (newCode) => {
    setCode(newCode);
    if (onChange) {
      onChange(newCode);
    }
  };

  return (
    <div className="w-full">
      <VerificationInput
        value={code}
        onChange={handleChange}
        length={length}
        placeholder={placeholder}
        autoFocus={autoFocus}
        validChars={validChars}
        classNames={{
          container: "mx-auto w-full flex justify-center",
          character: `bg-white dark:text-white dark:bg-zinc-900 ${isInvalid ? "border-glb_red" : "dark:border-zinc-800 border-gray-200"
            } rounded w-12 h-12 text-xl flex items-center justify-center`,
          characterInactive: "character--inactive",
          characterSelected: `character--selected ${isInvalid ? "!border-glb_red" : "!border-primary dark:!border-glb_blue"
            }`,
          characterFilled: "character--filled",
        }}
        {...props}
      />
      {isInvalid && errorMessage && (
        <span className="text-glb_red my-2 text-sm block text-start">
          {errorMessage}
        </span>
      )}
    </div>
  );
};

InputOTP.propTypes = {
  length: PropTypes.number,
  onChange: PropTypes.func,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  autoFocus: PropTypes.bool,
  validChars: PropTypes.string,
  errorMessage: PropTypes.string,
  isInvalid: PropTypes.bool,
};

export default InputOTP;
