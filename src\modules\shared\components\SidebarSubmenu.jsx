import SidebarMenuItem from "@shared/components/SidebarMenuItem.jsx";
import SidebarLinkGroup from "@shared/components/SidebarLinkGroup.jsx";
import { useContext } from "react";
import { SidebarContext } from "@/core/providers/SidebarContext.jsx";

const SidebarSubmenu = ({ item, pathname, sidebarExpanded, setSidebarExpanded }) => {
    const { activeSubmenuId, setActiveSubmenuId } = useContext(SidebarContext);
    const isActive = pathname.includes(item.path) ||
        item.children?.some(child => pathname.includes(child.path));
    const isOpen = (activeSubmenuId === item.path || (isActive && activeSubmenuId === null)) && sidebarExpanded;

    const handleClick = (e) => {
        e.preventDefault();
        setActiveSubmenuId(isOpen ? null : item.path);
        setSidebarExpanded(true);
    };

    return (
        <SidebarLinkGroup activeCondition={isActive}>
            {() => (
                <>
                    <a
                        href={item.path}
                        className={`block transition-all duration-300 ease-in-out rounded-xl hover:bg-gray-200 dark:hover:bg-zinc-800/60 
                            ${isActive
                                ? "text-white bg-glb_blue dark:bg-zinc-800/60 hover:bg-info/60"
                                : "hover:text-gray-900 dark:hover:text-white"
                            } ${sidebarExpanded ? 'w-full px-2 py-3.5' : 'w-12 py-2 px-1.5'}`}
                        onClick={handleClick}
                    >
                        <div className="flex items-center justify-center">
                            <div className={`flex items-center ${sidebarExpanded ? 'w-full' : 'w-6'}`}>
                                {item.icon && (
                                    <item.icon
                                        className={`${isActive ? 'dark:text-glb_blue' : ''} shrink-0`}
                                        size={24}
                                    />
                                )}
                                <div className={`overflow-hidden transition-all duration-300 
                                ${sidebarExpanded ? 'w-auto ml-3' : 'w-0'}`}>
                                    <span className="text-sm font-medium whitespace-nowrap">
                                        {item.name}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </a>
                    <div className={`lg:sidebar-expanded:block 2xl:block ${sidebarExpanded ? '' : 'hidden'}`}>
                        <ul className={!isOpen ? 'hidden' : ''}>
                            {item.children.map((subItem, index) => (
                                <SidebarMenuItem
                                    key={index}
                                    item={subItem}
                                    pathname={pathname}
                                    sidebarExpanded={sidebarExpanded}
                                    isSubmenuItem
                                />
                            ))}
                        </ul>
                    </div>
                </>
            )}
        </SidebarLinkGroup>
    );
};

export default SidebarSubmenu;