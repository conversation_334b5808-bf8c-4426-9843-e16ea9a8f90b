import CustomModal from "@shared/components/CustomModal.jsx";
import DataTile from "@shared/components/DataTile.jsx";
import CountryFlag from "@shared/components/CountryFlag.jsx";
import { fetchProductInventory } from "../../../core/redux/slices/stock/products/productsSlice";
import { useDispatch } from "react-redux";
import { useCallback, useEffect, useRef, useState } from "react";
import { countriesUrl } from "../../../core/redux/slices/URLs";
import { getToken } from "../../../core/services/TokenHandler";
import axios from "axios";
import { motion } from "framer-motion";
import { Spinner } from "@heroui/react";

const sampleData = {
    product: {
        productName: "iPhone",
        sku: "2982793783432",
        qty: "122",
        price: "+32 872-8192763",
        size: "Belgium",
        color: "Luxembourg"
    },
    countries: {
        BE: '019',
        AE: '019',
        FR: '019',
        KW: '019',
        OM: '019',
        PH: '019',
        IN: '019',
        SA: '019'
    }
};

const VariantsNav = ({ currentTab, setCurrentTab, NAV_OPT }) => {
    const containerRef = useRef(); // Ref for the parent container
    const itemRefs = useRef({}); // Ref object for each nav item

    const handleTabClick = (id) => {
        setCurrentTab(id);

        // Scroll the clicked tab into view
        const element = itemRefs.current[id];
        if (element) {
            element.scrollIntoView({
                behavior: "smooth", // Smooth scrolling
                block: "center",
                inline: "center", // Ensures the element is centered horizontally
            });
        }
    };

    return (
        <div ref={containerRef} className="w-full px-3 flex justify-start items-center gap-2 overflow-x-auto hide-scrollbar">
            {NAV_OPT.map((navItem) => {
                const isActive = currentTab === navItem.id;
                return (
                    <div
                        ref={(el) => (itemRefs.current[navItem.id] = el)} // Store the ref for each item
                        onClick={() => handleTabClick(navItem.id)}
                        key={navItem.id}
                        className={`cursor-pointer relative rounded-md text-nowrap transition
                            ${isActive ? 'bg-glb_blue text-white px-9 py-2' : '!bg-transparent border-1 !border-black !text-black dark:!border-white dark:!text-white px-4 py-1'}
                          `}
                    >
                        <span className={`text-sm md:text-base lg:text-lg`}>
                            {navItem.name}
                        </span>
                    </div>
                );
            })}
        </div>
    );
};



// Reusable skeleton component with pulsing animation
const SkeletonPulse = ({ className, delay = 0 }) => (
    <motion.div
        className={`bg-gray-200 dark:bg-gray-700 rounded ${className}`}
        initial={{ opacity: 0.5 }}
        animate={{ opacity: [0.5, 0.8, 0.5] }}
        transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay
        }}
    />
);

const ViewProductModal = ({ isOpen, onClose, productId, data = sampleData, productTypes, productCategories }) => {
    const dispatch = useDispatch()

    const [loading, setLoading] = useState(true)
    const [productInventory, setProductInventory] = useState(null)
    const [variants, setVariants] = useState([])
    const [currentVariantsTab, setCurrentVariantsTab] = useState(null)
    const [countries, setCountries] = useState(null)

    const labelClassName = 'w-[40%] truncate text-center text-xs lg:text-base'
    const valueClassName = 'w-[60%] text-center text-sm lg:text-base'

    const onModalClose = () => {
        // Just call the parent's onClose function
        // We'll reset state when the modal reopens
        onClose();
    };




    useEffect(() => {
        // Only fetch data when the modal is open
        if (!isOpen) return;

        setLoading(true);

        const getCountries = async () => {
            try {
                const response = await axios.get(`${countriesUrl}`,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    }
                );

                if (response.data.response !== 'success') {
                    console.error(response.data.message || 'Error fetching orders status');
                    return null;
                }
                // Set the countries array directly from the API response
                return response.data.result;
            } catch (error) {
                console.error("Countries fetch error:", error.response?.data?.message || error.message);
                return null;
            }
        };

        const getProductInventory = async () => {
            try {
                const result = await dispatch(fetchProductInventory({ id: productId }));
                if (fetchProductInventory.fulfilled.match(result)) {
                    return result.payload;
                }
                return null;
            } catch (error) {
                console.error("Product inventory fetch error:", error);
                return null;
            }
        };

        // Run both requests in parallel
        Promise.all([getProductInventory(), getCountries()])
            .then(([productData, countriesData]) => {
                if (productData) {
                    setProductInventory(productData);
                    setVariants(productData.inventory.map(i => ({ name: i.name, id: i.id })));
                    setCurrentVariantsTab(productData.inventory[0]?.id);
                }

                if (countriesData) {
                    setCountries(countriesData);
                }
            })
            .catch(error => {
                console.error("Error loading data:", error);
            })
            .finally(() => {
                setLoading(false);
            });

    }, [isOpen, productId, dispatch])

    const tabs = useCallback(() => {

        if (!productInventory) return [];

        return [
            { label: 'SKU', key: 'sku' },
            { label: 'QTY', key: 'quantity' },
            { label: 'Category', key: 'category' },
            { label: 'Type', key: 'productType' },
        ]
    }, [productInventory])


    const renderDataTile = (label, key) => {
        switch (key) {
            case 'productType':
                return (<DataTile
                    labelClassName={labelClassName}
                    valueClassName={valueClassName}
                    key={key}
                    label={label}
                    value={productTypes[productInventory?.product[key]]}
                />)

            case 'category':
                return (<DataTile
                    labelClassName={labelClassName}
                    valueClassName={valueClassName}
                    key={key}
                    label={label}
                    value={productCategories[productInventory?.product[key]]}
                />)

            default:
                return (<DataTile
                    labelClassName={labelClassName}
                    valueClassName={valueClassName}
                    key={key}
                    label={label}
                    value={productInventory?.product[key]}
                />)
        }
    }

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={onModalClose}
            width="max-w-4xl"
            showHeader={true}
            title={productInventory?.product.name}
            headerClassName="px-0 lg:px-8 py-4 border-b"
            bodyClassName="p-0 lg:p-8"
            showFooter={false}
        >
            <div className="w-full">

                <div className="px-2 lg:px-16 py-2">
                    <div className="grid grid-cols-1 bg-black/5 rounded-2xl overflow-hidden">
                        {loading ? (
                            // Skeleton for product details
                            Array.from({ length: 5 }).map((_, index) => (
                                <div key={index} className="py-4 px-2 lg:px-16 flex justify-between items-center font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                                    <SkeletonPulse className="h-5 w-32" delay={index * 0.1} />
                                    <SkeletonPulse className="h-5 w-48" delay={index * 0.1 + 0.05} />
                                </div>
                            ))
                        ) : (
                            tabs().map(({ label, key }) => (
                                renderDataTile(label, key)
                            ))
                        )}
                    </div>
                </div>

                {productInventory?.product.productType === 'variable' ? <h3 className="font-medium text-lg ml-2 lg:ml-12 my-3">Variants</h3> : <h3 className="font-medium text-lg ml-2 lg:ml-12 my-3">Inventory</h3>}
                {loading ? (
                    // Skeleton for variants navigation
                    <div className="w-full px-3 flex justify-start items-center gap-2 overflow-x-auto hide-scrollbar">
                        {Array.from({ length: 3 }).map((_, index) => (
                            <SkeletonPulse
                                key={index}
                                className="h-10 w-24 rounded-md"
                                delay={index * 0.1}
                            />
                        ))}
                    </div>
                ) : productInventory?.product.productType === 'variable' && (
                    <VariantsNav
                        currentTab={currentVariantsTab}
                        setCurrentTab={setCurrentVariantsTab}
                        NAV_OPT={variants}
                    />
                )}

                <div className="px-2 lg:px-16 py-2">
                    <div className="grid grid-cols-1 bg-black/5 rounded-2xl overflow-hidden">
                        <div>
                            {loading ? (
                                // Skeleton for country details
                                Array.from({ length: 6 }).map((_, index) => (
                                    <div key={index} className="py-4 px-2 lg:px-16 flex justify-between items-center font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                                        <div className="flex items-center">
                                            <SkeletonPulse className="h-4 w-6 mr-2 rounded-sm" delay={index * 0.1} />
                                            <SkeletonPulse className="h-5 w-32" delay={index * 0.1 + 0.05} />
                                        </div>
                                        <SkeletonPulse className="h-5 w-16" delay={index * 0.1 + 0.1} />
                                    </div>
                                ))
                            ) : (
                                <>
                                    <DataTile
                                        labelClassName={labelClassName + ' text-bold'}
                                        valueClassName={valueClassName + ' text-bold'}
                                        key="header"
                                        label="Country"
                                        value="Stock Quantity"
                                    />
                                    {productInventory?.inventory.find(i => i.id === currentVariantsTab)?.details.filter(d => d.leftQty != '0' || d.leftQty != 0).map((d) => (

                                        <DataTile
                                            labelClassName={labelClassName}
                                            valueClassName={valueClassName}
                                            key={d.countryId}
                                            label={(<div className="flex items-center justify-center">
                                                {countries && (() => {


                                                    const country = countries.find(c => c.id === d.countryId);
                                                    return country ? (
                                                        <>
                                                            <img
                                                                alt={country.code || ''}
                                                                src={`https://flagcdn.com/32x24/${country.flag?.toLowerCase() || ''}.png`}
                                                                className={"inline mr-2 h-4 rounded-sm"}
                                                                onError={(e) => {
                                                                    e.target.style.display = 'none';
                                                                    e.target.onerror = null;
                                                                }}
                                                            />
                                                            <span className="truncate">
                                                                {country.name || `Country ID: ${d.countryId}`}
                                                            </span>
                                                        </>
                                                    ) : (
                                                        <span className="truncate">Country ID: {d.countryId}</span>
                                                    );
                                                })()}
                                            </div>)}
                                            value={d.leftQty}
                                        />
                                    ))}
                                </>

                            )}
                        </div>
                    </div>
                </div>
            </div>
        </CustomModal>
    );
};

export default ViewProductModal;