import React, { createContext, useContext, useEffect, useState } from 'react';
import { getUserInfos, getUserPermissions } from '../services/UserHandler';

// Create the Permission Context
const PermissionContext = createContext({
  permissions: [],
  hasPermission: () => false,
  hasConfirmationService: () => false,
  hasShippingService: () => false,
  refreshPermissions: () => { },
  isLoading: true,
});

/**
 * Permission Provider Component
 * 
 * This provider manages user permissions throughout the application.
 * It listens for auth changes and updates permissions accordingly.
 */
export function PermissionProvider({ children }) {
  const [permissions, setPermissions] = useState([]);
  const [userInfos, setuserInfos] = useState({})
  const [isLoading, setIsLoading] = useState(true);

  // Function to check if user has a specific permission
  const hasConfirmationService = () => {
    return userInfos.confirmationService === 'yes';
  };
  const hasShippingService = () => {
    return userInfos.shippingService === 'yes';
  };
  const hasPermission = (permission) => {
    if (!permissions) return false;

    // If permission is an array, check if user has ANY of the permissions
    if (Array.isArray(permission)) {
      return permission.some(p => permissions.includes(p));
    }


    // Check for a single permission
    return permissions.includes(permission);
  };

  // Function to refresh permissions from localStorage
  const refreshPermissions = () => {
    setIsLoading(true);
    const userPermissions = getUserPermissions() || [];
    const userInfos = getUserInfos() || {};
    setPermissions(userPermissions);
    setuserInfos(userInfos)
    setIsLoading(false);
  };

  // Initialize permissions on mount
  useEffect(() => {
    refreshPermissions();
  }, []);


  // Listen for auth changes (login/logout)
  useEffect(() => {

    // Function to handle storage changes
    const handleStorageChange = (e) => {
      if (e.key === "xM_htUju" || e.key === null) {
        refreshPermissions();
      }
    };

    // Function to handle custom auth events
    const handleAuthEvent = () => {
      refreshPermissions();
    };

    // Add event listeners
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('auth-change', handleAuthEvent);

    // Cleanup event listeners on unmount
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('auth-change', handleAuthEvent);
    };
  }, []);

  // Context value
  const contextValue = {
    permissions,
    hasPermission,
    refreshPermissions,
    hasShippingService,
    hasConfirmationService,
    isLoading,
  };

  return (
    <PermissionContext.Provider value={contextValue}>
      {children}
    </PermissionContext.Provider>
  );
}

/**
 * Custom hook to use permissions
 * 
 * Usage:
 * const { permissions, hasPermission } = usePermissions();
 * 
 * if (hasPermission('sellers.followuporders')) {
 *   // Show UI element
 * }
 */
export function usePermissions() {
  const context = useContext(PermissionContext);

  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionProvider');
  }

  return context;
}
