import React from 'react'
import { formatNumber } from '../../../core/utils/functions'
import { useSelector } from 'react-redux'
import { motion, AnimatePresence } from 'framer-motion'

export default function PriceRenderer({ price, additional, additionalClass = null }) {
    const showPrices = useSelector(state => state.content.showPrices)


    return (
        <AnimatePresence >
            <motion.div
                initial={{
                    filter: showPrices ? 'blur(0px)' : 'blur(10px)',
                }}
                animate={{
                    filter: showPrices ? 'blur(0px)' : 'blur(10px)',
                }}
                transition={{
                    duration: 0.4,
                    type: "keyframes",
                    stiffness: 200,
                    damping: 10
                }}
                className={`${!showPrices ? 'cursor-not-allowed' : ''} flex flex-wrap justify-center items-center gap-2 ${additionalClass}`}
            >
                {price && formatNumber(price)}
                <h6>{additional}</h6>
            </motion.div>
        </AnimatePresence>
    )
}
