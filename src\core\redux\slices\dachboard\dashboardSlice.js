
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import { getToken } from "../../../services/TokenHandler";
import { dashboardCallCenterDataUrl, dashboardFollowupDataUrl, dashboardFundsDataUrl, dashboardShippingDataUrl } from "../URLs";


const initialState = {
    shipping: {},
    followup: {},
    callCenter: {},
    loadingShipping: false,
    loadingFollowup: false,
    loadingCallCenter: false,
    loadingFunds: false,
    error: null,
    params: {
        startDate: null,
        endDate: null,
        productReference: null,
        paymentMethod: null,
        sortBy: null,
    },
    paramsCount: 0,
}


// Async thunk for fetching shipping data
export const fetchFundsData = createAsyncThunk(
    "fetchFundsData/get",
    async (type, { rejectWithValue, getState }) => {
        try {
            const { params } = getState().dashboard;
            let query = `${dashboardFundsDataUrl}?type=${type}`;
            params.startDate && (query += `&startDate=${params.startDate}`);
            params.endDate && (query += `&endDate=${params.endDate}`);
            params.productReference && (query += `&productReference=${params.productReference}`);
            //params.paymentMethod && (query += `&paymentMethod=${params.paymentMethod}`);
            // ${params.page}

            const response = await axios.get(query, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching funds data' });
            }



            return response.data.result;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);
// Async thunk for fetching shipping data
export const fetchShippingData = createAsyncThunk(
    "fetchShippingData/get",
    async (_, { rejectWithValue, getState }) => {
        try {
            const { params } = getState().dashboard;
            let query = `${dashboardShippingDataUrl}?count=1`;
            params.startDate && (query += `&startDate=${params.startDate}`);
            params.endDate && (query += `&endDate=${params.endDate}`);
            params.productReference && (query += `&productReference=${params.productReference}`);
            params.paymentMethod && (query += `&paymentMethod=${params.paymentMethod}`);
            // ${params.page}

            const response = await axios.get(query, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching shipping data' });
            }


            return response.data.result;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);

// Async thunk for fetching call center data
export const fetchCallCenterData = createAsyncThunk(
    "fetchCallCenterData/get",
    async (_, { rejectWithValue, getState }) => {
        try {
            const { params } = getState().dashboard;
            let query = `${dashboardCallCenterDataUrl}?count=1`;
            params.startDate && (query += `&startDate=${params.startDate}`);
            params.endDate && (query += `&endDate=${params.endDate}`);
            params.productReference && (query += `&productReference=${params.productReference}`);
            params.paymentMethod && (query += `&paymentMethod=${params.paymentMethod}`);
            // ${params.page}

            const response = await axios.get(query, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching call center data' });
            }

            return response.data.result;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);


// Async thunk for fetching followup data
export const fetchFollwupData = createAsyncThunk(
    "fetchFollwupData/get",
    async (_, { rejectWithValue, getState }) => {
        try {
            const { params } = getState().dashboard;
            let query = `${dashboardFollowupDataUrl}?count=1`;
            params.startDate && (query += `&startDate=${params.startDate}`);
            params.endDate && (query += `&endDate=${params.endDate}`);
            params.productReference && (query += `&productReference=${params.productReference}`);
            params.paymentMethod && (query += `&paymentMethod=${params.paymentMethod}`);
            // ${params.page}

            const response = await axios.get(query, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching followup data' });
            }

            return response.data.result;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);



const dashboardSlice = createSlice({
    name: 'dashboard',
    initialState,
    reducers: {
        updateFilterParams: (state, action) => {
            state.params = { ...state.params, ...action.payload };

            // Calculate paramsCount by counting non-null params
            state.paramsCount = Object.keys(state.params)
                .filter(key => state.params[key] !== null)
                .length;
        },
        resetFilterParams: (state) => {
            state.params = {
                startDate: null,
                endDate: null,
                productReference: null,
                paymentMethod: null,
                sortBy: null,
            };
            state.paramsCount = 0;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchFundsData.pending, (state) => {
                state.loadingFunds = true;
                state.error = null;
            })
            .addCase(fetchFundsData.fulfilled, (state, action) => {
                state.loadingFunds = false;
                state.shipping = action.payload;
            })
            .addCase(fetchFundsData.rejected, (state, action) => {
                state.loadingFunds = false;
                state.error = action.payload;
            })
            .addCase(fetchShippingData.pending, (state) => {
                state.loadingShipping = true;
                state.error = null;
            })
            .addCase(fetchShippingData.fulfilled, (state, action) => {
                state.loadingShipping = false;
                state.shipping = action.payload;
            })
            .addCase(fetchShippingData.rejected, (state, action) => {
                state.loadingShipping = false;
                state.error = action.payload;
            })
            .addCase(fetchCallCenterData.pending, (state) => {
                state.loadingCallCenter = true;
                state.error = null;
            })
            .addCase(fetchCallCenterData.fulfilled, (state, action) => {
                state.loadingCallCenter = false;
                state.callCenter = action.payload;
            })
            .addCase(fetchCallCenterData.rejected, (state, action) => {
                state.loadingCallCenter = false;
                state.error = action.payload;
            })
            .addCase(fetchFollwupData.pending, (state) => {
                state.loadingFollowup = true;
                state.error = null;
            })
            .addCase(fetchFollwupData.fulfilled, (state, action) => {
                state.loadingFollowup = false;
                state.followup = action.payload;
            })
            .addCase(fetchFollwupData.rejected, (state, action) => {
                state.loadingFollowup = false;
                state.error = action.payload;
            })



    },
});

export const { updateFilterParams, resetFilterParams } = dashboardSlice.actions;

export default dashboardSlice.reducer;