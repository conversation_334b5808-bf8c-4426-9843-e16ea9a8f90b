import { useState } from "react";
import { Button } from "@heroui/button";
import { Cancel01Icon, Search01Icon } from "hugeicons-react";
import CountrySelector from "@shared/components/CountrySelector.jsx";
import { SignupSteps } from "@/core/constants/signup.js";
import { COUNTRIES } from "@/core/constants/countries.js";
import { Select, SelectItem } from "@heroui/select";
import CustomModal from "@shared/components/CustomModal.jsx";

export default function FilterModal({ id, modalOpen, setModalOpen }) {

    const [isFromCountryOpen, setIsFromCountryOpen] = useState(false);
    const [isToCountryOpen, setIsToCountryOpen] = useState(false);
    const [fromCountry, setFromCountry] = useState(SignupSteps.defaultCountry);
    const [toCountry, setToCountry] = useState(SignupSteps.defaultCountry);

    return (
        <>
            <CustomModal
                id={id}
                isOpen={modalOpen}
                onClose={() => setModalOpen(false)}
                width="max-w-6xl"
                showHeader={true}
                title="Order N° 01"
                headerClassName="px-8 py-4 border-b"
                bodyClassName="p-8"
                footerContent={
                    <div
                        className="mt-6 pt-4 border-t border-t-gray-200 dark:border-t-[#ffffff10] flex flex-row justify-center items-center gap-4">
                        <Button className="rounded-full bg-glb_blue text-white px-4 py-2"
                            onClick={() => setModalOpen(false)}>
                            <Search01Icon /> Apply & Search
                        </Button>
                        <Button className="rounded-full dark:bg-base_card px-4 py-2"
                            onClick={() => setModalOpen(false)}>
                            <Cancel01Icon size={16} /> Cancel
                        </Button>
                    </div>
                }
            >
                <div>
                    <h4 className="text-lg font-medium">Countries</h4>
                    <div className="flex flex-col lg:flex-row">
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#fromCountry" className="block mr-2">
                                <span className="text-sm text-gray-400">From Country</span>
                                <CountrySelector
                                    open={isFromCountryOpen}
                                    onToggle={() => setIsFromCountryOpen(!isFromCountryOpen)}
                                    id="fromCountry" onChange={(country) => setFromCountry(country)}
                                    selectedValue={COUNTRIES.find(option => option.value === fromCountry)} />
                            </label>
                        </div>
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#toCountry" className="block mt-4 lg:mt-0 lg:ml-2">
                                <span className="text-sm text-gray-400">To Country</span>
                                <CountrySelector
                                    open={isToCountryOpen}
                                    onToggle={() => setIsToCountryOpen(!isToCountryOpen)}
                                    id="toCountry" onChange={(country) => setToCountry(country)}
                                    selectedValue={COUNTRIES.find(option => option.value === toCountry)} />
                            </label>
                        </div>
                    </div>
                    <h4 className="text-lg font-medium mt-6 mb-2">Lists</h4>
                    <div className="flex flex-col lg:flex-row my-2">
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#agents" className="block mr-2">
                                <span className="text-sm text-gray-400">List of agents</span>
                                <Select
                                    id="agents"
                                    placeholder="Select an agent"
                                    labelPlacement="outside"
                                    classNames={{
                                        trigger: 'bg-white dark:bg-transparent focus:border-dark_selected border rounded-md border-black/10 dark:border-white/10 ',
                                    }}>
                                    <SelectItem>
                                        Agent
                                    </SelectItem>
                                </Select>
                            </label>
                        </div>
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#follow-up-agents" className="block mt-4 lg:mt-0 lg:ml-2">
                                <span className="text-sm text-gray-400">List of follow up agents</span>
                                <Select
                                    id="follow-up-agents"
                                    placeholder="Select a follow up agent"
                                    labelPlacement="outside"
                                    classNames={{
                                        trigger: 'bg-white dark:bg-transparent focus:border-dark_selected border rounded-md border-black/10 dark:border-white/10 ',
                                    }}>
                                    <SelectItem>
                                        Agent
                                    </SelectItem>
                                </Select>
                            </label>
                        </div>
                    </div>
                    <div className="flex flex-col lg:flex-row my-4">
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#seller" className="block mr-2">
                                <span className="text-sm text-gray-400">List of sellers *</span>
                                <Select
                                    id="agents"
                                    placeholder="Select a seller"
                                    labelPlacement="outside"
                                    classNames={{
                                        trigger: 'bg-white dark:bg-transparent focus:border-dark_selected border rounded-md border-black/10 dark:border-white/10 ',
                                    }}>
                                    <SelectItem>
                                        Agent
                                    </SelectItem>
                                </Select>
                            </label>
                        </div>
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#product" className="block mt-4 lg:mt-0 lg:ml-2">
                                <span className="text-sm text-gray-400">List of products *</span>
                                <Select
                                    id="product"
                                    placeholder="Select a product"
                                    labelPlacement="outside"
                                    classNames={{
                                        trigger: 'bg-white dark:bg-transparent focus:border-dark_selected border rounded-md border-black/10 dark:border-white/10 ',
                                    }}>
                                    <SelectItem>
                                        Agent
                                    </SelectItem>
                                </Select>
                            </label>
                        </div>
                    </div>
                    <div className="flex flex-col lg:flex-row my-4">
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#affiliate" className="block mr-2">
                                <span className="text-sm text-gray-400">List of affiliates *</span>
                                <Select
                                    id="affiliate"
                                    placeholder="Select an affiliate"
                                    labelPlacement="outside"
                                    classNames={{
                                        trigger: 'bg-white dark:bg-transparent focus:border-dark_selected border rounded-md border-black/10 dark:border-white/10 ',
                                    }}>
                                    <SelectItem>
                                        Agent
                                    </SelectItem>
                                </Select>
                            </label>
                        </div>
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#store" className="block mt-4 lg:mt-0 lg:ml-2">
                                <span className="text-sm text-gray-400">List of stores *</span>
                                <Select
                                    id="store"
                                    placeholder="Select a store"
                                    labelPlacement="outside"
                                    classNames={{
                                        trigger: 'bg-white dark:bg-transparent focus:border-dark_selected border rounded-md border-black/10 dark:border-white/10 ',
                                    }}
                                    color="default">
                                    <SelectItem>
                                        Agent
                                    </SelectItem>
                                </Select>
                            </label>
                        </div>
                    </div>
                    <h4 className="text-lg font-medium mt-6 mb-2">Confirmation</h4>
                    <div className="flex flex-col lg:flex-row my-2">
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#agents" className="block mr-2">
                                <span className="text-sm text-gray-400">Confirmed by</span>
                                <Select
                                    id="agents"
                                    placeholder="Confirmed by"
                                    labelPlacement="outside"
                                    classNames={{
                                        trigger: 'bg-white dark:bg-transparent focus:border-dark_selected border rounded-md border-black/10 dark:border-white/10 ',
                                    }}>
                                    <SelectItem>
                                        Agent
                                    </SelectItem>
                                </Select>
                            </label>
                        </div>
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#follow-up-agents" className="block mt-4 lg:mt-0 lg:ml-2">
                                <span className="text-sm text-gray-400">Confirmed via</span>
                                <Select
                                    id="follow-up-agents"
                                    placeholder="Confirmed via"
                                    labelPlacement="outside"
                                    classNames={{
                                        trigger: 'bg-white dark:bg-transparent focus:border-dark_selected border rounded-md border-black/10 dark:border-white/10 ',
                                    }}>
                                    <SelectItem>
                                        Whatsapp
                                    </SelectItem>
                                </Select>
                            </label>
                        </div>
                    </div>
                    <h4 className="text-lg font-medium mt-6 mb-2">Channels</h4>
                    <div className="flex flex-col lg:flex-row my-2">
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#agents" className="block mr-2">
                                <span className="text-sm text-gray-400">Sales Channel *</span>
                                <Select
                                    id="agents"
                                    placeholder="Select a channel"
                                    labelPlacement="outside"
                                    classNames={{
                                        trigger: 'bg-white dark:bg-transparent focus:border-dark_selected border rounded-md border-black/10 dark:border-white/10 ',
                                    }}>
                                    <SelectItem>
                                        Chanel
                                    </SelectItem>
                                </Select>
                            </label>
                        </div>
                    </div>
                    <h4 className="text-lg font-medium mt-6 mb-2">Companies</h4>
                    <div className="flex flex-col lg:flex-row my-2">
                        <div className="w-full lg:w-1/2">
                            <label htmlFor="#agents" className="block mr-2">
                                <span className="text-sm text-gray-400">Shopping Company *</span>
                                <Select
                                    id="agents"
                                    placeholder="Select a company"
                                    labelPlacement="outside"
                                    classNames={{
                                        trigger: 'bg-white dark:bg-transparent focus:border-dark_selected border rounded-md border-black/10 dark:border-white/10 ',
                                    }}>
                                    <SelectItem>
                                        Chanel
                                    </SelectItem>
                                </Select>
                            </label>
                        </div>
                    </div>
                </div>
            </CustomModal>
        </>
    );
}