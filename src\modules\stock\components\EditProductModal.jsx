import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";

// ---------- NextUI / Shared Imports ----------
import { Select, SelectItem } from "@heroui/select";
import { Button } from "@heroui/button";
import { AnimatePresence, motion } from "framer-motion";

// ---------- Hu<PERSON><PERSON>cons ----------
import {
  ArrowDown01Icon,
  ArrowExpandIcon,
  ArrowLeft01Icon,
  ArrowUp01Icon,
  Bookmark03Icon,
  Cancel01Icon,
  CancelCircleIcon,
  CloudUploadIcon,
  Delete02Icon,
  DeliveryBox01Icon,
  Dollar02Icon,
  PencilEdit01Icon,
  PlusSignIcon,
  Tag01Icon,
  Upload04Icon,
} from "hugeicons-react";

// ---------- Redux ----------
import { useDispatch, useSelector } from "react-redux";
import {
  fetchSalesPrices,
  createSalesPrice,
  updateSalesPrice as updateSalesPriceThunk,
  deleteSalesPrice as deleteSalesPriceThunk
} from "../../../core/redux/slices/stock/salesPrice/salesPriceSlice";
import {
  fetchUpsells,
  createUpsell,
  updateUpsell as updateUpsellThunk,
  deleteUpsell as deleteUpsellThunk,
} from "@/core/redux/slices/stock/upsell/upsellSlice";
// ---------- Local Components / Helpers ----------
import CustomModal from "@shared/components/CustomModal.jsx";
import CustomTable from "../../shared/components/CustomTable";
import UnderlinedInput from "../../settings/components/UnderlinedInput";
import { toSlug } from "../../../core/utils/functions";

// Example data / product arrays (your existing)
import {
  generateRandomData,
  generateRandomSalesPricesData,
  GetCategoryFromSet,
  GetCategorySetByName,
  GetTypeFromSet,
  GetTypeSetByName,
  ProductCategory,
  ProductType,
} from "../../../core/utils/ProductData";
import { fetchProductDetails, fetchProductsCategories, fetchProductsTypes, UpdateProduct } from "../../../core/redux/slices/stock/products/productsSlice";
import { Popover, PopoverContent, PopoverTrigger, Spinner, Tooltip } from "@heroui/react";
import { getCurrencies } from "../../../core/redux/slices/general/generalSlice";
import GeneralSelector from "../../shared/components/GeneralSelector";
import { addFromAttributes, createAttributes, createVaraints, deleteAttributes, deleteVariant, fetchAttributes, fetchVaraints, updateAttributes, updateVariant } from "../../../core/redux/slices/stock/variants/variantSlice";
import { set, update } from "lodash";
import { toast } from "sonner";

// -------------------------------------------------------------------
// SAMPLE NAV OPTIONS
// -------------------------------------------------------------------
const NAV_OPT = [
  { icon: <DeliveryBox01Icon />, label: "Informations" },
  { icon: <ArrowExpandIcon />, label: "Variants" },
  { icon: <Tag01Icon className="scale-y-[-1]" />, label: "Sales Price" },
  { icon: <Dollar02Icon />, label: "Upsell" },
];

// -------------------------------------------------------------------
// Navigator (Top Tabs Buttons)
// -------------------------------------------------------------------
const Navigator = ({ currentTab, variant, setCurrentTab, isDisabled }) => {
  const containerRef = useRef();
  const itemRefs = useRef([]);

  const handleTabClick = (slug, idx) => {
    if (slug !== toSlug('Variants') || variant !== 1) {
      setCurrentTab(slug);
    }

    const element = itemRefs.current[idx];
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "center", inline: "center" });
    }
  };

  return (
    <div
      ref={containerRef}
      className={` w-full max-w-[600px] mx-auto hide-scrollbar flex justify-center items-center gap-2`}
    >
      {NAV_OPT.map((navItem, idx) => {
        const slug = toSlug(navItem.label);
        const isActive = currentTab === slug;
        return (
          <Tooltip content={'Please save the product type modification first'} isDisabled={!(variant === 1 && navItem.label === 'Variants')} color="danger" key={idx} placement="top">
            <div

              ref={(el) => (itemRefs.current[idx] = el)}
              onClick={() => handleTabClick(slug, idx)}
              key={idx}
              className={`${variant === 0 && navItem.label === 'Variants' && 'hidden'} relative flex justify-center items-center gap-2 py-2 px-6 text-xs text-nowrap cursor-pointer hover:opacity-50 rounded-full h-10
              ${variant === 1 && navItem.label === 'Variants'
                  ? "cursor-not-allowed dark:bg-[#ffffff30] dark:text-[#ffffff10] bg-[#00000010] text-[#00000030]"
                  : isActive
                    ? "bg-glb_blue text-white"
                    : "bg-transparent text-black dark:text-white border-1 border-black dark:border-white"
                }`}
            >
              {navItem.icon}
              {navItem.label}
            </div></Tooltip>
        );
      })}
    </div>
  );
};

// -------------------------------------------------------------------
// 1) "Informations" Tab
// -------------------------------------------------------------------
const Informations = ({ productId, productsTypes, setProductTypeForNav, productCategories, onClose }) => {

  const [product, setproduct] = useState(null)
  const [loading, setLoading] = useState(false)
  const dispatch = useDispatch()
  const [isValide, setIsValide] = useState({})
  const [imageFile, setImageFile] = useState(null)
  const [imageUrl, setImageUrl] = useState(null)




  useEffect(() => {
    const fetchProduct = async () => {
      if (productId) {
        setLoading(true);
        const result = await dispatch(
          fetchProductDetails({ id: productId })
        );
        if (fetchProductDetails.fulfilled.match(result)) {
          const productData = result.payload;
          setproduct(productData);

          // Set the image URL if it exists
          if (productData?.mainImage) {
            setImageUrl(productData.mainImage);
          }
        }
        setLoading(false);
      }
    };
    fetchProduct();
  }, [productId, dispatch]);




  const handleInputChange = React.useCallback((field, value) => {
    if (['weight', 'length', 'width', 'height'].includes(field)) {

      if (value === "." || /^\d*\.?\d*$/.test(value)) {
        setproduct((prev) => ({ ...prev, [field]: value }));
        setIsValide((prev) => {
          if (field in prev) { // Check if the field exists
            const updated = { ...prev }; // Create a copy of the current state
            delete updated[field]; // Remove the specific field
            return updated; // Return the updated state
          }
          return prev; // If the field doesn't exist, return the previous state
        });
      } else {
        setIsValide((prev) => ({
          ...prev,
          [field]: 'Number is required',
        }));
      }
    } else {
      setproduct((prev) => ({ ...prev, [field]: value }));
    }
  }, []);

  const handleFileUpload = (file) => {
    setImageFile(file);

    // If we're clearing the file (file is null), and there's no imageUrl,
    // then we're completely removing the image
    if (!file && !imageUrl) {
      setproduct((prev) => ({
        ...prev,
        mainImage: null,
        image: null

      }));
    }
    // If we're setting a new file, update the mainImage property
    else if (file) {
      setproduct((prev) => ({
        ...prev,
        mainImage: file.name, // For new uploads, we'll use the file name
        image: file
      }));
    }
    // If we're clearing the file but there's still an imageUrl, keep the imageUrl
    else if (!file && imageUrl) {
      // Don't change the mainImage property, keep the existing URL
    }
  };


  const handleUpdateProduct = async () => {
    setLoading(true);
    const urlRegex = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})?(\/[a-zA-Z0-9-._~:?#%&=]*)?$/;

    if (product.productLink && urlRegex.test(product.productLink.trim()) === false && product.productLink !== null) {
      toast.error("Product link is not valid, please enter a valid URL.");
      setLoading(false);
      return;
    }

    if (product.productVideo && urlRegex.test(product.productVideo.trim()) === false && product.productVideo !== null) {
      toast.error("Product video link is not valid, please enter a valid URL.");
      setLoading(false);
      return;
    }
    const res = await dispatch(UpdateProduct({ product }));
    if (UpdateProduct.fulfilled.match(res)) {
      onClose()
    }
    setLoading(false);

  }
  return (
    <>
      <div className="md:w-fit mt-10 px-0 md:px-10 xl:px-20 mx-auto flex items-start gap-6 flex-col max-w-[1000px]">
        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4">
          <UnderlinedInput onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleShopifyConnect();
            }
          }} label="Product Name" onChange={(e) => handleInputChange('name', e.target.value)} value={product?.name} isDisabled={loading} start={true} startContent={loading && <Spinner size="sm" />} />
          <UnderlinedInput onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleShopifyConnect();
            }
          }} label="Arabic Name" onChange={(e) => handleInputChange('arabicName', e.target.value)} value={product?.arabicName} isDisabled={loading} start={true} startContent={loading && <Spinner size="sm" />} />
        </div>
        <div className="w-full grid grid-cols-1 gap-4">
          <UnderlinedInput onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleShopifyConnect();
            }
          }} label="SKU" onChange={(e) => handleInputChange('sku', e.target.value)} value={product?.sku} isDisabled={loading} start={true} startContent={loading && <Spinner size="sm" />} />
        </div>
        <div className="w-full grid grid-cols-1 gap-4">
          <Select
            isDisabled={loading}
            startContent={loading && <Spinner size="sm" />}
            selectedKeys={[
              Object.keys(productsTypes).find(
                (key) => key === product?.productType
              ),
            ]}

            onChange={(e) => {
              setproduct((prev) => ({ ...prev, productType: e.target.value }));
              setProductTypeForNav((prev) => ({ ...prev, productType: e.target.value }));
            }}
            variant="underlined"
            color="primary"
            label="Select Product Type"
            className="w-full"
            classNames={{
              label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],
            }}
          >
            {Object.entries(productsTypes).map(([key, value]) => (
              <SelectItem key={key}>{value}</SelectItem>
            ))}
          </Select>
        </div>
        <div className="w-full grid grid-cols-1 gap-4">
          <Select
            isDisabled={loading}
            startContent={loading && <Spinner size="sm" />}
            selectedKeys={[
              Object.keys(productCategories).find(
                (key) => key === product?.category
              ),
            ]}

            onChange={(e) => {
              setproduct((prev) => ({ ...prev, category: e.target.value }));
            }}
            variant="underlined"
            color="primary"
            label="Select Product Category"
            className="w-full"
            classNames={{
              label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],
            }}
          >
            {Object.entries(productCategories).map(([key, value]) => (
              <SelectItem key={key}>{value}</SelectItem>
            ))}
          </Select>
        </div>
      </div>
      {/* Dimensions, weight, etc. */}
      <div className="md:w-fit mt-10 px-0 md:px-10 xl:px-20 mx-auto flex items-start gap-6 flex-col max-w-[1000px]">
        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4">
          <UnderlinedInput onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleUpdateProduct();
            }
          }} isInvalid={isValide['weight']} label="Weight (Kg)" onChange={(e) => handleInputChange('weight', e.target.value)} value={product?.weight} isDisabled={loading} start={true} startContent={loading && <Spinner size="sm" />} />
          <UnderlinedInput onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleUpdateProduct();
            }
          }} isInvalid={isValide['Width']} label="Width (Cm)" onChange={(e) => handleInputChange('width', e.target.value)} value={product?.width} isDisabled={loading} start={true} startContent={loading && <Spinner size="sm" />} />
        </div>
        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4">
          <UnderlinedInput onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleUpdateProduct();
            }
          }} isInvalid={isValide['Height']} label="Height (Cm)" onChange={(e) => handleInputChange('height', e.target.value)} value={product?.height} isDisabled={loading} start={true} startContent={loading && <Spinner size="sm" />} />
          <UnderlinedInput onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleUpdateProduct();
            }
          }} isInvalid={isValide['Length']} label="Length (Cm)" onChange={(e) => handleInputChange('length', e.target.value)} value={product?.length} isDisabled={loading} start={true} startContent={loading && <Spinner size="sm" />} />
        </div>
        <div className="w-full grid grid-cols-1 gap-4">
          <UnderlinedInput
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleUpdateProduct();
              }
            }} label="HS Code"
            onChange={(e) => handleInputChange('hscode', e.target.value)}
            value={product?.hscode} isDisabled={loading} start={true}
            startContent={loading && <Spinner size="sm" />} />
          <UnderlinedInput
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleUpdateProduct();
              }
            }} label="Product Video"
            onChange={(e) => handleInputChange('productVideo', e.target.value)}
            value={product?.productVideo} isDisabled={loading} start={true}
            startContent={loading && <Spinner size="sm" />} />
          <UnderlinedInput
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleUpdateProduct();
              }
            }} label="Product Link"
            onChange={(e) => handleInputChange('productLink', e.target.value)}
            value={product?.productLink} isDisabled={loading} start={true}
            startContent={loading && <Spinner size="sm" />} />



        </div>

        <div className="w-full grid grid-cols-1 gap-4">
          <UnderlinedInput onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleUpdateProduct();
            }
          }} label="Note for Call Center" onChange={(e) => handleInputChange('descriptionCallcenter', e.target.value)} value={product?.descriptionCallcenter} isDisabled={loading} start={true} startContent={loading && <Spinner size="sm" />} />
        </div>
      </div>
      {/* Image */}
      <div className="md:w-fit mt-10 md:px-10 xl:px-20 mx-auto flex items-start gap-6 flex-col max-w-[1000px]">
        <div className="w-full grid grid-cols-1 gap-4">
          {/* Image uploader with preview */}
          <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800">
            {imageFile ? (
              // Show newly uploaded file
              (<>
                <div className="relative w-full h-full">
                  <img
                    src={URL.createObjectURL(imageFile)}
                    alt="Uploaded Preview"
                    className="h-full w-full object-cover rounded-lg"
                  />
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      handleFileUpload(null);
                    }}
                    className="absolute top-2 right-2 text-white rounded-full p-1 hover:text-glb_red"
                  >
                    <CancelCircleIcon />
                  </button>
                </div>
              </>)
            ) : imageUrl ? (
              // Show existing image from URL
              (<>
                <div className="relative w-full h-full">
                  <img
                    src={imageUrl}
                    alt="Product Image"
                    className="h-full w-full object-cover rounded-lg"
                  />
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      setImageUrl(null);
                      setproduct(prev => ({ ...prev, mainImage: null }));
                    }}
                    className="absolute top-2 right-2 text-white rounded-full p-1 hover:text-glb_red"
                  >
                    <CancelCircleIcon />
                  </button>
                </div>
              </>)
            ) : (
              // Show upload placeholder
              (<div className="flex flex-col items-center justify-center pt-5 pb-6 m-8">
                <CloudUploadIcon className="w-10 h-10 text-gray-400" />
                <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                  <span className="font-semibold">Click to upload</span> or drag and drop
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">PNG or JPG (max 10MB)</p>
              </div>)
            )}
            <input
              type="file"
              accept=".png, .jpg, .jpeg"
              className="hidden"
              onChange={(e) => {
                const file = e.target.files[0];
                if (file && file.size <= 10 * 1024 * 1024) {
                  handleFileUpload(file);
                  setImageUrl(null); // Clear the image URL when a new file is selected
                } else {
                  toast.error("File must be PNG or JPG and less than 10MB.");
                }
              }}
            />
          </label>
          {imageFile && (
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {imageFile.name}
            </span>
          )}
          {imageUrl && !imageFile && (
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {imageUrl.split('/').pop()}
            </span>
          )}
        </div>
      </div>
      {/* Save Button */}
      <div className="md:w-fit mt-10 md:px-10 xl:px-20 mx-auto flex items-start gap-6 flex-col max-w-[1000px]">
        <Button isLoading={loading} onClick={handleUpdateProduct} color="default" className="rounded-full bg-glb_blue text-white">
          Save Changes
        </Button>
      </div>
    </>
  );
};

// -------------------------------------------------------------------
// 2) "Variants" Tab (unchanged from your code)
// -------------------------------------------------------------------
const Variants = ({ productId, data, renderCell, editRowId, handleCancel, handleSave, addVariation }) => {
  const columns = [
    { key: "name", label: "Name", w: "w-[40%]" },
    { key: "sku", label: "SKU", w: "w-[40%]" },
    { key: "actions", label: "Actions", w: "w-[10%]" },
  ];
  const addFromAttributescolumns = [
    { key: "name", label: "Attribute", w: "w-[40%]" },
    { key: "value", label: "Values", w: "w-[40%]" },
    { key: "actions", label: "Actions", w: "w-[10%]" },
  ];
  const variantAttributescolumns = [
    { key: "name", label: "Attribute", w: "w-[40%]" },
    { key: "values", label: "Values", w: "w-[40%]" },
    { key: "actions", label: "Actions", w: "w-[10%]" },
  ];

  const { attributes, loading: variantLoading } = useSelector((state) => state.variants);
  const dispatch = useDispatch();


  const [CreateFromAttribute, setCreateFromAttribute] = useState(false)
  const [showAttributeManagement, setShowAttributeManagement] = useState(false)
  const [attributeEditRow, setAttributeEditRow] = useState(null)
  const [attributeNewItem, setAttributeNewItem] = useState(null)
  const [attributeData, setAttributeData] = useState(null)
  const [attributeEditFormData, setAttributeEditFormData] = useState({});
  const [values, setValues] = useState([])
  const [expandedRow, setExpandedRow] = useState(null)
  const [variantAttribute, setVariantAttribute] = useState(null)

  useEffect(() => {
    const mapped = attributes.map((v) => ({
      key: v.id, // Make sure key matches id
      id: v.id,
      name: v.name,
      values: v.value,
      value: '',

    }));
    setAttributeData(mapped);


  }, [attributes]);

  useEffect(() => {
    const mapped = attributes.map((v) => ({
      key: v.id, // Make sure key matches id
      id: v.id,
      name: v.name,
      values: v.value,
      value: '',

    }));
    setVariantAttribute(mapped);
  }, [attributes]);



  useEffect(() => {
    if (attributeEditRow && attributeData) {
      const foundAttribute = attributeData.find(a => a.id === attributeEditRow);
      if (foundAttribute && foundAttribute.values) {
        // Only split if values is a string
        if (typeof foundAttribute.values === 'string') {
          // Split by semicolon instead of comma, and remove empty values
          setValues(foundAttribute.values.split(';').filter(value => value !== ""));
        } else {
          setValues([]);
        }
      } else {
        // If attribute not found or has no values, set empty array
        setValues([]);
      }
    }
  }, [attributeEditRow, attributeData])




  // sub-row for extra prices
  const rowDetails = useCallback((item) => {

    // Check if expandedRow matches either item.key or item.id
    if (expandedRow !== item.key && expandedRow !== item.id) return null;

    const isEditing = attributeEditRow === item.id;
    // If in editing mode => show values array
    const valueArray = values || [];

    return (
      <AnimatePresence>
        <tr key={"expand-" + item.id}>
          <td colSpan={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
              className="flex flex-wrap items-center gap-2 py-2"
            >
              {valueArray.map((p, idx) => {
                return (
                  <div
                    key={idx}
                    className="p-2 bg-glb_blue rounded-lg text-white text-sm flex justify-center items-center"
                  >
                    {p}

                    <Cancel01Icon size={15} className="cursor-pointer" onClick={() => {
                      const updatedValues = values.filter((_, index) => index !== idx);
                      setValues(updatedValues);
                    }} />
                  </div>
                );
              })}
            </motion.div>
          </td>
        </tr>
      </AnimatePresence>
    );
  }, [expandedRow, values, attributeEditRow]);

  const handleAttributeEditClick = (row) => {
    setAttributeEditRow(row.id);
    setAttributeEditFormData({ ...row });
    setExpandedRow(row.id)
  };
  const removeAttributeElement = async (keyToRemove) => {
    const result = await dispatch(deleteAttributes({ id: keyToRemove }))
    if (deleteAttributes.fulfilled.match(result)) {
      toast.success('Attribute deleted succefully')
      await dispatch(fetchAttributes())
    }
  };
  const handleAttributeCancel = () => {
    if (attributeNewItem) {
      setAttributeData((prev) => prev.filter((r) => r.id !== attributeNewItem.id));
      setAttributeNewItem(null);
    }
    setAttributeEditRow(null);
    setAttributeEditFormData({});
    setExpandedRow(null)
  };


  const handleAddFromAttribute = async () => {
    // Get the product ID from props
    const currentProductId = productId;

    // Check if we have a valid product ID
    if (!currentProductId) {
      toast.error("Product ID is missing. Cannot create variants from attributes.");
      return;
    }

    const body = variantAttribute.map(m => m.values.split(';').filter(v => v !== ''))
    const result = await dispatch(addFromAttributes({ productId: currentProductId, body }))
    if (addFromAttributes.fulfilled.match(result)) {
      // If there are unsaved variants, alert the user for each one
      if (result.payload.unsavedVariants && result.payload.unsavedVariants.length > 0) {
        result.payload.unsavedVariants.forEach((variant) => {
          toast.warning(`Variant already exists: Name - ${variant.name}, SKU - ${variant.sku}`);
        });
      }
      // Fetch the updated variants before changing the UI state
      await dispatch(fetchVaraints(currentProductId));

      // Reset the variant attribute state to prevent empty array issue
      const mappedAttributes = attributes.map((v) => ({
        key: v.id,
        id: v.id,
        name: v.name,
        values: v.value,
        value: '',
      }));
      setVariantAttribute(mappedAttributes);

      // Now change the UI state
      setCreateFromAttribute(false)
    } else {
      toast.error('Failed to create variants from attributes')
    }
  }


  const handleAttributeSave = async () => {
    const row = attributeData.find((r) => r.id === attributeEditRow);

    if (!row) return;

    // Prepare body with semicolons at start and end
    const body = {
      name: row.name,
      value: ';' + values.join(';') + ';',
    };


    if (!row.new) {
      // existing => update

      const result = await dispatch(
        updateAttributes({ id: row.id, body })
      );
      if (updateAttributes.fulfilled.match(result)) {
        toast.success('Attribute updated succefully')
        await dispatch(fetchAttributes())
      } else if (updateAttributes.rejected.match(result)) {
        handleAttributeCancel();
      }
    } else {


      const result = await dispatch(
        createAttributes({ id: row.id, body })
      );
      if (createAttributes.fulfilled.match(result)) {
        toast.success('Attribute created succefully')
        await dispatch(fetchAttributes())
      } else if (createAttributes.rejected.match(result)) {
        handleAttributeCancel();
        setAttributeData((prev) => prev.filter((r) => r.id !== attributeNewItem.id));
      }

    }

    setAttributeEditRow(null);
    setAttributeEditFormData({});
    setAttributeNewItem(null);
    setExpandedRow(null)
    setValues([])
  };
  const addAttribute = () => {

    setAttributeData((prev) => {
      const nextKey = Date.now();
      const newElement = {
        id: nextKey,
        key: nextKey, // Add key property that matches id
        name: "",
        value: "",
        values: '',
        new: true

      };
      setAttributeNewItem(newElement);
      setAttributeEditRow(nextKey);
      setExpandedRow(nextKey)
      setValues([])
      setAttributeEditFormData({ ...newElement });
      return [...prev, newElement];
    });

  };

  useEffect(() => {
    if (CreateFromAttribute) {
      dispatch(fetchAttributes())
    }

  }, [CreateFromAttribute])

  const renderAttributeCell = useCallback(
    (item, columnKey) => {
      const isEditing = attributeEditRow === item.id;
      const disableAll = Boolean(attributeEditRow && attributeEditRow !== item.id);
      switch (columnKey) {
        case "name":
          return isEditing ? (
            <UnderlinedInput

              value={item.name || ""}
              placeholder="Attribute Name"
              onChange={(e) => {
                item.name = e.target.value;
                setAttributeEditFormData({ ...item })
              }

              }
            />
          ) : (
            <span>{item.name}</span>
          );
        case "value":
          return isEditing ? (
            <UnderlinedInput
              autoFocus
              value={item.value}
              placeholder="Values"
              onChange={(e) => {
                item.value = e.target.value;
                if (e.target.value.endsWith(' ') && e.target.value.trim() !== "") {
                  const trimmedValue = e.target.value.trim();
                  if (!values.includes(trimmedValue)) {
                    setValues([...values, trimmedValue]);
                  }
                  item.value = '';
                }
                setAttributeEditFormData({ ...item })
              }}
            />

          ) : (
            <div className="flex flex-wrap items-center justify-center gap-2 py-2">
              <AnimatePresence mode="popLayout">
                {item.values && item.values.split(';').filter(v => v.trim() !== '').map((v) => (
                  <motion.div
                    key={v}
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    transition={{
                      type: "spring",
                      stiffness: 500,
                      damping: 30,
                      duration: 0.3
                    }}
                    className="p-1 bg-glb_blue rounded-lg text-white text-xs flex justify-center items-center"
                    layout
                  >
                    {v}
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>);
        case "actions":
          const isEditingAttribute = attributeEditRow === item.id;
          const disableAttributeActions = Boolean(attributeEditRow && isEditingAttribute);
          return (
            <div className="flex flex-row gap-2 justify-center">
              {/* EDIT BUTTON */}
              <Button
                isIconOnly
                size="sm"
                className={
                  disableAttributeActions
                    ? "rounded-full bg-transparent border border-black dark:border-white "
                    : "rounded-full bg-glb_blue text-white"
                }
                onClick={() => handleAttributeEditClick(item)}
                disabled={disableAttributeActions}
              >
                <PencilEdit01Icon
                  size={18}
                  className={disableAttributeActions ? "text-black dark:text-white" : "text-white"}
                />
              </Button>

              {/* DELETE BUTTON */}
              <Popover
                showArrow
                backdrop="opaque"
                classNames={{

                }}
                placement="right"
              >
                <PopoverTrigger>
                  <Button
                    isIconOnly
                    size="sm"
                    className={
                      disableAttributeActions
                        ? "rounded-full bg-transparent border border-black dark:border-white "
                        : "rounded-full bg-glb_red text-white"
                    }

                    disabled={disableAttributeActions}
                  >
                    <Delete02Icon
                      size={18}
                      className={disableAttributeActions ? "text-black dark:text-white" : "text-white"}
                    />
                  </Button>
                </PopoverTrigger>
                <PopoverContent>
                  {(titleProps) => (
                    <div className="px-1 py-2 ">
                      <h3 className="text-small font-bold mb-2 text-glb_red" >
                        Attribute Deletion
                      </h3>
                      <hr className="my-1 border-black/30 dark:border-white/30" />
                      <h6 className="text-small font-normal my-2 w-[90%] mx-auto " >
                        Are you sure you want to delete the attribute "{item.name}"?
                      </h6>
                      <hr className="my-1 border-black/30 dark:border-white/30" />
                      <div className="flex justify-between items-center gap-2 mt-2">
                        <span size="sm" className="text-gray-500">
                          ESC
                        </span>
                        <Button isLoading={variantLoading} size="sm" color="danger" onClick={() => removeAttributeElement(item.id)}>
                          Delete
                        </Button>
                      </div>
                    </div>
                  )}
                </PopoverContent>
              </Popover>

            </div>
          );
          return (
            <div className="flex flex-row gap-2 justify-center">
              <Button
                isIconOnly
                size="sm"
                className="rounded-full text-white bg-glb_blue"
                onClick={() => handleVariantEditClick(item)}
                disabled={disableAll}
              >
                <PencilEdit01Icon size={18} />
              </Button>
              <Button
                isIconOnly
                size="sm"
                className="rounded-full text-white bg-glb_red"
                onClick={() => removeVariantElement(item.key)}
                disabled={disableAll}
              >
                <Delete02Icon size={18} />
              </Button>
            </div>
          );
        default:
          return <span>{item[columnKey]}</span>;
      }
    }, [attributeEditFormData, attributeEditRow]);

  const renderAddVariationCell = useCallback(

    (item, columnKey) => {
      switch (columnKey) {
        case "values":


          return (
            <div className="flex flex-wrap items-center justify-center gap-2 py-2">
              <AnimatePresence mode="popLayout">
                {item.values && item.values.split(';').filter(v => v.trim() !== '').map((v, idx) => (
                  <motion.div
                    key={v}
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    transition={{
                      type: "spring",
                      stiffness: 500,
                      damping: 30,
                      duration: 0.3
                    }}
                    className="p-1 bg-glb_blue rounded-lg text-white text-xs flex justify-center items-center"
                    layout
                  >
                    {v}
                    <Cancel01Icon
                      size={18}
                      className="text-white ml-1 cursor-pointer"
                      onClick={() => {
                        // Make sure variantAttribute is an array before using find
                        if (Array.isArray(variantAttribute)) {
                          const foundItem = variantAttribute.find(attr => attr.id === item.id);
                          if (foundItem) {
                            // Update the values by removing the clicked value properly
                            // Split the values, filter out the one to remove, then join back
                            const valueArray = foundItem.values.split(';');
                            const filteredValues = valueArray.filter(val => val !== v);

                            // If all values are removed, delete the entire item
                            if (filteredValues.length === 0 || (filteredValues.length === 2 && filteredValues[0] === '' && filteredValues[1] === '')) {
                              // Remove the item from variantAttribute
                              const updatedVariantAttribute = variantAttribute.filter(attr => attr.id !== item.id);
                              setVariantAttribute(updatedVariantAttribute);
                            } else {
                              // Otherwise just update the values
                              foundItem.values = filteredValues.join(';');
                              // Update state with the new array
                              setVariantAttribute([...variantAttribute]);
                            }
                          }
                        }
                      }}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>);

        case "actions":

          return (
            <div className="flex flex-row gap-2 justify-center">
              <Button
                isIconOnly
                size="sm"
                className={
                  "rounded-full bg-glb_red text-white"
                }

              >
                <Delete02Icon
                  onClick={() => {
                    const updatedVariantAttribute = variantAttribute.filter(attr => attr.id !== item.id);
                    setVariantAttribute(updatedVariantAttribute);
                  }}
                  size={18}
                  className={"text-white"}
                />
              </Button>
            </div>
          );

        default:
          return <span>{item[columnKey]}</span>;
      }
    }, [showAttributeManagement, CreateFromAttribute, variantAttribute]);




  return (
    <div className="flex flex-col w-full gap-3">
      {/* Top "Cancel / Save" or "Add Variation" */}
      {editRowId ? (
        <div className="flex flex-row gap-2 justify-end">
          <Button
            variant="solid"
            className="rounded-full text-xs p-2 md:text-base md:p-4 bg-[#00000020] dark:bg-[#ffffff20]"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            variant="solid"
            className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_red text-white"
          >
            <Bookmark03Icon size={18} className="text-white" />
            Save
          </Button>
        </div>
      ) : (
        <div className="flex flex-row gap-2 justify-end">
          {CreateFromAttribute ? !showAttributeManagement ?
            <div className="flex justify-between w-full flex-wrap items-center gap-2">
              <Button
                variant="solid"
                className="rounded-full text-xs p-2 md:text-base md:p-4 bg-transparent text-glb_blue"
                onClick={() => setShowAttributeManagement(true)}
              >
                <PlusSignIcon />
                Add Attributes
              </Button>
              <div className="flex flex-row gap-2 justify-end">
                <Button
                  variant="solid"
                  className="rounded-full text-xs p-2 md:text-base md:p-4 bg-[#00000020] dark:bg-[#ffffff20]"
                  onClick={() => {
                    // Reset the variant attribute state to prevent empty array issue
                    const mappedAttributes = attributes.map((v) => ({
                      key: v.id,
                      id: v.id,
                      name: v.name,
                      values: v.value,
                      value: '',
                    }));
                    setVariantAttribute(mappedAttributes);
                    setCreateFromAttribute(false);
                  }}
                >
                  Cancel
                </Button>

                <Popover
                  showArrow
                  backdrop="opaque"
                  placement="right"
                >
                  <PopoverTrigger>
                    <Button

                      variant="solid"
                      className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_red text-white"
                    >
                      <Bookmark03Icon size={18} className="text-white" />
                      Generate
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent>
                    {(titleProps) => (
                      <div className="px-1 py-2 ">
                        <h3 className="text-small font-bold mb-2 text-glb_red" >
                          Generate Variants
                        </h3>
                        <hr className="my-1 border-black/30 dark:border-white/30" />
                        <h6 className="text-small font-normal my-2 w-[90%] mx-auto " >
                          Are you sure you want to generate variants from the selected attributes?
                        </h6>
                        <hr className="my-1 border-black/30 dark:border-white/30" />
                        <div className="flex justify-between items-center gap-2 mt-2">
                          <span size="sm" className="text-gray-500">
                            ESC
                          </span>
                          <Button onClick={() => {
                            handleAddFromAttribute()
                          }}
                            className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_blue text-white"

                            isDisabled={variantLoading || variantAttribute.length === 0}
                            isLoading={variantLoading}>
                            Generate
                          </Button>
                        </div>
                      </div>
                    )}
                  </PopoverContent>
                </Popover>
              </div>
            </div> : attributeEditRow ?
              <div className="flex flex-row gap-2 justify-end">
                <Button
                  variant="solid"
                  className="rounded-full text-xs p-2 md:text-base md:p-4 bg-[#00000020] dark:bg-[#ffffff20]"
                  onClick={() => handleAttributeCancel()}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => handleAttributeSave()}
                  variant="solid"
                  className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_red text-white"
                >
                  <Bookmark03Icon size={18} className="text-white" />
                  Save
                </Button>
              </div> :
              <div className="flex justify-between w-full flex-wrap items-center gap-2">
                <Button
                  variant="solid"
                  className="rounded-full text-xs p-2 md:text-base md:p-4 bg-transparent text-glb_blue"
                  onClick={() => setShowAttributeManagement(false)}
                >
                  <ArrowLeft01Icon />
                  Return
                </Button>
                <Button
                  variant="solid"
                  className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_blue text-white"
                  onClick={() => addAttribute()}
                >
                  <PlusSignIcon />
                  Add Attribute
                </Button> </div> : <> <Button
                  onClick={addVariation}
                  variant="solid"
                  className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_blue text-white"
                >
                  <PlusSignIcon />
                  Custom Variant
                </Button>

            <Button
              variant="solid"
              className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_red text-white"
              onClick={() => setCreateFromAttribute(true)}
            >
              <PlusSignIcon />
              Attribute Management
            </Button></>}
        </div>
      )}

      {/* Table */}
      <div className="w-full mt-6">
        <div className="relative w-full min-h-[300px]"> {/* Make sure the parent is relative and has min-height */}
          <AnimatePresence initial={false}>
            {showAttributeManagement ? (
              <motion.div
                key="attributeTable"
                initial={{ x: "100%", opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: "100%", opacity: 0 }}
                transition={{ type: "spring" }}
                className="absolute top-0 left-0 w-full"
              >
                <CustomTable
                  rowDetails={rowDetails}
                  expandedRow={expandedRow}
                  maxHeight={false}
                  columns={addFromAttributescolumns}
                  data={attributeData ? attributeData.sort((a, b) => b.id - a.id) : []}
                  renderCell={renderAttributeCell}
                  className="dark:bg-gray-800 dark:text-white"
                  loading={variantLoading}
                />
              </motion.div>
            ) : (
              <motion.div
                key="regularTable"
                initial={{ x: "-100%", opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: "-100%", opacity: 0 }}
                transition={{ type: "spring" }}
                className="absolute top-0 left-0 w-full"
              >
                <CustomTable
                  maxHeight={false}
                  columns={CreateFromAttribute ? showAttributeManagement ? addFromAttributescolumns : variantAttributescolumns : columns}
                  data={CreateFromAttribute ? showAttributeManagement ? attributeData.sort((a, b) => b.id - a.id) : variantAttribute.sort((a, b) => b.id - a.id) : data.sort((a, b) => b.id - a.id)}
                  renderCell={CreateFromAttribute ? showAttributeManagement ? renderAttributeCell : renderAddVariationCell : renderCell}
                  className="dark:bg-gray-800 dark:text-white"
                  loading={variantLoading}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* {CreateFromAttribute && <div className="flex flex-row gap-2 justify-end mt-4">
          {!showAttributeManagement ? <Button
            variant="solid"
            className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_blue text-white"
            onClick={() => setShowAttributeManagement(true)}
          >
            <PlusSignIcon />
            Attributes Management
          </Button>
            :
            attributeEditRow ?
              <div className="flex flex-row gap-2 justify-end">
                <Button
                  variant="solid"
                  className="rounded-full text-xs p-2 md:text-base md:p-4 bg-[#00000020] dark:bg-[#ffffff20]"
                  onClick={() => handleAttributeCancel()}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => handleAttributeSave()}
                  variant="solid"
                  className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_red text-white"
                >
                  <Bookmark03Icon size={18} className="text-white" />
                  Save
                </Button>
              </div> :
              <Button
                variant="solid"
                className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_blue text-white"
                onClick={() => addAttribute()}
              >
                <PlusSignIcon />
                Add Attribute
              </Button>
          } </div>
        }
        {
          showAttributeManagement &&
          <AnimatePresence>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
              className="flex flex-wrap items-center gap-3 py-2"
            >
              <CustomTable
                rowDetails={rowDetails}
                expandedRow={expandedRow}
                maxHeight={false}
                columns={addFromAttributescolumns}
                data={attributeData ? attributeData.sort((a, b) => b.id - a.id) : []}
                renderCell={renderAttributeCell}
                className="dark:bg-gray-800 dark:text-white"
                loading={variantLoading}
              />
            </motion.div>
          </AnimatePresence>
        } */}
      </div>
    </div>
  );
};

// -------------------------------------------------------------------
// 3) "SalesPrices" Tab
// (Back to your original design: new row or edit row =>
//  open sub-row with multiple currency fields, etc.)
// -------------------------------------------------------------------
const SalesPrices = ({
  data,
  setSalesData,         // needed so we can update the local array
  editRowId,
  editFormData,
  setEditFormData,
  handleCancel,
  handleSave,
  addSalesPrice,        // function for new item
  renderCell,
  expandedRow,
  toggleExpandedRow,
}) => {

  const dispatch = useDispatch()
  const { currencies, loading, error } = useSelector((state) => state.general);

  useEffect(() => {
    dispatch(getCurrencies())
  }, [dispatch])


  const columns = [
    { key: "offerName", label: "Offer Name" },
    { key: "quantityPaid", label: "Quantity Paid" },
    { key: "quantityFree", label: "Quantity Free" },
    { key: "prices", label: "Price" },
    { key: "actions", label: "Actions", w: "w-[10%]" },
  ];




  // sub-row for extra prices
  const rowDetails = (item) => {

    if (expandedRow !== item.key) return null;
    const isEditing = editRowId === item.key;
    // If in editing mode => show item._editPriceArray; otherwise item.price
    const priceArray = isEditing ? item._editPriceArray || [] : item.price || [];

    return (
      <AnimatePresence>
        <tr key={"expand-" + item.key}>
          <td colSpan={5}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
              className="flex flex-wrap items-center gap-3 py-2"
            >
              {priceArray.map((p, idx) => {


                if (!p.currency) p.currency = "MAD";
                if (isEditing) {
                  return (
                    <div
                      key={idx}
                      className="flex items-center gap-2 bg-[#00000010] dark:bg-[#ffffff10] rounded-full px-4 py-2"
                    >
                      {/* Price input */}
                      <div className="flex items-center gap-1">
                        <span className="text-[#00000030] dark:text-[#ffffff30] text-xs">
                          Price
                        </span>
                        <input
                          type="text"
                          autoFocus
                          className="!w-20 px-2 py-1 bg-transparent rounded outline-none focus:ring-0 text-sm"
                          value={p.price || ""}

                          onChange={(e) => {
                            // Regular expression to allow only digits, commas, and periods
                            const regex = /^[0-9.,]*$/;

                            // If the input matches the regex, update the value
                            if (regex.test(e.target.value)) {
                              p.price = e.target.value;
                              setSalesData((prev) => [...prev]);
                            }

                          }}
                        />
                      </div>
                      {/* Currency select */}

                      <Select
                        labelPlacement="outside-left"
                        label='Currency'
                        size='sm'
                        selectedKeys={new Set([p.currency])}
                        variant="flat"
                        classNames={{
                          base: 'relative text-white flex items-center gap-1 px-2 py-1 text-xs flex items-center gap-1',
                          mainWrapper: 'min-w-24',
                          innerWrapper: "w-full",
                          label: 'text-[#00000030] dark:text-[#ffffff30]',
                          trigger: "rounded-full bg-[#21B53950] dark:bg-[#21B53920] appearance-none  border-none pr-6 dark:text-white text-black outline-none cursor-pointer"
                        }}
                        onSelectionChange={(val) => {

                          const selectedValue = Array.from(val)[0];
                          p.currency = selectedValue;
                          setSalesData((prev) => [...prev]);
                        }}
                      >
                        {currencies.map((cur) => (
                          <SelectItem key={cur.code}>{cur.code}</SelectItem>
                        ))}
                      </Select>


                      {/* Remove price item icon without border/background */}
                      <button
                        onClick={() => {
                          const updatedArray = item._editPriceArray.filter((_, i) => i !== idx);
                          item._editPriceArray = updatedArray;
                          setSalesData((prev) => [...prev]);
                        }}
                        className="bg-transparent border-0 p-0 cursor-pointer text-[#ff0000]"
                      >
                        <Delete02Icon size={16} />
                      </button>
                    </div>
                  );
                } else {
                  // Read-only display
                  return (
                    <div
                      key={idx}
                      className="flex items-center gap-3 bg-[#00000010] dark:bg-[#ffffff10] rounded-full px-4 py-2"
                    >
                      <div className="text-xs flex items-center gap-1">
                        <span className="text-[#00000070] dark:text-[#ffffff30]">Price</span>
                        <span className="dark:text-white">{p.price || "0"}</span>
                      </div>
                      <div className="text-xs flex items-center gap-1">
                        <span className="text-[#00000070] dark:text-[#ffffff30]">Currency</span>
                        <div className="rounded-full px-3 py-1 bg-[#21B53950] dark:bg-[#21B53920] dark:text-white flex items-center gap-1">
                          {p.currency}
                        </div>
                      </div>
                    </div>
                  );
                }
              })}



              {isEditing && (
                <Button
                  variant="bordered"
                  className="rounded-full text-sm"
                  onClick={() => {
                    // Add a new price item
                    item._editPriceArray.push({ price: "", currency: "€" });
                    setSalesData((prev) => [...prev]);
                    // Make sure row is expanded (already is if isEditing)
                  }}
                >
                  <PlusSignIcon size={18} />
                  Add Price
                </Button>
              )}
            </motion.div>
          </td>
        </tr>
      </AnimatePresence>
    );
  };

  return (
    <div className="flex flex-col w-full gap-3">
      {/* Buttons at top-right */}
      {editRowId ? (
        <div className="flex flex-row gap-2 justify-end">
          <Button
            variant="solid"
            className="rounded-full text-xs p-2 md:text-base md:p-4 bg-[#00000020] dark:bg-[#ffffff20]"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            variant="solid"
            className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_red text-white"
          >
            <Bookmark03Icon size={18} className="text-white" />
            Save
          </Button>
        </div>
      ) : (
        <div className="flex flex-row gap-2 justify-end">
          <Button
            onClick={addSalesPrice}
            variant="solid"
            className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_blue text-white"
          >
            <PlusSignIcon />
            New Sales Price
          </Button>
        </div>
      )}

      {/* The Table */}
      <div className="w-full mt-6">
        <CustomTable
          columns={columns}
          data={data.sort((a, b) => Number(b.key) - Number(a.key))}
          renderCell={renderCell}
          expandedRow={expandedRow}
          rowDetails={rowDetails}
          className="dark:bg-gray-800 dark:text-white"
        />
      </div>
    </div>
  );
};

// -------------------------------------------------------------------
// 4) "Upsell" Tab (same as your code, omitted for brevity)
// -------------------------------------------------------------------
const UpsellTab = ({
  data,
  setUpsellData,
  editRowId,
  setEditRowId,
  editFormData,
  setEditFormData,
  handleCancel,
  handleSave,
  addUpsellItem,
  renderCell,
  expandedRow,
  toggleExpandedRow,
}) => {
  const dispatch = useDispatch()
  const { currencies, loading, error } = useSelector((state) => state.general);

  useEffect(() => {
    dispatch(getCurrencies())
  }, [dispatch])

  const columns = [
    { key: "offerName", label: "Upsell Name" },
    { key: "quantityPaid", label: "Quantity Paid" },
    { key: "quantityFree", label: "Quantity Free" },
    { key: "price", label: "Price" },
    { key: "actions", label: "Actions", w: "w-[10%]" },
  ];

  // Sub-row for extra prices
  const rowDetails = (item) => {
    if (expandedRow !== item.key) return null;
    const isEditing = editRowId === item.key;
    const priceArray = isEditing ? item._editPriceArray || [] : item.price || [];

    return (
      <AnimatePresence>
        <tr key={"expand-upsell-" + item.key}>
          <td colSpan={5}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
              className="flex flex-wrap items-center gap-3 py-2"
            >
              {priceArray.map((p, idx) => {
                if (!p.currency) p.currency = "€";
                if (isEditing) {
                  // Editing mode
                  return (
                    <div
                      key={idx}
                      className="flex items-center gap-2 bg-[#00000010] dark:bg-[#ffffff10] rounded-full px-4 py-2"
                    >
                      {/* Price input */}
                      <div className="flex items-center gap-1">
                        <span className="text-[#00000030] dark:text-[#ffffff30] text-xs">
                          Price
                        </span>
                        <input
                          type="text"
                          className="!w-20 px-2 py-1 bg-transparent rounded outline-none focus:ring-0 text-sm"
                          value={p.amount || ""}
                          onChange={(e) => {
                            // Regular expression to allow only digits, commas, and periods
                            const regex = /^[0-9.,]*$/;

                            // If the input matches the regex, update the value
                            if (regex.test(e.target.value)) {
                              p.amount = e.target.value;
                              setUpsellData((prev) => [...prev]);
                            }
                          }}
                        />

                      </div>
                      {/* Currency select */}
                      <Select
                        labelPlacement="outside-left"
                        label='Currency'
                        size='sm'
                        selectedKeys={new Set([p.currency])}
                        variant="flat"
                        classNames={{
                          base: 'relative  text-white flex items-center gap-1 px-2 py-1 text-xs flex items-center gap-1',
                          mainWrapper: 'min-w-24',
                          innerWrapper: "w-full",
                          label: 'text-[#00000030] dark:text-[#ffffff30]',
                          trigger: "rounded-full bg-[#21B53950] dark:bg-[#21B53920] appearance-none  border-none pr-6 dark:text-white text-black outline-none cursor-pointer"
                        }}
                        onSelectionChange={(val) => {

                          const selectedValue = Array.from(val)[0];
                          p.currency = selectedValue;
                          setUpsellData((prev) => [...prev]);
                        }}
                      >
                        {currencies.map((cur) => (
                          <SelectItem key={cur.code}>{cur.code}</SelectItem>
                        ))}
                      </Select>
                      <button
                        onClick={() => {
                          const updatedArray = item._editPriceArray.filter((_, i) => i !== idx);
                          item._editPriceArray = updatedArray;
                          setUpsellData((prev) => [...prev]);
                        }}
                        className="bg-transparent border-0 p-0 cursor-pointer text-[#ff0000]"
                      >
                        <Delete02Icon size={16} />
                      </button>
                    </div>
                  );
                } else {
                  // Read-only
                  return (
                    <div
                      key={idx}
                      className="flex items-center gap-3 bg-[#00000010] dark:bg-[#ffffff10] rounded-full px-4 py-2"
                    >
                      <div className="text-xs flex items-center gap-1">
                        <span className="text-[#00000070] dark:text-[#ffffff30]">Price</span>
                        <span className="dark:text-white">{p.amount || "0"}</span>
                      </div>
                      <div className="text-xs flex items-center gap-1">
                        <span className="text-[#00000070] dark:text-[#ffffff30]">Currency</span>
                        <div className="rounded-full px-3 py-1 bg-[#21B53950] dark:bg-[#21B53920] dark:text-white flex items-center gap-1">
                          {p.currency}
                        </div>
                      </div>
                    </div>
                  );
                }
              })}

              {isEditing && (
                <Button
                  variant="bordered"
                  className="rounded-full text-sm"
                  onClick={() => {
                    item._editPriceArray.push({ amount: "", currency: "€" });
                    setUpsellData((prev) => [...prev]);
                  }}
                >
                  <PlusSignIcon size={18} />
                  Add Price
                </Button>
              )}
            </motion.div>
          </td>
        </tr>
      </AnimatePresence>
    );
  };

  return (
    <div className="flex flex-col w-full gap-3">
      {editRowId ? (
        <div className="flex flex-row gap-2 justify-end">
          <Button
            variant="solid"
            className="rounded-full text-xs p-2 md:text-base md:p-4 bg-[#00000020] dark:bg-[#ffffff20]"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            variant="solid"
            className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_red text-white"
          >
            <Bookmark03Icon size={18} className="text-white" />
            Save
          </Button>
        </div>
      ) : (
        <div className="flex flex-row gap-2 justify-end">
          <Button
            onClick={addUpsellItem}
            variant="solid"
            className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_blue text-white"
          >
            <PlusSignIcon />
            New Upsell
          </Button>
        </div>
      )}

      <div className="w-full mt-6">
        <CustomTable
          columns={columns}
          data={data.sort((a, b) => Number(b.key) - Number(a.key))}
          renderCell={renderCell}
          expandedRow={expandedRow}
          rowDetails={rowDetails}
          className="dark:bg-gray-800 dark:text-white"
        />
      </div>
    </div>
  );
};

// -------------------------------------------------------------------
// MAIN COMPONENT: EditProductModal
// -------------------------------------------------------------------
const EditProductModal = ({ isOpen, onClose, editedProduct, productsTypes, productsCategories }) => {
  const dispatch = useDispatch();
  // The product from parent. We rely on product.id to fetch sales price
  const [product, setProduct] = useState(editedProduct || {});
  const [productTypeForNav, setProductTypeForNav] = useState(null)
  useEffect(() => {
    if (editedProduct) {
      setProduct(editedProduct);
    }
  }, [editedProduct]);
  // Tabs
  const [currentTab, setCurrentTab] = useState("informations");
  const onModalClose = () => {
    setProductTypeForNav(null)
    handleSalesCancel()
    handleUpsellCancel()

    // Call handleVariantCancel without trying to access attributes
    // This will prevent the ReferenceError
    setVariantEditRowId(null);
    setVariantEditFormData({});
    if (variantNewItem) {
      setVariantData((prev) => prev.filter((r) => r.id !== variantNewItem.id));
      setVariantNewItem(null);
    }

    setProduct(null);
    setCurrentTab("informations");
    onClose();
  }






  // -----------------------------------------
  //  Variants (unchanged from your example)
  // -----------------------------------------
  const { variants, loading: variantLoading } = useSelector((state) => state.variants);

  const [variantData, setVariantData] = useState(() => generateRandomData(5));
  const [variantEditRowId, setVariantEditRowId] = useState(null);
  const [variantEditFormData, setVariantEditFormData] = useState({});
  const [variantNewItem, setVariantNewItem] = useState(null);
  const [loadingVariantRows, setLoadingVariantRows] = useState(null);
  useEffect(() => {
    if (product?.id) {
      dispatch(fetchVaraints(product.id));
    }
  }, [product, dispatch]);


  useEffect(() => {

    if (currentTab !== 'variants') {
      return
    }

    const mapped = variants.map((v) => ({
      key: v.id,
      id: v.id,
      name: v.name,
      sku: v.sku,

    }));

    setVariantData(mapped);

  }, [variants, currentTab]);

  const handleVariantEditClick = (row) => {
    setVariantEditRowId(row.id);
    setVariantEditFormData({ ...row });
  };

  const removeVariantElement = async (keyToRemove) => {

    try {
      // Set loading state for this specific row
      setLoadingVariantRows(keyToRemove);

      // Find the variant to delete
      const rowToDelete = variants.find((r) => r.id === keyToRemove);

      // If variant not found, show error and exit
      if (!rowToDelete) {
        toast.error("Variant not found.");
        return;
      }

      // Get product ID
      const productId = product?.id;

      // Validate product ID
      if (!productId) {
        toast.error("Product ID is missing. Cannot delete variant.");
        return;
      }

      // Call API to delete the variant
      const resultAction = await dispatch(
        deleteVariant({
          productId: productId,
          variantId: rowToDelete.id,
        })
      );

      // Handle success or failure
      if (deleteVariant.fulfilled.match(resultAction)) {
        // Optionally refresh the variants list if successful
        // await dispatch(fetchVaraints(productId));
      } else {
        toast.error(resultAction.payload?.message || "Failed to delete variant");
      }
    } catch (error) {
      console.error("Error deleting variant:", error);
      toast.error("An error occurred while deleting the variant");
    } finally {
      // Ensure loading state is reset regardless of success or failure
      setLoadingVariantRows(null);

    }
  };


  const handleVariantCancel = async () => {
    // Store the product ID in a local variable to ensure it's available
    const productId = product?.id;

    if (variantNewItem) {
      setVariantData((prev) => prev.filter((r) => r.id !== variantNewItem.id));
      setVariantNewItem(null);
    }

    // Reset all edit-related state
    setVariantEditRowId(null);
    setVariantEditFormData({});

    // If we have a valid product ID, refresh the data from the server
    if (productId) {
      // Fetch the latest variants data
      dispatch(fetchVaraints(productId));

      // We don't need to reset variantAttribute here since it's handled in the Variants component
      // This was causing the ReferenceError: attributes is not defined
    }
  };



  const handleVariantSave = async () => {
    const row = variantData.find((r) => r.id === variantEditRowId);
    if (!row) return;

    // Store the product ID in a local variable to ensure it's available
    const productId = product?.id;

    // Check if we have a valid product ID
    if (!productId) {
      toast.error("Product ID is missing. Cannot save variant.");
      return;
    }

    // Prepare body
    const body = {
      name: row.name,
      sku: row.sku,
    };

    if (row.key) {
      const result = await dispatch(
        updateVariant({ productId: productId, variantId: row.id, body })
      );
      if (updateVariant.rejected.match(result)) {
        toast.error(result.payload.message.message || "Failed to update variant");
        // handleVariantCancel();
      } else if (updateVariant.fulfilled.match(result)) {
        setVariantEditRowId(null);
        setVariantEditFormData({});
        setVariantNewItem(null);
      }
    } else {
      const result = await dispatch(
        createVaraints({ productId: productId, body })
      );
      if (createVaraints.rejected.match(result)) {

        toast.error(result.payload.message.message || "Failed to create variant");
        // handleVariantCancel();
      } else if (createVaraints.fulfilled.match(result)) {
        setVariantEditRowId(null);
        setVariantEditFormData({});
        setVariantNewItem(null);
      }
    }


  };
  const addVariation = () => {

    setVariantData((prev) => {
      const nextKey = Date.now();
      const newElement = {
        id: nextKey,
        name: "",
        sku: "",

      };
      setVariantNewItem(newElement);
      setVariantEditRowId(nextKey);
      setVariantEditFormData({ ...newElement });
      return [...prev, newElement];
    });
  };


  const renderVariantCell = useCallback(
    (item, columnKey) => {
      const isEditing = variantEditRowId === item.id;
      const disableAll = Boolean(variantEditRowId && variantEditRowId !== item.id);

      switch (columnKey) {
        case "name":
          return isEditing ? (
            <UnderlinedInput
              autoFocus
              value={item.name || ""}
              placeholder="Variant Name"
              onChange={(e) => {
                item.name = e.target.value;
                setVariantEditFormData({ ...item })
              }

              }
            />
          ) : (
            <span>{item.name}</span>
          );
        case "sku":
          return isEditing ? (
            <UnderlinedInput
              value={item.sku || ""}
              placeholder="Variant SKU"
              onChange={(e) => {
                item.sku = e.target.value;
                setVariantEditFormData({ ...item })
              }}
            />
          ) : (
            <span>{item.sku}</span>
          );
        // case "type":
        //   return isEditing ? (

        //     <Select
        //       variant="underlined"
        //       color="primary"
        //       placeholder="Type"
        //       className="w-full"
        //       selectedKeys={GetTypeSetByName(variantEditFormData.type || "")}
        //       onSelectionChange={(value) =>
        //         setVariantEditFormData((prev) => ({
        //           ...prev,
        //           type: GetTypeFromSet(value),
        //         }))
        //       }
        //       classNames={{
        //         value: "text-center",
        //         label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],
        //       }}
        //     >
        //       {ProductType.map((type) => (
        //         <SelectItem key={type.id.toString()}>{type.name}</SelectItem>
        //       ))}
        //     </Select>
        //   ) : (
        //     <span>{item.type}</span>
        //   );
        // case "category":
        //   return isEditing ? (
        //     <Select
        //       variant="underlined"
        //       color="primary"
        //       placeholder="Category"
        //       className="w-full"
        //       selectedKeys={GetCategorySetByName(variantEditFormData.category || "")}
        //       onSelectionChange={(value) =>
        //         setVariantEditFormData((prev) => ({
        //           ...prev,
        //           category: GetCategoryFromSet(value),
        //         }))
        //       }
        //       classNames={{
        //         value: "text-center",
        //         label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],
        //       }}
        //     >
        //       {ProductCategory.map((cat) => (
        //         <SelectItem key={cat.id.toString()}>{cat.name}</SelectItem>
        //       ))}
        //     </Select>
        //   ) : (
        //     <span>{item.category}</span>
        //   );
        case "actions":
          const isEditingVariant = variantEditRowId === item.id;
          const disableVariantActions = Boolean(variantEditRowId && isEditingVariant);

          return (
            <div className="flex flex-row gap-2 justify-center">
              {/* EDIT BUTTON */}
              <Button
                isIconOnly
                size="sm"
                className={
                  disableVariantActions
                    ? "rounded-full bg-transparent border border-black dark:border-white "
                    : "rounded-full bg-glb_blue text-white"
                }
                onClick={() => handleVariantEditClick(item)}
                disabled={disableVariantActions}
              >
                <PencilEdit01Icon
                  size={18}
                  className={disableVariantActions ? "text-black dark:text-white" : "text-white"}
                />
              </Button>

              {/* DELETE BUTTON */}
              <Button
                isIconOnly
                isLoading={loadingVariantRows && loadingVariantRows === item.id}
                size="sm"
                className={
                  disableVariantActions
                    ? "rounded-full bg-transparent border border-black dark:border-white "
                    : "rounded-full bg-glb_red text-white"
                }
                onClick={() => removeVariantElement(item.id)}
                disabled={disableVariantActions}
              >
                <Delete02Icon
                  size={18}
                  className={disableVariantActions ? "text-black dark:text-white" : "text-white"}
                />
              </Button>
            </div>
          );
          return (
            <div className="flex flex-row gap-2 justify-center">
              <Button
                isIconOnly
                size="sm"
                className="rounded-full text-white bg-glb_blue"
                onClick={() => handleVariantEditClick(item)}
                disabled={disableAll}
              >
                <PencilEdit01Icon size={18} />
              </Button>
              <Button
                isIconOnly
                size="sm"
                className="rounded-full text-white bg-glb_red"
                onClick={() => removeVariantElement(item.key)}
                disabled={disableAll}
              >
                <Delete02Icon size={18} />
              </Button>
            </div>
          );
        default:
          return <span>{item[columnKey]}</span>;
      }
    },
    [variantEditRowId, variantEditFormData, variants, loadingVariantRows]
  );

  // -----------------------------------------
  // SALES PRICE (Redux + Local State synergy)
  // -----------------------------------------
  const { salesPrices, loading: salesLoading } = useSelector((state) => state.salesPrice);

  // We keep local "salesData" so we can do the same row-expanding design
  const [salesData, setSalesData] = useState([]);
  const [salesExpandedRow, setSalesExpandedRow] = useState(null);
  const [salesEditRowId, setSalesEditRowId] = useState(null);
  const [salesEditFormData, setSalesEditFormData] = useState({});
  const [salesNewItem, setSalesNewItem] = useState(null);
  const [loadingSales, setLoadingSales] = useState({})

  // 1) On product change => fetch from the server
  useEffect(() => {
    if (product?.id) {
      dispatch(fetchSalesPrices(product.id));
    }
  }, [product, dispatch]);

  // 2) Whenever Redux `salesPrices` changes, copy them to local `salesData`
  useEffect(() => {
    const mapped = salesPrices.map((sp) => ({
      key: sp.id.toString(),
      id: sp.id,
      offerName: sp.name,
      quantityPaid: sp.paidQuantity,
      quantityFree: sp.freeQuantity,
      // rename 'prices' to 'price' to match your existing columns:
      price: (sp.prices || []).map((p) => ({
        price: p.price,
        currency: p.currency
      })),
    }));
    setSalesData(mapped);
  }, [salesPrices]);

  // Expand/unexpand sub-row
  const toggleSalesExpandedRow = (key) => {
    setSalesExpandedRow((prev) => (prev === key ? null : key));
  };

  // "Edit" => clone current row price array to _editPriceArray
  const handleSalesEditClick = (row) => {
    row._editPriceArray = (row.price || []).map((p) => ({ ...p }));
    setSalesEditRowId(row.key);
    setSalesEditFormData({ ...row });
    // Ensure the row is expanded
    setSalesExpandedRow(row.key);
  };

  // "Delete" => call thunk, remove from store
  // 1) mark function as async
  // "Delete" => always call thunk
  const removeSalesElement = async (keyToRemove) => {
    setLoadingSales((prev) => ({ ...prev, [keyToRemove]: true }));
    const rowToDelete = salesData.find((r) => r.key === keyToRemove);
    if (!rowToDelete) return;

    // Always call deleteSalesPriceThunk, even if rowToDelete.id is undefined
    const resultAction = await dispatch(
      deleteSalesPriceThunk({
        productId: product.id,
        salesPriceId: rowToDelete.id,
      })
    );

    // If delete succeeded, re-fetch updated prices
    if (deleteSalesPriceThunk.fulfilled.match(resultAction)) {
      await dispatch(fetchSalesPrices(product.id));
    }

    setLoadingSales((prev) => ({ ...prev, [keyToRemove]: false }));
  };



  // "Cancel" => if we created a brand-new row, remove it
  const handleSalesCancel = () => {
    if (salesNewItem) {
      // user was adding a new row but clicked "Cancel"
      setSalesData((prev) => prev.filter((r) => r.key !== salesNewItem.key));
      setSalesNewItem(null);
    }
    setSalesEditRowId(null);
    setSalesEditFormData({});
  };

  // "Save" => if row.id is defined => update API, else => create API
  const handleSalesSave = async () => {
    const row = salesData.find((r) => r.key === salesEditRowId);
    if (!row) return;

    setLoadingSales((prev) => ({ ...prev, [row.key]: true }));
    // Prepare body
    const body = {
      name: row.offerName,
      freeQuantity: Number(row.quantityFree),
      paidQuantity: Number(row.quantityPaid),
      prices: row._editPriceArray.map((p) => ({
        currency: p.currency,
        price: Number(p.price),
      })),
    };

    if (row.id) {
      // existing => update
      const result = await dispatch(
        updateSalesPriceThunk({ productId: product.id, salesPriceId: row.id, body })
      );
      if (updateSalesPriceThunk.fulfilled.match(result)) {
        // Optionally handle success, show toast, etc.
      } else {
        // handle error
      }
    } else {
      // ------------------------------
      // NEW => CREATE
      // ------------------------------
      const result = await dispatch(
        createSalesPrice({ productId: product.id, body })
      );
      // if (createSalesPrice.fulfilled.match(result)) {
      //   // The response might have { result: { salesPrices: [...] } }
      //   const updatedProduct = result.payload?.result;
      //   if (updatedProduct?.salesPrices?.length) {
      //     // Find the newly created one in that array
      //     //   (if your backend only returns the single newly created item,
      //     //    you don't need a "find" – you can just take updatedProduct.salesPrices[0].id)
      //     const justCreated = updatedProduct.salesPrices.find(
      //       (sp) =>
      //         sp.name === row.offerName &&
      //         sp.paidQuantity === row.quantityPaid &&
      //         sp.freeQuantity === row.quantityFree &&
      //         sp.prices?.[0]?.price === body.prices?.[0]?.price
      //     );
      //     if (justCreated) {
      //       // Merge its "id" back into your local row
      //       // so future deletes/edits can call the server with the correct ID
      //       setSalesData((prev) =>
      //         prev.map((item) =>
      //           item.key === row.key
      //             ? { ...item, id: justCreated.id }
      //             : item
      //         )
      //       );
      //     }
      //   }
      // } else {
      //   // handle error
      // }
    }
    setLoadingSales((prev) => ({ ...prev, [row.key]: false }));
    // Cleanup local edit states
    setSalesNewItem(null);
    setSalesEditRowId(null);
    setSalesEditFormData({});
    setSalesExpandedRow(null);
  };


  // "New Sales Price" => create a brand new local row and open it for editing
  const addSalesPrice = () => {
    setSalesData((prev) => {
      const nextKey = Date.now().toString(); // Generate a unique key
      const newElement = {
        key: nextKey,
        // no "id", so we know it's a brand-new item
        offerName: "",
        quantityPaid: 0,
        quantityFree: 0,
        price: [{ price: "", currency: "€" }],
      };
      // put a copy in _editPriceArray for editing
      newElement._editPriceArray = [...newElement.price];
      setSalesNewItem(newElement);
      setSalesEditRowId(newElement.key);
      setSalesEditFormData({ ...newElement });
      setSalesExpandedRow(newElement.key);

      // Prepend the new row so it appears at the top
      return [newElement, ...prev];
    });
  };



  // Renders each cell in SalesPrices
  const renderSalesCell = useCallback(
    (item, columnKey) => {
      const disableAll = false;
      const isEditing = salesEditRowId === item.key;

      switch (columnKey) {
        case "offerName":
          return isEditing ? (
            <UnderlinedInput
              autoFocus
              value={item.offerName || ""}
              placeholder="Offer Name"
              onChange={(e) => {
                item.offerName = e.target.value;
                setSalesEditFormData({ ...item });
              }}
            />
          ) : (
            <span>{item.offerName}</span>
          );

        case "quantityPaid":
          return isEditing ? (
            <UnderlinedInput
              value={item.quantityPaid}
              placeholder="Quantity Paid"
              onChange={(e) => {
                // Regular expression to allow only digits, commas, and periods
                const regex = /^[0-9.,]*$/;

                // If the input matches the regex, update the value
                if (regex.test(e.target.value)) {
                  p.quantityPaid = e.target.value;
                  setSalesEditFormData({ ...item });
                }
              }}
            />
          ) : (
            <span>{item.quantityPaid}</span>
          );

        case "quantityFree":
          return isEditing ? (
            <UnderlinedInput
              value={item.quantityFree}
              placeholder="Quantity Free"

              onChange={(e) => {
                // Regular expression to allow only digits, commas, and periods
                const regex = /^[0-9.,]*$/;

                // If the input matches the regex, update the value
                if (regex.test(e.target.value)) {
                  item.quantityFree = e.target.value;
                  setSalesEditFormData({ ...item });
                }
              }}
            />
          ) : (
            <span>{item.quantityFree}</span>
          );

        case "prices":
          // Show first price only
          const firstPrice = item.price?.[0];
          if (!firstPrice) {
            return isEditing ? <UnderlinedInput value="" /> : <span>--</span>;
          }
          return isEditing ? (
            <UnderlinedInput
              value={firstPrice.price || ""}
              placeholder="Price"

              onChange={(e) => {
                const regex = /^[0-9.,]*$/;

                // If the input matches the regex, update the value
                if (regex.test(e.target.value)) {

                  if (item._editPriceArray && item._editPriceArray[0]) {
                    // Regular expression to allow only digits, commas, and periods
                    item._editPriceArray[0].price = e.target.value;
                  }
                  setSalesEditFormData({ ...item });
                }
              }}
            />
          ) : (
            <span>
              {firstPrice.price || "0"}{" "}
              <span className="dark:bg-[#ffffff10] bg-[#00000020] dark:text-white rounded-full px-2 py-1">
                {firstPrice.currency || "€"}
              </span>
            </span>
          );

        case "actions":
          const isEditingSales = salesEditRowId === item.key;
          const disableActionsSales = Boolean(salesEditRowId && isEditingSales);

          return (
            <div className="flex flex-row gap-2 justify-center">
              <Button
                isIconOnly
                size="sm"
                className={
                  disableActionsSales
                    ? "rounded-full bg-transparent border border-black dark:border-white"
                    : "rounded-full bg-glb_blue text-white"
                }
                onClick={() => handleSalesEditClick(item)}
                disabled={disableActionsSales}
              >
                {loadingSales[item.key] ? <Spinner size="sm" /> : <PencilEdit01Icon size={18} />}
              </Button>

              <Button
                isIconOnly
                size="sm"
                className={
                  disableActionsSales
                    ? "rounded-full bg-transparent border border-black dark:border-white"
                    : "rounded-full bg-glb_red text-white"
                }
                onClick={() => removeSalesElement(item.key)}
                disabled={disableActionsSales}
              >
                {loadingSales[item.key] ? <Spinner size="sm" /> : <Delete02Icon size={18} />}
              </Button>

              <Button
                isIconOnly
                size="sm"
                className="rounded-full dark:bg-[#ffffff10] dark:text-[#ffffff80]"
                onClick={() => toggleSalesExpandedRow(item.key)}
                disabled={disableActionsSales}
              >
                {salesExpandedRow === item.key ? <ArrowUp01Icon /> : <ArrowDown01Icon />}
              </Button>
            </div>
          );
          return (
            <div className="flex flex-row gap-2 justify-center">
              <Button
                isIconOnly
                size="sm"
                className="rounded-full text-white bg-glb_blue"
                onClick={() => handleSalesEditClick(item)}
                disabled={disableAll}
              >
                <PencilEdit01Icon size={18} />
              </Button>
              <Button
                isIconOnly
                size="sm"
                className="rounded-full text-white bg-glb_red"
                onClick={() => removeSalesElement(item.key)}
                disabled={disableAll}
              >
                <Delete02Icon size={18} />
              </Button>
              <Button
                isIconOnly
                size="sm"
                className="rounded-full dark:bg-[#ffffff10] dark:text-[#ffffff80]"
                onClick={() => toggleSalesExpandedRow(item.key)}
                disabled={disableAll}
              >
                {salesExpandedRow === item.key ? <ArrowUp01Icon /> : <ArrowDown01Icon />}
              </Button>
            </div>
          );

        default:
          return <span>{item[columnKey]}</span>;
      }
    },
    [salesEditRowId, salesExpandedRow]
  );

  // -----------------------------------------
  //  Upsell (unchanged or omitted)
  // -----------------------------------------
  // -----------------------------------------
  // UPSELL
  // -----------------------------------------
  const { upsells, loading: upsellLoading } = useSelector((state) => state.upsell);
  const [upsellData, setUpsellData] = useState([]);
  const [upsellExpandedRow, setUpsellExpandedRow] = useState(null);
  const [upsellNewItem, setUpsellNewItem] = useState(null);
  const [upsellEditFormData, setUpsellEditFormData] = useState({});

  // fetch upsells from server
  useEffect(() => {
    if (product?.id) {
      dispatch(fetchUpsells(product.id));
    }
  }, [product, dispatch]);

  // sync store => local state
  useEffect(() => {
    const mapped = upsells.map((u) => ({
      key: u.id.toString(),
      id: u.id,
      offerName: u.name,
      quantityPaid: u.paidQuantity,
      quantityFree: u.freeQuantity,
      price: (u.prices || []).map((p) => ({
        amount: p.price,
        currency: p.currency,
      })),
    }));
    setUpsellData(mapped);
  }, [upsells]);

  const [upsellEditRowId, setUpsellEditRowId] = useState(null);

  const toggleUpsellExpandedRow = (key) => {
    setUpsellExpandedRow((prev) => (prev === key ? null : key));
  };

  const handleUpsellEditClick = (row) => {
    row._editPriceArray = (row.price || []).map((p) => ({ ...p }));
    setUpsellEditRowId(row.key);
    setUpsellEditFormData({ ...row });
    setUpsellExpandedRow(row.key);
  };
  const removeUpsellElement = async (keyToRemove) => {
    const rowToDelete = upsellData.find((r) => r.key === keyToRemove);
    if (!rowToDelete) return;
    if (!rowToDelete.id) {
      setUpsellData((prev) => prev.filter((r) => r.key !== keyToRemove));
    } else {
      await dispatch(
        deleteUpsellThunk({
          productId: product.id,
          upsellId: rowToDelete.id,
        })
      );
    }
  };
  const handleUpsellCancel = () => {
    if (upsellNewItem) {
      setUpsellData((prev) => prev.filter((r) => r.key !== upsellNewItem.key));
      setUpsellNewItem(null);
    }
    setUpsellEditRowId(null);
    setUpsellEditFormData({});
  };
  const handleUpsellSave = async () => {
    const row = upsellData.find((r) => r.key === upsellEditRowId);
    if (!row) return;
    const body = {
      name: row.offerName,
      freeQuantity: Number(row.quantityFree),
      paidQuantity: Number(row.quantityPaid),
      prices: row._editPriceArray.map((p) => ({
        currency: p.currency,
        price: Number(p.amount),
      })),
    };
    if (row.id) {
      await dispatch(
        updateUpsellThunk({
          productId: product.id,
          upsellId: row.id,
          body,
        })
      );
    } else {
      await dispatch(
        createUpsell({
          productId: product.id,
          body,
        })
      );
    }
    setUpsellNewItem(null);
    setUpsellEditRowId(null);
    setUpsellEditFormData({});
    setUpsellExpandedRow(null);
  };
  const addUpsellItem = () => {
    setUpsellData((prev) => {
      const nextKey = Date.now().toString(); // Generate a unique key
      const newElement = {
        key: nextKey,
        offerName: "",
        quantityPaid: 0,
        quantityFree: 0,
        price: [{ amount: "", currency: "€" }],
      };
      newElement._editPriceArray = [...newElement.price];
      setUpsellNewItem(newElement);
      setUpsellEditRowId(newElement.key);
      setUpsellEditFormData({ ...newElement });
      setUpsellExpandedRow(newElement.key);
      return [newElement, ...prev];
    });
  };
  const renderUpsellCell = useCallback(
    (item, columnKey) => {
      const disableAll = Boolean(upsellEditRowId && upsellEditRowId !== item.key);
      const isEditing = upsellEditRowId === item.key;

      switch (columnKey) {
        case "offerName":
          return isEditing ? (
            <UnderlinedInput
              value={item.offerName || ""}
              placeholder="Upsell Name"
              autoFocus
              onChange={(e) => {
                item.offerName = e.target.value;
                setUpsellEditFormData({ ...item });
              }}
            />
          ) : (
            <span>{item.offerName}</span>
          );
        case "quantityPaid":
          return isEditing ? (
            <UnderlinedInput
              value={item.quantityPaid}
              placeholder="Quantity Paid"

              onChange={(e) => {
                const regex = /^[0-9.,]*$/;

                // If the input matches the regex, update the value
                if (regex.test(e.target.value)) {
                  item.quantityPaid = e.target.value;
                  setUpsellEditFormData({ ...item });
                }
              }}
            />
          ) : (
            <span>{item.quantityPaid}</span>
          );
        case "quantityFree":
          return isEditing ? (
            <UnderlinedInput
              value={item.quantityFree}
              placeholder="Quantity Free"

              onChange={(e) => {
                const regex = /^[0-9.,]*$/;

                // If the input matches the regex, update the value
                if (regex.test(e.target.value)) {
                  item.quantityFree = e.target.value;
                  setUpsellEditFormData({ ...item });
                }
              }}
            />
          ) : (
            <span>{item.quantityFree}</span>
          );
        case "price":
          const firstPrice = item.price?.[0];
          if (!firstPrice) return isEditing ? <UnderlinedInput value="" /> : <span>--</span>;
          return isEditing ? (
            <UnderlinedInput
              value={firstPrice.amount || ""}
              placeholder="Price"

              onChange={(e) => {
                const regex = /^[0-9.,]*$/;

                // If the input matches the regex, update the value
                if (regex.test(e.target.value)) {
                  if (item._editPriceArray && item._editPriceArray[0]) {
                    item._editPriceArray[0].amount = e.target.value;
                  }
                  setUpsellEditFormData({ ...item });
                }
              }}
            />
          ) : (
            <span>
              {firstPrice.amount || "0"}{" "}
              <span className="dark:bg-[#ffffff10] bg-[#00000020] dark:text-white rounded-full px-2 py-1">
                {firstPrice.currency || "€"}
              </span>
            </span>
          );
        case "actions":
          const isEditingUpsell = upsellEditRowId === item.key;
          const disableActionsUpsell = Boolean(upsellEditRowId && isEditingUpsell);

          return (
            <div className="flex flex-row gap-2 justify-center">
              <Button
                isIconOnly
                size="sm"
                className={
                  disableActionsUpsell
                    ? "rounded-full bg-transparent border border-black dark:border-white"
                    : "rounded-full bg-glb_blue text-white"
                }
                onClick={() => handleUpsellEditClick(item)}
                disabled={disableActionsUpsell}
              >
                <PencilEdit01Icon size={18} />
              </Button>

              <Button
                isIconOnly
                size="sm"
                className={
                  disableActionsUpsell
                    ? "rounded-full bg-transparent border border-black dark:border-white"
                    : "rounded-full bg-glb_red text-white"
                }
                onClick={() => removeUpsellElement(item.key)}
                disabled={disableActionsUpsell}
              >
                <Delete02Icon size={18} />
              </Button>

              <Button
                isIconOnly
                size="sm"
                className="rounded-full dark:bg-[#ffffff10] dark:text-[#ffffff80]"
                onClick={() => toggleUpsellExpandedRow(item.key)}
                disabled={disableActionsUpsell}
              >
                {upsellExpandedRow === item.key ? <ArrowUp01Icon /> : <ArrowDown01Icon />}
              </Button>
            </div>
          );
          return (
            <div className="flex flex-row gap-2 justify-center">
              <Button
                isIconOnly
                size="sm"
                className="rounded-full text-white bg-glb_blue"
                onClick={() => handleUpsellEditClick(item)}
                disabled={disableAll}
              >
                <PencilEdit01Icon size={18} />
              </Button>
              <Button
                isIconOnly
                size="sm"
                className="rounded-full text-white bg-glb_red"
                onClick={() => removeUpsellElement(item.key)}
                disabled={disableAll}
              >
                <Delete02Icon size={18} />
              </Button>
              <Button
                isIconOnly
                size="sm"
                className="rounded-full dark:bg-[#ffffff10] dark:text-[#ffffff80]"
                onClick={() => toggleUpsellExpandedRow(item.key)}
                disabled={disableAll}
              >
                {upsellExpandedRow === item.key ? <ArrowUp01Icon /> : <ArrowDown01Icon />}
              </Button>
            </div>
          );
        default:
          return <span>{item[columnKey]}</span>;
      }
    },
    [upsellEditRowId, upsellExpandedRow]
  );

  // -----------------------------------------
  //  Show tab content
  // -----------------------------------------
  const renderComponent = () => {
    switch (currentTab) {
      case "informations":
        return <Informations productId={product?.id} setProductTypeForNav={setProductTypeForNav} productsTypes={productsTypes} productCategories={productsCategories} onClose={onModalClose} />;

      case "variants":
        return (
          <Variants
            productId={product?.id}
            data={variantData}
            renderCell={renderVariantCell}
            editRowId={variantEditRowId}
            handleCancel={handleVariantCancel}
            handleSave={handleVariantSave}
            addVariation={addVariation}
            setEditFormData={setVariantEditFormData}
            editFormData={variantEditFormData}
          />
        );

      case "sales-price":
        return (
          <SalesPrices
            data={salesData}
            setSalesData={setSalesData}
            editRowId={salesEditRowId}
            editFormData={salesEditFormData}
            setEditFormData={setSalesEditFormData}
            handleCancel={handleSalesCancel}
            handleSave={handleSalesSave}
            addSalesPrice={addSalesPrice}
            renderCell={renderSalesCell}
            expandedRow={salesExpandedRow}
            toggleExpandedRow={toggleSalesExpandedRow}
          />
        );

      case "upsell":
        return upsellLoading ? (
          <div className="text-center">Loading Upsells...</div>
        ) : (
          <UpsellTab
            data={upsellData}
            setUpsellData={setUpsellData}
            editRowId={upsellEditRowId}
            setEditRowId={setUpsellEditRowId}
            editFormData={upsellEditFormData}
            setEditFormData={setUpsellEditFormData}
            handleCancel={handleUpsellCancel}
            handleSave={handleUpsellSave}
            addUpsellItem={addUpsellItem}
            renderCell={renderUpsellCell}
            expandedRow={upsellExpandedRow}
            toggleExpandedRow={toggleUpsellExpandedRow}
          />
        );


      default:
        return <div className="text-center">Select a tab</div>;
    }
  };

  // Lock tabs if something is in edit mode
  const disableTabs = variantEditRowId || salesEditRowId ? true : false;
  const [variant, setVariant] = useState(0)

  useEffect(() => {
    const getProductTypeValue = () => {
      if (productTypeForNav) {
        if (productTypeForNav?.productType === "variable" && product?.productType === "simple") {
          return 1;
        } else if (product?.productType === "simple") {
          return 0;
        } else if (productTypeForNav?.productType === "simple" && product?.productType === "variable") {
          return 1
        } else {
          return 2;
        }
      } else {
        if (product?.productType === "simple") {
          return 0;
        } else {
          return 2;
        }
      }

    };

    setVariant(getProductTypeValue());
  }, [productTypeForNav, product]);

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onModalClose}
      width="max-w-6xl"
      showHeader={true}
      title={`${product?.sku}`}
      headerClassName="px-0 lg:px-8 py-4 border-b"
      bodyClassName="p-0 lg:p-8"
      showFooter={false}
    >
      <div className="w-full">
        <Navigator currentTab={currentTab} variant={variant} setCurrentTab={setCurrentTab} isDisabled={disableTabs} />
        <div className="w-full my-10">
          {(
            renderComponent()
          )}
        </div>
      </div>
    </CustomModal>
  );
};

export default EditProductModal;
