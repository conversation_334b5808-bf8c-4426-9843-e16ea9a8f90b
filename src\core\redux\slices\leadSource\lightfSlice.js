import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { lightFunnelsInstallUrl, lightFunnelsStateSavingUrl } from "../URLs";
import axios from "axios";
import { getToken } from "../../../services/TokenHandler";


// Function to generate a random state (similar to Str::random(40) in PHP)
const generateRandomState = () => {
    return [...Array(40)]
        .map(() => Math.random().toString(36)[2]) // Generate random alphanumeric characters
        .join("");
};

// Async thunk for installing Light Funnels app
export const installLightfApp = createAsyncThunk(
    "lightf/install",
    async (_, { rejectWithValue }) => {


        try {
            const response = await axios.get(lightFunnelsInstallUrl,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );



            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error installing lightFunnels' });

            }
            return response.data.result;

        } catch (error) {
            console.log("error", error);
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }


    }
);


const lightfSlice = createSlice({
    name: "lightf",
    initialState: {
        loading: false,
        error: null,
        installUrl: null,
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(installLightfApp.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(installLightfApp.fulfilled, (state, action) => {
                state.loading = false;
                state.installUrl = action.payload;
            })
            .addCase(installLightfApp.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload; // Error message from Laravel backend
            });
    },
});

export default lightfSlice.reducer;
