import React, { useEffect, useState } from 'react'
import CustomModal from '../../components/CustomModal';
import UnderlinedInput from '../../../settings/components/UnderlinedInput';
import GeneralSelector from '../../components/GeneralSelector';
import axios from 'axios';
import { getToken } from '../../../../core/services/TokenHandler';
import moment from 'moment';
import { endOfMonth, endOfWeek, getLocalTimeZone, parseDate, startOfMonth, startOfWeek, today } from '@internationalized/date';
import { Button, ButtonGroup } from "@heroui/button";
import { I18nProvider, useLocale } from '@react-aria/i18n';
import { cn, DateRangePicker, RadioGroup, Radio } from "@heroui/react";
import { RouteNames } from '../../../../core/routes/routes';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { updateParams, resetParams } from '../../../../core/redux/slices/invoices/invoiceSlice';
import { Cancel01Icon, Delete02Icon, FilterIcon } from 'hugeicons-react';
import { invoicesStatusUrl } from '../../../../core/redux/slices/URLs';

const InvoiceFilterModal = ({ isOpen, onClose }) => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { params } = useSelector((state) => state.invoices);

    const [invoiceNum, setInvoiceNum] = useState('');
    const [dateRange, setDateRange] = useState({
        start: null,
        end: null,
    });
    const [loadingInvoiceStatus, setLoadingInvoiceStatus] = useState(false);
    const [statusList, setStatusList] = useState([]);
    const [isStatusListOpen, setIsStatusListOpen] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState(null);
    const [sortedBy, setSortedBy] = useState('');

    let { locale } = useLocale();

    // Format the date range
    const formatDate = (dateObj) => {
        if (!dateObj) return ""; // Return empty string if dateObj is null or undefined
        if (!dateObj.start || !dateObj.end) return `${moment().startOf('week').format('DD/MM/YYYY')} - ${moment().endOf('week').format('DD/MM/YYYY')}`;
        const formattedStart = dateObj?.start
            ? moment(dateObj.start.toDate()).format("DD/MM/YYYY")
            : "";
        const formattedEnd = dateObj?.end
            ? moment(dateObj.end.toDate()).format("DD/MM/YYYY")
            : "";

        return `${formattedStart} - ${formattedEnd}`;
    };

    // Date presets
    let now = today(getLocalTimeZone());
    let lastWeek = {
        start: startOfWeek(now.subtract({ weeks: 1 }), locale),
        end: endOfWeek(now.subtract({ weeks: 1 }), locale),
    };
    let lastMonth = {
        start: startOfMonth(now.subtract({ months: 1 }), locale),
        end: endOfMonth(now.subtract({ months: 1 }), locale),
    };
    let thisWeek = {
        start: startOfWeek(now, locale),
        end: endOfWeek(now, locale),
    };

    // Custom Radio component for date range selection
    const CustomRadio = (props) => {
        const { children, ...otherProps } = props;

        const handleClick = (e) => {
            const target = e.currentTarget; // safer than e.target
            const parent = target.closest('.overflow-scroll'); // Find the parent container

            if (parent) {
                const parentWidth = parent.offsetWidth;
                const itemWidth = target.offsetWidth;
                const itemLeft = target.offsetLeft;
                const scrollPosition = itemLeft - (parentWidth / 2) + (itemWidth / 2);
                parent.scrollTo({
                    left: scrollPosition,
                    behavior: 'smooth',
                });
            }
        };

        return (
            <Radio
                {...otherProps}
                onClick={handleClick}
                classNames={{
                    base: cn(
                        "flex-none m-0 h-8 bg-content1 hover:bg-content2 items-center justify-between",
                        "cursor-pointer rounded-full border-2 border-default-200/60",
                        "data-[selected=true]:border-primary",
                    ),
                    label: "text-tiny text-default-500",
                    labelWrapper: "px-1 m-0",
                    wrapper: "hidden",
                }}
            >
                {children}
            </Radio>
        );
    };

    const datePickerRef = React.useRef(null);

    const handleOpenCalendar = () => {
        if (datePickerRef.current) {
            // Look for the internal selector button
            const button = datePickerRef.current.querySelector('button[data-slot="selector-button"]');
            if (button) button.click();
        }
    };

    // Fetch invoice statuses
    useEffect(() => {
        const fetchInvoiceStatus = async () => {
            setLoadingInvoiceStatus(true);
            try {
                const response = await axios.get(`${invoicesStatusUrl}`,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    }
                );

                if (response.data.response !== 'success') {
                    console.error(response.data.message || 'Error fetching invoice status');
                    return;
                }

                setStatusList(Object.entries(response.data.result));
            } catch (error) {
                console.error(error.response?.data?.message || error.message);
            } finally {
                setLoadingInvoiceStatus(false);
            }
        };

        fetchInvoiceStatus();
    }, []);

    // Initialize form fields with params values when component mounts
    useEffect(() => {
        // Initialize invoiceNum with params value
        if (params.invoiceNum) setInvoiceNum(params.invoiceNum);

        // Initialize date range if params has startDate and endDate
        if (params.startDate && params.endDate) {
            setDateRange({
                start: parseDate(params.startDate),
                end: parseDate(params.endDate)
            });
        }

        // Initialize sortedBy if params has sortBy
        if (params.sortBy) {
            setSortedBy(new Set([params.sortBy]));
        }
    }, []);

    useEffect(() => {
        if (params.status && statusList.length > 0) {
            setSelectedStatus(statusList.find(option => option[0] === params.status));
        }
    }, [params.status, statusList]);

    // Reset state when modal is closed
    useEffect(() => {
        if (!isOpen) {
            // Reset all state values to their initial state
            setInvoiceNum('');
            setDateRange({
                start: null,
                end: null,
            });
            setSelectedStatus(null);
            setSortedBy(new Set());
            setIsStatusListOpen(false);
        }
    }, [isOpen]);

    const clearFilter = () => {
        setInvoiceNum('');
        setDateRange({
            start: null,
            end: null,
        });
        setSelectedStatus(null);
        setSortedBy(new Set());
        setIsStatusListOpen(false);

        dispatch(resetParams());
        onClose();
    };

    const handleApplyFilter = () => {
        const filter = {
            invoiceNum: invoiceNum || null,
            startDate: dateRange && dateRange.start ? moment(dateRange.start.toDate()).format("YYYY-MM-DD") : null,
            endDate: dateRange && dateRange.end ? moment(dateRange.end.toDate()).format("YYYY-MM-DD") : null,
            status: selectedStatus && selectedStatus[0] || null,
            sortBy: Array.from(sortedBy)[0] || null,
        };

        dispatch(updateParams({ ...filter }));
        onClose(); // close modal
    };

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={() => {
                onClose();
            }}
            closeOnClickOutside={false}
            title="Filter Invoices"
            position='top-32'
            footerContent={
                <div className="flex flex-row gap-2 flex-1 justify-center md:justify-end">
                    <div className="flex flex-row justify-end flex-1 items-center gap-4">
                        <Button
                            className="rounded-full bg-glb_blue text-white text-xs p-2 md:text-base md:p-4"
                            onClick={() => handleApplyFilter()}
                        >
                            <FilterIcon size={16} /> Apply Filter
                        </Button>
                        <Button
                            className="rounded-full bg-glb_red text-white text-xs p-2 md:text-base md:p-4"
                            onClick={() => clearFilter()}
                        >
                            <Delete02Icon size={16} /> Clear All
                        </Button>
                    </div>
                </div>
            }
        >
            <div>
                <div className="flex flex-col lg:flex-row gap-2">
                    <div className="w-full lg:w-1/2">
                        <UnderlinedInput
                            id="invoiceNum"
                            label="Invoice Number"
                            value={invoiceNum}
                            onChange={(e) => setInvoiceNum(e.target.value)}
                            start={true}
                        />
                    </div>
                    <div className="w-full lg:w-1/2">
                        <label htmlFor="#Status" className="block mr-2">
                            <span className="text-sm text-[#00000050] dark:text-[#FFFFFF30]">Status</span>
                            <GeneralSelector
                                id="Status"
                                placeholder="Select a status..."
                                open={isStatusListOpen}
                                onToggle={() => setIsStatusListOpen(!isStatusListOpen)}
                                onChange={(val) => {
                                    setSelectedStatus(statusList.find(opt => opt[1] === val));
                                }}
                                selectedValue={selectedStatus ? selectedStatus[1] : ''}
                                options={statusList.map(p => p[1])}
                                loading={loadingInvoiceStatus}
                            />
                        </label>
                    </div>
                </div>

                <div className="flex flex-col lg:flex-row my-2">
                    <div ref={datePickerRef} className="cursor-pointer w-full flex justify-center items-center lg:w-[80%]">
                        <I18nProvider locale="en-GB">
                            <DateRangePicker
                                onClick={(e) => {
                                    // Prevent event from bubbling up to parent elements
                                    e.stopPropagation();
                                }}
                                calendarProps={{
                                    classNames: {
                                        base: "bg-background",
                                        headerWrapper: "pt-4 bg-background",
                                        prevButton: "border-1 border-default-200 rounded-small",
                                        nextButton: "border-1 border-default-200 rounded-small",
                                        gridHeader: "bg-background shadow-none border-b-1 border-default-100",
                                        cellButton: [
                                            "data-[today=true]:bg-default-100 data-[selected=true]:bg-transparent rounded-small",
                                            // start (pseudo)
                                            "data-[range-start=true]:before:rounded-l-small",
                                            "data-[selection-start=true]:before:rounded-l-small",
                                            // end (pseudo)
                                            "data-[range-end=true]:before:rounded-r-small",
                                            "data-[selection-end=true]:before:rounded-r-small",
                                            // start (selected)
                                            "data-[selected=true]:data-[selection-start=true]:data-[range-selection=true]:rounded-small",
                                            // end (selected)
                                            "data-[selected=true]:data-[selection-end=true]:data-[range-selection=true]:rounded-small",
                                        ],
                                    },
                                    onPress: (e) => {
                                        // Prevent calendar button clicks from bubbling up
                                        e.stopPropagation();
                                    }
                                }}
                                firstDayOfWeek="mon"
                                classNames={{
                                    label: ["!text-[#00000050] dark:!text-[#FFFFFF30] "],
                                    selectorButton: 'justify-center',
                                    input: 'hidden',
                                    separator: 'hidden',
                                    innerWrapper: 'cursor-pointer'
                                }}
                                CalendarBottomContent={
                                    <RadioGroup
                                        value={sortedBy}
                                        onValueChange={setSortedBy}
                                        aria-label="Sort By"
                                        label="Sort By"
                                        onClick={(e) => e.stopPropagation()}
                                        classNames={{
                                            base: "w-full pb-2 radiogroup",
                                            label: 'px-3',
                                            wrapper:
                                                "-my-2.5 py-2.5 px-3 gap-1 flex-nowrap max-w-[w-[calc(var(--visible-months)_*_var(--calendar-width))]] overflow-scroll hide-scrollbar",
                                        }}
                                        defaultValue="createdAt"
                                        orientation="horizontal"
                                    >
                                        <CustomRadio value="createdAt" onClick={(e) => e.stopPropagation()}>Created Date</CustomRadio>
                                        <CustomRadio value="updatedAt" onClick={(e) => e.stopPropagation()}>Updated Date</CustomRadio>
                                        <CustomRadio value="dueDate" onClick={(e) => e.stopPropagation()}>Due Date</CustomRadio>
                                    </RadioGroup>
                                }
                                CalendarTopContent={
                                    <ButtonGroup
                                        fullWidth
                                        className="px-3 pb-2 pt-3 bg-content1 [&>button]:text-default-500 [&>button]:border-default-200/60"
                                        radius="full"
                                        size="sm"
                                        variant="bordered"
                                    >
                                        <Button
                                            onPress={(e) => {
                                                setDateRange(lastMonth);
                                            }}
                                        >
                                            Last Month
                                        </Button>
                                        <Button
                                            onPress={(e) => {
                                                setDateRange(lastWeek);
                                            }}
                                        >
                                            Last week
                                        </Button>
                                        <Button
                                            onPress={(e) => {
                                                setDateRange(thisWeek);
                                            }}
                                        >
                                            This Week
                                        </Button>
                                    </ButtonGroup>
                                }
                                startContent={
                                    <div
                                        className='w-full flex justify-start items-center gap-1'
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleOpenCalendar();
                                        }}
                                    >
                                        {!(!dateRange.end || !dateRange.start) && <div onClick={(e) => {
                                            e.stopPropagation();
                                            setDateRange({
                                                start: null,
                                                end: null,
                                            });
                                        }}
                                            className="flex justify-center items-center p-1 bg-transparent cursor-pointer">
                                            <Cancel01Icon size={16} />
                                        </div>}
                                        <span className={` ${!dateRange.end || !dateRange.start ? 'text-sm text-[#00000050] dark:text-[#FFFFFF30]' : 'text-medium text-black dark:text-white'}`}>
                                            {formatDate(dateRange)}
                                        </span>
                                    </div>
                                }
                                className="flex-grow w-full"
                                value={dateRange}
                                onChange={setDateRange}
                                color="primary"
                                label="Date Range"
                                variant='underlined'
                            />
                        </I18nProvider>
                    </div>
                </div>
            </div>
        </CustomModal>
    );
};

export default InvoiceFilterModal;
