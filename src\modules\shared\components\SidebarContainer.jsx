import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import {SidebarProvider} from "@/core/providers/SidebarContext.jsx";

const SidebarContainer = ({ children, showSidebar, setShowSidebar}) => {
    const [isHovered, setIsHovered] = useState(false);
    const [delayedHover, setDelayedHover] = useState(false);

    useEffect(() => {
        let timeoutId;
        if (isHovered) {
            setShowSidebar(true);
            setDelayedHover(true);
        } else {
            timeoutId = setTimeout(() => {
                setDelayedHover(false);
            }, 300);
            setShowSidebar(false);
        }
        return () => clearTimeout(timeoutId);
    }, [isHovered]);

    useEffect(() => {
        if(showSidebar) {
            setDelayedHover(true);
        }
    }, [showSidebar]);

    return (
        <motion.div
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            initial={showSidebar}
            animate={{
                width: delayedHover ? 320 : 64,
            }}
            transition={{
                type: "spring",
                damping: 20,
                stiffness: 200,
            }}
            className="right-0 border-r border-black/10 dark:border-white/10 shadow-sm z-30 overflow-y-auto min-h-screen"
        >
            <SidebarProvider>
                <div className="h-full">
                    {children}
                </div>
            </SidebarProvider>
        </motion.div>
    );
};

export default SidebarContainer;