// src/modules/dashboard/pages/Analytics.jsx

import React, { useState, useEffect } from "react";
import CountrySelector from "../../shared/components/CountrySelector";
import axios from "axios";
import { Accordion, AccordionItem, Button, Checkbox, Chip, Dropdown, DropdownItem, DropdownMenu, DropdownTrigger, getKeyValue, Navbar, NavbarItem, ScrollShadow, Spinner, Table, TableBody, TableCell, TableColumn, TableHeader, TableRow, Tooltip, User } from "@heroui/react";
import { ArrowDown01Icon, CheckListIcon, DeliveryTruck01Icon, EuroIcon, InformationCircleIcon, Logout05Icon, Moon02Icon, PercentIcon, Sun02Icon } from "hugeicons-react";
import { toast } from "sonner";
import { useThemeProvider } from "../../../core/providers/ThemeContext";
import { motion, AnimatePresence } from "framer-motion";

import codPowerGroupLogo from "@shared/assets/images/cod-power-group-logo.svg";
import codPowerGroupLogoDark from "@shared/assets/images/cod-power-group-logo-dark.svg";
import { Link } from "react-router-dom";
import GeneralSelector from "../../shared/components/GeneralSelector";
import DraggableInput from "../../shared/components/DraggableInput";
import { getToken } from "../../../core/services/TokenHandler";
import UnderlinedInput from "../../settings/components/UnderlinedInput";
import { countriesUrl, getFeesUrl, simulatorUrl } from "../../../core/redux/slices/URLs";
import DashboardLayout from "../../shared/layouts/DashboardLayout";
import { capitalize } from "lodash";

export default function Simulator() {
    const [isFromCountryOpen, setIsFromCountryOpen] = useState(false);
    const [isToCountryOpen, setIsToCountryOpen] = useState(false);
    const [loadingCountries, setLoadingCountries] = useState(false);
    const [toCountries, setToCountries] = useState([]);
    const [fromCountries, setFromCountries] = useState([]);
    const [fromCountry, setFromCountry] = useState(null);
    const [toCountry, setToCountry] = useState(false)
    const [CountrFromPage, setCountrFromPage] = useState(1)
    const [CountrToPage, setCountrToPage] = useState(1)
    const [loadingOrderPayements, setLoadingOrderPayements] = useState(false);
    const [paymentsList, setPaymentsList] = useState([]);
    const [isPayementListOpen, setIsPayementListOpen] = useState(false);
    const [selectedPayment, setSelectedPayment] = useState(null);

    const [loading, setLoading] = useState(false);
    const { currentTheme, changeCurrentTheme } = useThemeProvider();


    const [result, setResult] = useState([])

    const [loadingFees, setLoadingFees] = useState(false)
    const user = JSON.parse(localStorage.getItem('xM_htUju'));



    // Form state
    const [formData, setFormData] = useState({
        leads: '',
        cDomestic: '50%',
        dDomestic: '50%',
        sellingPrice: '',
        productCost: '',
        gWeight: '',
        vWeight: '0',
        length: '',
        height: '',
        width: '',
        costLead: '',
        confirmationFees: '',
        deliveredFees: '',
        shippingFees: '',
        codFees: '',
        fullfillmentFees: '',
        returnFees: '',
        codFeesType: 'percentage',
        enteredLead: '',
        shippingType: '',
    });



    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();



        // Check if all fields are not null
        const allFieldsFilled = Object.values(formData).every(value => value !== null && value !== '');
        if (!allFieldsFilled || !fromCountry || !toCountry) {
            toast.error('Please fill in all fields');
            return;
        }

        try {
            setLoading(true);
            // Create a copy of formData with percentage values converted to numbers
            const processedFormData = { ...formData };

            // Remove % from percentage fields and convert to numbers
            if (processedFormData.cDomestic) {
                processedFormData.cDomestic = (processedFormData.cDomestic.replace('%', '') / 100).toFixed(2);
            }

            if (processedFormData.dDomestic) {
                processedFormData.dDomestic = (processedFormData.dDomestic.replace('%', '') / 100).toFixed(2);
            }

            // Real API call
            const response = await axios.post(`${simulatorUrl}`,
                {
                    ...processedFormData,
                },

                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );


            if (response.data.response !== 'success') {
                toast.error(response.data.message || 'Error simulating');
            }
            setResult(response.data.result);

        } catch (error) {
            toast.error(error.response?.data?.message || error.message)
        } finally {
            setLoading(false)
        };
    };

    // Handle form input changes with numeric validation
    const handleInputChange = (e) => {
        const { id, value } = e.target;

        // Special handling for percentage fields (cDomestic and dDomestic)
        if (id === 'cDomestic' || id === 'dDomestic') {
            // Get the current value without the % symbol
            const currentValue = formData[id].replace('%', '');

            // Handle backspace/delete when removing characters
            if (value.length < formData[id].length) {
                // If user is trying to delete the % symbol, don't allow it
                // Instead, remove the last digit if there are any
                if (currentValue.length > 0) {
                    const newValue = currentValue.slice(0, -1);
                    // Only add % if there's a value
                    setFormData(prevData => ({
                        ...prevData,
                        [id]: newValue ? `${newValue}%` : ''
                    }));
                }
                return;
            }

            // Remove any non-numeric characters from the input
            const numericValue = value.replace(/[^0-9]/g, '');

            // Ensure the value is between 0 and 100
            if (numericValue === '' || (parseInt(numericValue) >= 0 && parseInt(numericValue) <= 100)) {
                setFormData(prevData => ({
                    ...prevData,
                    [id]: numericValue ? `${numericValue}%` : ''
                }));
            }

            return;
        }

        // Check if this is a fees field
        const feeFields = ['formData.enteredLead', 'confirmationFees', 'deliveredFees', 'shippingFees', 'codFees', 'fullfillmentFees', 'returnFees'];
        if (feeFields.includes(id)) {
            if (value === '' ||
                value === '.' ||
                /^[0-9]+$/.test(value) ||
                /^[0-9]*\.[0-9]*$/.test(value)) {

                // Update the fee field directly
                setFormData(prevData => ({
                    ...prevData,
                    [id]: value
                }));

                console.log(`Fee field ${id} updated to: ${value}`);
            }
            return;
        }

        // For regular fields
        if (value === '' ||
            value === '.' ||
            /^[0-9]+$/.test(value) ||
            /^[0-9]*\.[0-9]*$/.test(value)) {

            setFormData(prevData => ({
                ...prevData,
                [id]: value
            }));

            console.log(`Field ${id} updated to: ${value}`);
        }
        // If invalid input (non-numeric), don't update state
    };

    const CustomfetchCountries = async (type, page) => {
        // Don't fetch if we're already loading or if we've reached the last page
        if (loadingCountries || (type === "origin" && page > CountrFromPage && page > 2) || (type === "destination" && page > CountrToPage && page > 2)) {
            return;
        }

        setLoadingCountries(true);
        try {
            const response = await axios.get(`${countriesUrl}?type=${type}&page=${page}`,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                console.error(response.data.message || 'Error fetching orders status');
                return;
            }

            const newCountries = response.data.result || [];
            if (type === "origin") {
                setCountrFromPage(page);
                setFromCountries(prev => {
                    if (page === 1) {
                        // First page: reset and add "All"
                        return [...newCountries];
                    } else {
                        // Next pages: append new results (avoid duplicates)
                        const existingIds = new Set(prev.map(c => c.id));
                        const filteredNew = newCountries.filter(c => !existingIds.has(c.id));
                        return [...prev, ...filteredNew];
                    }
                });
            } else {
                setCountrToPage(page);
                setToCountries(prev => {
                    if (page === 1) {
                        return [...newCountries];
                    } else {
                        const existingIds = new Set(prev.map(c => c.id));
                        const filteredNew = newCountries.filter(c => !existingIds.has(c.id));
                        return [...prev, ...filteredNew];
                    }
                });
            }
        } catch (error) {
            console.error(error.response?.data?.message || error.message);
        } finally {
            setLoadingCountries(false);
        }
    };

    // Fetch initial data (countries and payment methods)
    useEffect(() => {

        const fetchCountries = async () => {
            setLoadingCountries(true)
            try {
                var response = await axios.get(`${countriesUrl}?type=origin&page=${1}`,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    }
                );

                if (response.data.response !== 'success') {
                    console.error(response.data.message || 'Error fetching orders status');
                }
                const allOption = { id: 0, code: 'all', name: 'All' };
                var countriesList = [allOption, ...response.data.result];
                setFromCountries(countriesList)

                response = await axios.get(`${countriesUrl}?type=destination`,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    }
                );
                countriesList = [allOption, ...response.data.result];
                setToCountries(countriesList)
            } catch (error) {
                console.error(error.response?.data?.message || error.message);
            } finally {
                setLoadingCountries(false)
            }
        }

        fetchCountries()
    }, [])

    // Fetch fees when both countries are selected
    useEffect(() => {
        const fetchFees = async () => {
            if (!fromCountry || !toCountry) return;

            try {
                setLoadingFees(true);
                const fromCountryObj = fromCountries.find(c => c.code === fromCountry);
                const toCountryObj = toCountries.find(c => c.code === toCountry);

                if (!fromCountryObj || !toCountryObj || fromCountryObj.id === 0 || toCountryObj.id === 0) return;

                const response = await axios.get(
                    `${getFeesUrl}?originCountry=${fromCountryObj.id}&destinationCountry=${toCountryObj.id}&grossWeight=${formData.gWeight}&length=${formData.length}&height=${formData.height}&width=${formData.width}`,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    }
                );

                if (response.data.response === 'success') {
                    const fees = response.data.result;
                    setFormData(prevData => ({
                        ...prevData,
                        confirmationFees: fees.confirmationFees ? parseFloat(fees.confirmationFees).toFixed(2) : '0',
                        deliveredFees: fees.deliveredFees ? parseFloat(fees.deliveredFees).toFixed(2) : '0',
                        shippingFees: fees.shippingFees ? parseFloat(fees.shippingFees).toFixed(2) : '0',
                        codFees: fees.codFees ? parseFloat(fees.codFees).toFixed(2) : '0',
                        fullfillmentFees: fees.fulfillmentFees ? parseFloat(fees.fulfillmentFees).toFixed(2) : '0',
                        returnFees: fees.returnFees ? parseFloat(fees.returnFees).toFixed(2) : '0',
                        enteredLead: fees.enteredLead ? parseFloat(fees.enteredLead).toFixed(2) : '0',
                        shippingType: fees.shippingType
                    }));
                }
            } catch (error) {
                console.error('Error fetching fees:', error);
            } finally {
                setLoadingFees(false);
            }
        };

        // Debounce logic
        const handler = setTimeout(() => {
            fetchFees();
        }, 500); // 500ms debounce

        return () => clearTimeout(handler);
    }, [
        fromCountry,
        toCountry,
        fromCountries,
        toCountries,
        formData.gWeight,
        formData.length,
        formData.height,
        formData.width
    ]);



    return (
        <DashboardLayout title="Simulator">
            <div className="flex justify-center items-end gap-2 ">
                {toCountry && fromCountry && result && result.length > 0 && (
                    <div className="w-full flex max-w-[600px] mx-auto justify-center items-center gap-2">
                        <motion.div
                            className="text-sm text-center lg:text-lg w-fit mx-auto font-semibold mb-4 flex flex-col justify-center items-center p-2 "
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{
                                type: "spring",
                                stiffness: 260,
                                damping: 20,
                                duration: 4.5
                            }}
                        >
                            <h3 className="text-sm text-center lg:text-lg font-light">From</h3>
                            {fromCountries.find(option => option.code === fromCountry)?.name || 'None'}
                        </motion.div>
                        <div className="flex justify-center items-center gap-1 overflow-hidden relative w-[200px] h-6">

                            {(<motion.div
                                className="flex items-center gap-1 absolute"
                                initial={{ x: -100 }}
                                animate={{ x: 100 }}
                                transition={{
                                    duration: 4.5,
                                    repeat: Infinity,
                                    ease: "linear"
                                }}
                            >
                                {Array(40).fill(0).map((_, idx) => (
                                    <DeliveryTruck01Icon color="primary" key={idx} className="text-glb_blue" />
                                ))}
                            </motion.div>)}
                        </div>
                        <motion.div
                            className="text-sm text-center lg:text-lg w-fit mx-auto font-semibold mb-4 flex flex-col justify-center items-center p-2 "
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{
                                type: "spring",
                                stiffness: 260,
                                damping: 20,
                                duration: 0.5
                            }}
                        >
                            <h3 className="text-sm text-center lg:text-lg font-light">To</h3>
                            {toCountries.find(option => option.code === toCountry)?.name || 'None'}
                        </motion.div>
                    </div>

                )}

            </div>
            <div className="w-full relative py-4 mx-auto grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 justify-start my-4 gap-3 flex-1 flex-grow h-full overflow-y-auto">
                <div className="flex flex-col col-span-1 gap-2 mx-auto  max-w-[800px] ">
                    <div className="">

                        <div className="flex-col flex w-full gap-2 ">
                            <div className="flex flex-col gap-2 flex-1">
                                <label className="block mt-3 text-sm text-[#00000050] dark:text-[#FFFFFF30] mb-1">From Country</label>
                                {/* <CountrySelector
                                    placeholder="Select a country"
                                    open={isFromCountryOpen}
                                    onToggle={() => {
                                        if (isFromCountryOpen) {
                                            setCountrFromPage(1);
                                        }
                                        setIsFromCountryOpen(!isFromCountryOpen);
                                    }}
                                    COUNTRIES={fromCountries}
                                    loading={loadingCountries}
                                    id="fromCountry"
                                    onChange={(id) => setFromCountry(fromCountries.find(option => option.id === id).code)}
                                    defaultSelectedKeys={fromCountries.find(option => option.id === 0)}
                                    selectedValue={fromCountry ? fromCountries.find(option => option.code === fromCountry) : null}
                                    useAll={false}
                                    required
                                    onEndScroll={() => CustomfetchCountries("origin", CountrFromPage + 1)}
                                /> */}
                                <CountrySelector
                                    placeholder="Select an origin country"
                                    open={isFromCountryOpen}
                                    onToggle={() => setIsFromCountryOpen(!isFromCountryOpen)}
                                    COUNTRIES={fromCountries}
                                    loading={loadingCountries}
                                    id="fromCountry"
                                    useAll={false}
                                    onChange={(id) => setFromCountry(id)}
                                    defaultSelectedKeys={
                                        fromCountry
                                            ? fromCountries.find(
                                                (option) => option.id === fromCountry
                                            )
                                            : null
                                    }
                                    selectedValue={
                                        fromCountry
                                            ? fromCountries.find((option) => option.id === fromCountry)
                                            : null
                                    }
                                />
                            </div>
                            <div className="flex flex-col gap-2 flex-1">
                                <label className="block mt-3 text-sm text-[#00000050] dark:text-[#FFFFFF30] mb-1">To Country</label>
                                {/* <CountrySelector
                                    open={isToCountryOpen}
                                    onToggle={() => {
                                        if (isToCountryOpen) {
                                            setCountrToPage(1);
                                        }
                                        setIsToCountryOpen(!isToCountryOpen);
                                    }}
                                    COUNTRIES={toCountries}
                                    loading={loadingCountries}
                                    id="toCountry"
                                    onChange={(id) => setToCountry(toCountries.find(option => option.id === id).code)}
                                    defaultSelectedKeys={toCountries.find(option => option.id === 0)}
                                    selectedValue={toCountry ? toCountries.find(option => option.code === toCountry) : { id: 0, code: 'all', name: 'All' }}
                                    useAll={false}
                                    required
                                    onEndScroll={() => CustomfetchCountries("destination", CountrToPage + 1)}
                                /> */}
                                <CountrySelector
                                    placeholder="Select a destination country"
                                    open={isToCountryOpen}
                                    onToggle={() => setIsToCountryOpen(!isToCountryOpen)}
                                    COUNTRIES={toCountries}
                                    loading={loadingCountries}
                                    id="toCountry"
                                    useAll={false}
                                    onChange={(id) => setToCountry(id)}
                                    defaultSelectedKeys={
                                        toCountry
                                            ? toCountries.find(
                                                (option) => option.id === toCountry
                                            )
                                            : null
                                    }
                                    selectedValue={
                                        toCountry
                                            ? toCountries.find((option) => option.id === toCountry)
                                            : null
                                    }
                                />
                            </div>
                        </div>
                    </div>
                    {fromCountry && toCountry && <div className="flex flex-col w-full">
                        <div className="grid grid-cols-[60%_40%] w-full gap-2">
                            <UnderlinedInput
                                autoFocus
                                id="leads"
                                label="Leads"
                                start={true}
                                value={formData.leads}
                                onChange={handleInputChange}
                                required
                                endContent={
                                    <Tooltip color='primary' content="Number of leads generated">
                                        <InformationCircleIcon size={16} className="cursor-help text-primary" />
                                    </Tooltip>
                                }
                            />
                            <UnderlinedInput
                                id="costLead"
                                label="Cost/Lead"
                                start={true}
                                value={formData.costLead}
                                onChange={handleInputChange}
                                required
                                endContent={
                                    <Tooltip color='primary' content="Cost per lead">
                                        <InformationCircleIcon size={16} className="cursor-help text-primary" />
                                    </Tooltip>
                                }
                            />
                        </div>

                        <div className="mt-4">Call Center Fees</div>
                        <div className="grid grid-col-1 sm:grid-cols-3 w-full  gap-2">
                            <UnderlinedInput
                                isDisabled={user === null || user.email !== '<EMAIL>'}
                                startContent={loadingFees && <Spinner size="xs" color="primary" />}
                                classNames={{
                                    label: "text-[#000000] dark:text-[#ffffff]"
                                }}
                                id="enteredLead"
                                label="Per Order"
                                value={formData.enteredLead}
                                onChange={handleInputChange}
                                start={true}
                            />
                            <UnderlinedInput
                                isDisabled={user === null || user.email !== '<EMAIL>'}
                                startContent={loadingFees && <Spinner size="xs" color="primary" />}
                                classNames={{
                                    label: "text-[#000000] dark:text-[#ffffff]"
                                }}
                                id="confirmationFees"
                                label="Confirmation"
                                value={formData.confirmationFees}
                                onChange={handleInputChange}
                                start={true}
                            />
                            <UnderlinedInput
                                isDisabled={user === null || user.email !== '<EMAIL>'}
                                classNames={{
                                    label: "text-[#000000] dark:text-[#ffffff]"
                                }}
                                id="deliveredFees"
                                label="Delivered"
                                value={formData.deliveredFees}
                                onChange={handleInputChange}
                                start={true}
                            />

                        </div>
                        <div className="mt-4 pt-4 border-t border-glb_blue border-dashed ">Shipping Fees</div>
                        <div className="flex justify-between w-full items-center gap-2">
                            <UnderlinedInput
                                isDisabled={user === null || user.email !== '<EMAIL>'}
                                classNames={{
                                    label: "text-[#000000] dark:text-[#ffffff]"
                                }}
                                id="shippingFees"
                                label={capitalize(formData.shippingType)}
                                value={formData.shippingFees}
                                onChange={handleInputChange}
                                start={true}
                            />
                            <UnderlinedInput
                                isDisabled={user === null || user.email !== '<EMAIL>'}
                                classNames={{
                                    label: "text-[#000000] dark:text-[#ffffff]"
                                }}
                                id="codFees"
                                label="COD"
                                value={formData.codFees}
                                onChange={handleInputChange}
                                start={true}
                                endContent={
                                    <Tooltip color='primary' content={formData.codFeesType === 'percentage' ? "Percentage value (0-100%)" : "Fixed value (in €)"}>
                                        {formData.codFeesType === 'percentage' ? <PercentIcon size={16} className="cursor-help text-primary" /> : <EuroIcon size={16} className="cursor-help text-primary" />}
                                    </Tooltip>
                                }
                            />
                        </div>
                        <div className="flex justify-between w-full items-center gap-2">
                            <UnderlinedInput
                                isDisabled={user === null || user.email !== '<EMAIL>'}
                                classNames={{
                                    label: "text-[#000000] dark:text-[#ffffff]"
                                }}
                                id="fullfillmentFees"
                                label="Fullfillment"
                                value={formData.fullfillmentFees}
                                onChange={handleInputChange}
                                start={true}
                            />
                            <UnderlinedInput
                                isDisabled={user === null || user.email !== '<EMAIL>'}
                                classNames={{
                                    label: "text-[#000000] dark:text-[#ffffff]"
                                }}
                                id="returnFees"
                                label="Return"
                                value={formData.returnFees}
                                onChange={handleInputChange}
                                start={true}
                            />
                        </div>
                    </div>}
                    <form className='border-t border-glb_blue border-dashed mt-2 pt-2' onSubmit={handleSubmit}>

                        <div className="flex justify-between w-full items-center gap-2">
                            <DraggableInput
                                id="cDomestic"
                                label="Confirmation rate"
                                value={formData.cDomestic}
                                onChange={handleInputChange}
                                required
                                step={1}
                                min={0}
                                max={100}
                                endContent={
                                    <>
                                        <Tooltip color='primary' content="Percentage value (0-100%). Click and drag left/right to adjust.">
                                            <InformationCircleIcon size={16} className="cursor-help text-primary" />
                                        </Tooltip>
                                    </>
                                }
                                start={true}
                            />
                            <DraggableInput
                                id="dDomestic"
                                label="Delivery rate"
                                value={formData.dDomestic}
                                onChange={handleInputChange}
                                required
                                step={1}
                                min={0}
                                max={100}
                                endContent={
                                    <>
                                        <Tooltip color='primary' content="Percentage value (0-100%). Click and drag left/right to adjust.">
                                            <InformationCircleIcon size={16} className="cursor-help text-primary" />
                                        </Tooltip>
                                    </>
                                }
                                start={true}
                            />
                        </div>
                        <div className="flex justify-between w-full items-center gap-2">
                            <UnderlinedInput
                                id="sellingPrice"
                                label="Selling Price"
                                value={formData.sellingPrice}
                                onChange={handleInputChange}
                                required
                                start={true}
                            />
                            <UnderlinedInput
                                id="productCost"
                                label="Product Cost"
                                value={formData.productCost}
                                onChange={handleInputChange}
                                required
                                start={true}
                            />
                        </div>
                        <div className="flex justify-between w-full items-center gap-2">
                            <UnderlinedInput
                                id="gWeight"
                                label="Gross Weight (Kg)"
                                value={formData.gWeight}
                                onChange={handleInputChange}
                                required
                                start={true}
                            />
                            <UnderlinedInput
                                id="vWeight"
                                label="Volumetric Weight"
                                value={formData.vWeight}
                                onChange={handleInputChange}
                                required
                                isDisabled={true}
                                start={true}
                            />
                        </div>
                        <div className="flex justify-between w-full items-center gap-2">
                            <UnderlinedInput
                                id="length"
                                label="Length (Cm)"
                                value={formData.length}
                                onChange={handleInputChange}
                                required
                                start={true}
                            />
                            <UnderlinedInput
                                id="height"
                                label="Height (Cm)"
                                value={formData.height}
                                onChange={handleInputChange}
                                required
                                start={true}
                            />
                            <UnderlinedInput
                                id="width"
                                label="Width (Cm)"
                                value={formData.width}
                                onChange={handleInputChange}
                                required
                                start={true}
                            />
                        </div>


                        <div className="mt-4">
                            <Button
                                type="submit"
                                isLoading={loading}
                                className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors w-full"
                            >
                                Calculate
                            </Button>
                        </div>

                    </form>
                </div>

                <div className="flex flex-col xl:col-span-2 2xl:col-span-3 gap-2 flex-1 flex-grow" >

                    <div className="flex flex-col gap-2 w-full">

                        <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
                            {['Leads', 'Confirmed', 'Delivered', 'SH/U'].map((i, idx) => (
                                <div key={idx} className="flex flex-col justify-center items-center p-2 rounded-lg border-1 border-[#00000020] dark:border-[#ffffff20]">
                                    <h1 className="text-lg font-semibold">{i}</h1>
                                    <h1 className="text-sm font-normal">{result[i] || '--'}</h1>
                                </div>
                            ))}
                        </div>
                    </div>
                    <div className="grid grid-cols-1 2xl:grid-cols-2 w-full gap-2">
                        <div className="flex flex-col gap-2 w-full p-2 rounded-lg border-1 border-[#00000020] dark:border-[#ffffff20] ">

                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 w-full">
                                <div className="col-span-1 flex flex-col gap-2 justify-start items-start" >
                                    {['CPD Product', 'CPD Shipping', 'CPD Fulfillment', 'CPD VAT', 'CPD COD'].map((i, idx) => (
                                        <div key={idx} className="flex w-full justify-between items-center p-2 gap-3">
                                            <h1 className="text-lg font-normal">{i}</h1>
                                            <h1 className="text-sm font-normal">{result[i] || '--'}</h1>
                                        </div>
                                    ))}
                                </div>
                                <div className="col-span-1 flex flex-col flex-wrap gap-2 justify-start items-start" >

                                    {['CPD Ads', 'CPD Calls', 'CPD Returns', 'CPD Entered Lead'].map((i, idx) => (

                                        <div key={idx} className={` flex w-full justify-between items-center p-2 gap-3`}>
                                            <h1 className="text-lg font-normal">{i}</h1>
                                            <div className="flex justify-center items-center gap-2">
                                                <h1 className="text-sm font-normal">{result[i] || '--'}</h1>
                                            </div>
                                        </div>

                                    ))}
                                </div>
                            </div>
                        </div>


                        <div className="flex flex-col gap-2 w-full p-2 rounded-lg border-1 border-[#00000020] dark:border-[#ffffff20]">

                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 w-full">
                                <div className="col-span-1 flex flex-col gap-2 justify-start items-start" >
                                    {['Sales', 'Products', 'Fulfillment', 'Shipping', 'VAT'].map((i, idx) => (
                                        <div key={idx} className="flex justify-between w-full items-center p-2 gap-3">
                                            <h1 className="text-lg font-normal">{i}</h1>
                                            <h1 className="text-sm font-normal">{result[i] || '--'}</h1>
                                        </div>
                                    ))}
                                </div>
                                <div className="col-span-1 flex flex-col gap-2 justify-start items-start" >

                                    {['COD', 'Returns', 'Ads', 'Calls'].map((i, idx) => (
                                        <div key={idx} className="flex justify-between w-full items-center p-2 gap-3">
                                            <h1 className="text-lg font-normal">{i}</h1>
                                            <h1 className="text-sm font-normal">{result[i] || '--'}</h1>
                                        </div>
                                    ))}
                                </div>

                            </div>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 w-full">
                            <div className={`bg-glb_blue text-white rounded-lg flex w-full justify-between items-center p-2 gap-3`}>
                                <h1 className="text-lg font-normal">{'CPD'}</h1>
                                <div className="flex justify-center items-center gap-2">
                                    <h1 className="text-sm font-normal">{result['CPD'] || '--'}</h1>
                                </div>
                            </div>
                            <AnimatePresence mode="wait" >
                                <motion.div
                                    key={result['EPD']} // Add key to force re-render on change
                                    initial={{ scale: 0.95 }}
                                    animate={{ scale: 1 }}
                                    exit={{ scale: 0.95 }}
                                    transition={{
                                        type: "spring",
                                        stiffness: 500,
                                        damping: 15,
                                        duration: 0.2
                                    }}
                                    className={`${!isNaN(Number(result['EPD']?.replace(',', '.'))) && Number(result['EPD']?.replace(',', '.')) > 0 ? 'border-glb_green border-2 rounded-lg' : 'border-glb_red border-2 rounded-lg'} flex w-full justify-between items-center p-2 gap-3`}
                                >
                                    <h1 className={`${!isNaN(Number(result['EPD']?.replace(',', '.'))) && Number(result['EPD']?.replace(',', '.')) > 0 ? 'text-glb_green font-bold' : 'text-glb_red font-bold'} text-lg`}>{'EPD'}</h1>
                                    <div className="flex justify-center items-center gap-2">
                                        <h1 className={`${!isNaN(Number(result['EPD']?.replace(',', '.'))) && Number(result['EPD']?.replace(',', '.')) > 0 ? 'text-glb_green font-bold' : 'text-glb_red font-bold'} text-sm`}>{result['EPD'] || '--'}</h1>
                                        <motion.div
                                            className={`${!isNaN(Number(result['EPD']?.replace(',', '.'))) && Number(result['EPD']?.replace(',', '.')) > 0 ? 'bg-glb_green' : 'bg-glb_red'} w-3 h-3 rounded-full`}
                                            animate={{
                                                boxShadow: [
                                                    `${!isNaN(Number(result['EPD']?.replace(',', '.'))) && Number(result['EPD']?.replace(',', '.')) > 0 ? '0 0 0 0 rgba(34, 197, 94, 0.4)' : '0 0 0 0 rgba(239, 68, 68, 0.4)'}`,
                                                    `${!isNaN(Number(result['EPD']?.replace(',', '.'))) && Number(result['EPD']?.replace(',', '.')) > 0 ? '0 0 0 10px rgba(34, 197, 94, 0)' : '0 0 0 10px rgba(239, 68, 68, 0)'}`
                                                ]
                                            }}
                                            transition={{
                                                duration: 1.5,
                                                repeat: Infinity,
                                                ease: "easeInOut"
                                            }}
                                        />
                                    </div>
                                </motion.div>
                            </AnimatePresence>

                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}