import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import BlankLayout from "@/modules/shared/layouts/BlankLayout";
import { Mail01Icon, SettingsError02Icon, ViewIcon, ViewOffSlashIcon } from "hugeicons-react";
import { useState } from "react";
import { forgotPasswordSchema } from "@/modules/shared/schemes/userSchema.js";
import { useNavigate } from "react-router-dom";
import { isObjectEmpty } from "@/core/utils/object.js";
import { RouteNames } from "../../../core/routes/routes";
import { checkResetCode, forgotPassword, resetPassword, clearMessages } from "../../../core/redux/slices/authSlice";
import InputOTP from "../../shared/components/InputOTP";
import { useDispatch, useSelector } from "react-redux";
import { registerSchema } from "../../shared/schemes/userSchema";
import { toast } from "sonner";

export default function ForgotPassword() {
    const [validation, setValidation] = useState({});
    const [email, setEmail] = useState('');
    const [otpCode, setOtpCode] = useState('');

    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [showForgotPassword, setShowForgotPassword] = useState(false);

    const navigate = useNavigate();
    const dispatch = useDispatch();

    const [curretnStep, setCurretnStep] = useState(0)
    const { loading, error } = useSelector((state) => state.auth);

    const isValidEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleEmailInputChange = async (e) => {
        const email = e.target.value;
        setEmail(email);
        const check = await registerSchema.safeParse({ email, password });
        if (!check.success) {
            setValidation(prevValidation => ({
                ...prevValidation,
                email: check.error.flatten().fieldErrors.email
            }));
        } else {
            setValidation(prevValidation => ({
                ...prevValidation,
                email: null
            }));
        }
    }
    const handlePasswordInputChange = async (e) => {
        const password = e.target.value;
        setPassword(password);
        // const check = await loginSchema.safeParse({ email, password });

        // if (!check.success) {
        //     setValidation(prevValidation => ({
        //         ...prevValidation,
        //         password: check.error.flatten().fieldErrors.password
        //     }));
        // } else {
        //     setValidation(prevValidation => ({
        //         ...prevValidation,
        //         password: null
        //     }));
        // }
    }

    const handleConfirmPasswordInputChange = (e) => {
        const confirmPassword = e.target.value;
        setConfirmPassword(confirmPassword);
        setValidation(prevValidation => ({
            ...prevValidation,
            confirmPassword: confirmPassword !== password ? "Passwords do not match" : null
        }));
    };


    const HandleBack = () => {
        if (curretnStep > 0) {
            setCurretnStep(curretnStep - 1);
        } else {
            dispatch(clearMessages()); // Clear error state
            navigate(RouteNames.login); // Redirect to login page
        }
    }
    const handleSubmit = async (e) => {
        switch (curretnStep) {
            case 0:
                try {
                    const forgotPasswordResult = await dispatch(forgotPassword({ email }));
                    if (forgotPassword.fulfilled.match(forgotPasswordResult)) {
                        setCurretnStep(1); // Move to the next step
                    }
                } catch (error) {
                    console.error("Error in forgotPassword:", error);
                }
                break;
            case 1:
                try {
                    const checkResetCodeResult = await dispatch(checkResetCode({ email, code: otpCode }));
                    if (checkResetCode.fulfilled.match(checkResetCodeResult)) {
                        setCurretnStep(2); // Move to the next step
                    }
                } catch (error) {
                    console.error("Error in checkResetCode:", error);
                }
                break;
            case 2:
                try {
                    if (password !== confirmPassword) {
                        toast.error("Passwords do not match");
                        return;
                    }
                    const resetPasswordResult = await dispatch(resetPassword({ newPassword: password, code: otpCode, email }));
                    if (resetPassword.fulfilled.match(resetPasswordResult)) {
                        navigate(RouteNames.login); // Redirect to login page
                    }
                } catch (error) {
                    console.error("Error in resetPassword:", error);
                }
                break;
            default:
                break;
        }
    };

    return (
        <BlankLayout showNavbar={true}>
            <div className="relative min-h-screen">
                <div className="px-8 mx-auto text-center w-[25rem]">

                    {curretnStep === 0 &&
                        <form action="" className="mt-8">
                            <h2 className="my-3 text-xl font-bold text-primary dark:text-white">Restore your account !</h2>
                            <p className="text-gray-400 mb-4">Please enter a valid email to this website, so as to recover your
                                account by your inbox.</p>
                            <Input

                                isInvalid={validation?.email} errorMessage={validation?.email} type="email" placeholder="Your Email Address" value={email}
                                onChange={handleEmailInputChange}
                                classNames={{
                                    inputWrapper: `bg-white dark:bg-zinc-900 border-2 ${validation?.email ? 'border-red-200 dark:border-glb_red' : 'dark:border-zinc-700 border-gray-200'} rounded-md py-6 px-4 focus:bg-normal`
                                }} endContent={<Mail01Icon />} required />
                            {/* {validation?.email && (
                                <span className="text-red-400 my-2 text-sm block text-start">{validation?.email}</span>)} */}

                            <Button isLoading={loading} disabled={!isValidEmail(email)} className="w-full my-4 font-bold text-white rounded bg-glb_blue px-0 py-6 cursor-pointer"
                                onClick={(e) => handleSubmit(e)} >
                                Continue
                            </Button>
                            <Button className="w-full  font-bold text-white rounded bg-danger px-0 py-6 cursor-pointer"
                                onClick={() => HandleBack()} >
                                Back
                            </Button>
                        </form>}
                    {curretnStep === 1 &&
                        <form action="" className="mt-8 ">
                            <h1 className="text-xl font-bold text-primary dark:text-white">A verification code has been sent to  {email}</h1>
                            <p className="text-gray-400 mb-4">Please check your email and enter the code below to verify your account</p>
                            <div className="my-6 flex flex-col py-12 px-8 rounded-lg justify-center items-center gap-2 ">
                                <InputOTP
                                    length={6}
                                    value={otpCode}
                                    onChange={setOtpCode}
                                    isInvalid={error !== null}
                                    errorMessage={error}
                                />
                                {error && <div className="flex justify-start items-center text-glb_red pb-3 gap-2">
                                    <SettingsError02Icon /><h6> {error}</h6></div>}
                                <Button isLoading={loading} className="w-full my-4 font-bold text-white rounded bg-primary dark:bg-glb_blue"
                                    onClick={(e) => handleSubmit(e)}
                                >Verify</Button>
                                <Button className="w-full  font-bold text-white rounded bg-danger px-0 py-6 cursor-pointer"
                                    onClick={() => HandleBack()} >
                                    Back
                                </Button>
                            </div>
                        </form>}
                    {curretnStep === 2 &&
                        <form action="" className="mt-8 ">
                            <h1 className="text-xl font-bold text-primary dark:text-white">Set your new password</h1>
                            <p className="text-gray-400 mb-4">Please enter and confirm your new password below to update your account.</p>
                            <div className="my-6 flex flex-col py-12 px-8 rounded-lg justify-center items-center gap-2 ">
                                <Input
                                    isInvalid={error !== null} type={showPassword ? 'text' : 'password'} className="mt-3" placeholder="Your New Password"
                                    value={password} classNames={{
                                        inputWrapper: `bg-white  dark:bg-zinc-900 border-1 ${validation?.password ? 'border-red-200 dark:border-glb_red' : 'dark:border-zinc-700 border-gray-200'} rounded-md py-6 px-4 focus:bg-normal`
                                    }} endContent={showPassword ?
                                        <ViewOffSlashIcon className="cursor-pointer" onClick={() => setShowPassword(false)} /> :
                                        <ViewIcon className="cursor-pointer" onClick={() => setShowPassword(true)} />
                                    }
                                    onChange={handlePasswordInputChange} required />
                                <Input
                                    isInvalid={error !== null} type={showForgotPassword ? 'text' : 'password'} className="" placeholder="Confirm New Password"
                                    value={confirmPassword} classNames={{
                                        inputWrapper: `bg-white  dark:bg-zinc-900 border-1 ${validation?.confirmPassword ? 'border-red-200 dark:border-glb_red' : 'dark:border-zinc-700 border-gray-200'} rounded-md py-6 px-4 focus:bg-normal`
                                    }} endContent={showForgotPassword ?
                                        <ViewOffSlashIcon className="cursor-pointer" onClick={() => setShowForgotPassword(false)} /> :
                                        <ViewIcon className="cursor-pointer" onClick={() => setShowForgotPassword(true)} />
                                    }
                                    onChange={handleConfirmPasswordInputChange} required />
                                {validation?.confirmPassword && (
                                    <span className="text-red-400 my-2 text-sm block text-start">{validation?.confirmPassword}</span>
                                )}
                                {error && <div className="flex justify-start items-center text-glb_red pb-3 gap-2">
                                    <SettingsError02Icon /><h6> {error}</h6></div>}
                                <Button isLoading={loading} className="w-full my-4 font-bold text-white rounded bg-primary dark:bg-glb_blue"
                                    onClick={(e) => handleSubmit(e)}
                                >Reset Password</Button>
                                <Button className="w-full  font-bold text-white rounded bg-danger px-0 py-6 cursor-pointer"
                                    onClick={() => HandleBack()} >
                                    Back
                                </Button>
                            </div>
                        </form>}
                </div>
            </div>
        </BlankLayout>
    );
}