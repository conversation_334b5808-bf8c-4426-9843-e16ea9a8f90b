import { render } from "react-dom";

const CustomTile = ({
    data,
    renderCell,
}) => {
    return (
        <div
            className=" py-4 px-2 lg:px-16 flex justify-between items-center font-medium text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
            {Object.entries(data).map(([label, value]) => (
                renderCell(label, value)
            ))}
        </div>
    );
};

export default CustomTile;