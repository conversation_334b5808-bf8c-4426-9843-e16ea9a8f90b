import React, { useEffect, useMemo, useRef, useState } from "react";
import CustomModal from "../../components/CustomModal";
import UnderlinedInput from "../../../settings/components/UnderlinedInput";
import GeneralSelector from "../../components/GeneralSelector";
import axios from "axios";
import { getToken } from "../../../../core/services/TokenHandler";
import moment from "moment";
import {
  endOfMonth,
  endOfWeek,
  getLocalTimeZone,
  parseDate,
  startOfMonth,
  startOfWeek,
  today,
} from "@internationalized/date";
import CountrySelector from "../../components/CountrySelector";
import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownSection,
  DropdownTrigger,
} from "@heroui/dropdown";

import {
  Cancel01Icon,
  Delete02Icon,
  FilterHorizontalIcon,
  FilterIcon,
} from "hugeicons-react";
import {
  countriesUrl,
  orderPayementsUrl,
  orderStatusUrl,
  productListUrl,
} from "../../../../core/redux/slices/URLs";
import { Button, ButtonGroup } from "@heroui/button";
import { I18nProvider, useLocale } from "@react-aria/i18n";
import { cn, DateRangePicker, Radio, RadioGroup, Tooltip } from "@heroui/react";
import { RouteNames } from "../../../../core/routes/routes";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  getOrders,
  resetParams,
  updateParams,
} from "../../../../core/redux/slices/orders/ordersManagementSlice";
import {
  setFilterApplyed,
  setFilterParams,
} from "../../../../core/redux/slices/contentSlice";
import { param } from "framer-motion/client";
import { debounce } from "lodash";

const OrderFilterModal = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { exportError, params } = useSelector((state) => state.orders);

  const [orderNo, setOrderNo] = useState("");
  const [fullName, setFullName] = useState("");
  const [phoneNum, setPhoneNum] = useState("");
  const [dateRange, setDateRange] = useState({
    start: null,
    end: null,
  });
  let { locale } = useLocale();
  // Format the date range
  const formatDate = (dateObj) => {
    if (!dateObj) return ""; // Return empty string if dateObj is null or undefined
    if (!dateObj.start || !dateObj.end)
      return `${moment().startOf("week").format("DD/MM/YYYY")} - ${moment()
        .endOf("week")
        .format("DD/MM/YYYY")}`;
    const formattedStart = dateObj?.start
      ? moment(dateObj.start.toDate()).format("DD/MM/YYYY")
      : "";
    const formattedEnd = dateObj?.end
      ? moment(dateObj.end.toDate()).format("DD/MM/YYYY")
      : "";

    return `${formattedStart} - ${formattedEnd}`;
  };
  let now = today(getLocalTimeZone());
  let lastWeek = {
    start: startOfWeek(now.subtract({ weeks: 1 }), locale),
    end: endOfWeek(now.subtract({ weeks: 1 }), locale),
  };
  let lastMonth = {
    start: startOfMonth(now.subtract({ months: 1 }), locale),
    end: endOfMonth(now.subtract({ months: 1 }), locale),
  };
  let thisWeek = {
    start: startOfWeek(now, locale),
    end: endOfWeek(now, locale),
  };

  const CustomRadio = (props) => {
    const { children, ...otherProps } = props;

    const handleClick = (e) => {
      const target = e.currentTarget; // safer than e.target
      const parent = target.closest(".overflow-scroll"); // Find the parent container

      if (parent) {
        const parentWidth = parent.offsetWidth;
        const itemWidth = target.offsetWidth;
        const itemLeft = target.offsetLeft;
        const scrollPosition = itemLeft - parentWidth / 2 + itemWidth / 2;
        parent.scrollTo({
          left: scrollPosition,
          behavior: "smooth",
        });
      }
    };

    return (
      <Radio
        {...otherProps}
        onClick={handleClick}
        classNames={{
          base: cn(
            "flex-none m-0 h-8 bg-content1 hover:bg-content2 items-center justify-between",
            "cursor-pointer rounded-full border-2 border-default-200/60",
            "data-[selected=true]:border-primary"
          ),
          label: "text-tiny text-default-500",
          labelWrapper: "px-1 m-0",
          wrapper: "hidden",
        }}>
        {children}
      </Radio>
    );
  };
  const datePickerRef = useRef(null);

  const handleOpenCalendar = () => {
    if (datePickerRef.current) {
      // Look for the internal selector button
      const button = datePickerRef.current.querySelector(
        'button[data-slot="selector-button"]'
      );
      if (button) button.click();
    }
  };

  const [loadingProductList, setLoadingProductList] = useState(false);
  const [loadingOrderStatus, setLoadingOrderStatus] = useState(false);
  const [loadingOrderPayements, setLoadingOrderPayements] = useState(false);
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [ProductList, setProductList] = useState([]);
  const [StatusList, setStatusList] = useState([]);
  const [PaymentsList, setPaymentsList] = useState([]);
  const [countries, setCountries] = useState([]);

  const [isFromCountryOpen, setIsFromCountryOpen] = useState(false);
  const [isToCountryOpen, setIsToCountryOpen] = useState(false);
  const [isProductListOpen, setIsProductListOpen] = useState(false);
  const [isStatusListOpen, setIsStatusListOpen] = useState(false);
  const [isPayementListOpen, setIsPayementListOpen] = useState(false);
  const [fromCountry, setFromCountry] = useState(0);
  const [toCountry, setToCountry] = useState(0);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [pendingProductName, setPendingProductName] = useState("Loading...");

  const [sortedBy, setSortedBy] = useState("");

  const [ProdPage, setProdPage] = useState(1);
  const [prodKey, setProdKey] = useState("");

  // Create a debounced function to update prodKey using useMemo to ensure it's stable
  const debouncedSetProdKey = useMemo(
    () =>
      debounce((value) => {
        setProdKey(value);
      }, 500), // 500ms delay
    []
  ); // Empty dependency array ensures this is only created once

  // Cleanup debounce on component unmount to avoid memory leaks
  useEffect(() => {
    return () => {
      if (debouncedSetProdKey && debouncedSetProdKey.cancel) {
        debouncedSetProdKey.cancel();
      }
    };
  }, [debouncedSetProdKey]);

  // Component state is now properly set up

  useEffect(() => {
    const fetchProductList = async () => {
      // Only show loading indicator for the first page in the OrderFilterModal
      // Loading for subsequent pages is handled by GeneralSelector
      ProdPage === 1 && setLoadingProductList(true);
      try {
        if (prodKey !== "") {
          setProdPage(1);
        }
        const response = await axios.get(
          `${productListUrl}?page=${ProdPage}&keyword=${prodKey}`,
          {
            headers: {
              Authorization: `Bearer ${getToken()}`,
            },
          }
        );

        if (response.data.response !== "success") {
          console.error(
            response.data.message || "Error fetching orders status"
          );
          return;
        }

        // If it's the first page or we're searching, replace the list
        // Otherwise, append the new results to the existing list
        if (ProdPage === 1 || prodKey) {
          setProductList(response.data.result);
        } else {
          setProductList((prevList) => [...prevList, ...response.data.result]);
        }
      } catch (error) {
        console.error(error.response?.data?.message || error.message);
      } finally {
        // Only update loading state for the first page
        ProdPage === 1 && setLoadingProductList(false);
      }
    };

    fetchProductList();
  }, [ProdPage, prodKey]);

  useEffect(() => {

    const fetchOrderStatus = async () => {
      setLoadingOrderStatus(true);
      try {
        const response = await axios.get(`${orderStatusUrl}`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (response.data.response !== "success") {
          console.error(
            response.data.message || "Error fetching orders status"
          );
        }

        setStatusList(Object.entries(response.data.result));


      } catch (error) {
        console.error(error.response?.data?.message || error.message);
      } finally {
        setLoadingOrderStatus(false);
      }
    };

    const fetchOrderPayements = async () => {
      setLoadingOrderPayements(true);
      try {
        const response = await axios.get(`${orderPayementsUrl}`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (response.data.response !== "success") {
          console.error(
            response.data.message || "Error fetching orders status"
          );
        }
        setPaymentsList(response.data.result);
      } catch (error) {
        console.error(error.response?.data?.message || error.message);
      } finally {
        setLoadingOrderPayements(false);
      }
    };
    const fetchCountries = async () => {
      setLoadingCountries(true);
      try {
        const response = await axios.get(`${countriesUrl}`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (response.data.response !== "success") {
          console.error(
            response.data.message || "Error fetching orders status"
          );
        }
        //const allOption = { id: 0, code: "all", name: "All" };
        const countriesList = [...response.data.result];
        setCountries(countriesList);
      } catch (error) {
        console.error(error.response?.data?.message || error.message);
      } finally {
        setLoadingCountries(false);
      }
    };
    fetchCountries();
    fetchOrderStatus();
    fetchOrderPayements();

  }, []);

  const ClearFilter = () => {
    setOrderNo("");
    setFullName("");
    setPhoneNum("");
    setDateRange({
      start: null,
      end: null,
    });
    setFromCountry(0);
    setToCountry(0);
    setSelectedProduct(null);
    setSelectedStatus(null);
    setSelectedPayment(null);
    setSortedBy(new Set());
    setIsFromCountryOpen(false);
    setIsToCountryOpen(false);
    setIsProductListOpen(false);
    setIsStatusListOpen(false);
    setIsPayementListOpen(false);
    setProdKey("");
    setProdPage(1);
    setPendingProductName("");

    dispatch(resetParams());
    onClose();
    navigate(RouteNames.allOrders);
  };

  const handleApplyFilter = () => {
    const filter = {
      orderNum: orderNo || null,
      consigneeContact: fullName || null,
      consigneePhone: phoneNum || null,
      startDate:
        dateRange && dateRange.start
          ? moment(dateRange.start.toDate()).format("YYYY-MM-DD")
          : null,
      endDate:
        dateRange && dateRange.end
          ? moment(dateRange.end.toDate()).format("YYYY-MM-DD")
          : null,
      productReference: selectedProduct?.id || null,
      status: (selectedStatus && selectedStatus[0]) || null,
      paymentMethod: selectedPayment?.key || null,
      originCountry: fromCountry || null,
      destinationCountry: toCountry || null,
      sortBy: Array.from(sortedBy)[0] || null,
    };
    dispatch(updateParams({ ...filter }));

    onClose(); // close modal
    navigate(RouteNames.allOrders);
  };
  // Initialize form fields with params values when component mounts
  useEffect(() => {
    // Initialize orderNo, fullName, and phoneNum with params values
    if (params.orderNum) setOrderNo(params.orderNum);
    if (params.consigneeContact) setFullName(params.consigneeContact);
    if (params.consigneePhone) setPhoneNum(params.consigneePhone);

    // Initialize date range if params has startDate and endDate
    if (params.startDate && params.endDate) {
      let startDate = params.startDate;
      let endDate = params.endDate;

      // Check if the startDate and endDate are valid strings before trying to parse them
      if (typeof startDate === 'string' && typeof endDate === 'string') {
        startDate = parseDate(startDate);
        endDate = parseDate(endDate);

        // Check if both dates were successfully parsed
        if (startDate && endDate) {
          setDateRange({
            start: startDate,
            end: endDate,
          });
        } else {
          // If parsing fails, log the error and remove the invalid params from the URL
          console.error("Invalid date format.");

          // Remove the 'start' and 'end' parameters from the URL
          const url = new URL(window.location);
          url.searchParams.delete('start');
          url.searchParams.delete('end');
          window.history.replaceState({}, '', url);
        }
      } else {
        console.error("startDate and endDate should be strings.");

        // Remove the 'start' and 'end' parameters from the URL if they are not valid strings
        const url = new URL(window.location);
        url.searchParams.delete('start');
        url.searchParams.delete('end');
        window.history.replaceState({}, '', url);
      }
    }

    // Initialize sortedBy if params has sortBy
    if (params.sortBy) {
      setSortedBy(new Set([params.sortBy]));
    }
  }, []); // Empty dependency array means this runs once when component mounts

  useEffect(() => {
    // Set selected status if it exists in StatusList
    if (params.status && StatusList.length > 0) {
      setSelectedStatus(
        StatusList.find((option) => option[0] === params.status)
      );
    }

    // Set selected product if it exists in ProductList
    if (params.productReference && ProductList.length > 0) {
      const product = ProductList.find((p) => p.id === params.productReference);
      if (product) {
        setSelectedProduct(product);
      }
    }

    // Set selected payment method if it exists in PaymentsList
    if (params.paymentMethod && Object.keys(PaymentsList).length > 0) {
      const paymentLabel = PaymentsList[params.paymentMethod];
      if (paymentLabel) {
        setSelectedPayment({ key: params.paymentMethod, label: paymentLabel });
      }
    }

    // Fetch product name if we have a reference but ProductList is empty
    if (
      params.productReference &&
      ProductList.length === 0 &&
      !pendingProductName
    ) {
      const fetchProductName = async () => {
        try {
          const response = await axios.get(
            `${productListUrl}/${params.productReference}`,
            {
              headers: {
                Authorization: `Bearer ${getToken()}`,
              },
            }
          );

          if (response.data.response === "success" && response.data.result) {
            setPendingProductName(response.data.result.name || "Product");
          }
        } catch (error) {
          console.error("Error fetching product name:", error);
        }
      };

      fetchProductName();
    }
  }, [params, ProductList, StatusList, PaymentsList, pendingProductName]);

  // Reset state when modal is closed
  useEffect(() => {
    if (!isOpen) {
      // Reset all state values to their initial state
      setOrderNo("");
      setFullName("");
      setPhoneNum("");
      setDateRange({
        start: null,
        end: null,
      });
      setFromCountry(0);
      setToCountry(0);
      setSelectedProduct(null);
      setSelectedStatus(null);
      setSelectedPayment(null);
      setSortedBy(new Set());
      setIsFromCountryOpen(false);
      setIsToCountryOpen(false);
      setIsProductListOpen(false);
      setIsStatusListOpen(false);
      setIsPayementListOpen(false);
      setPendingProductName("");

      // Reset product search state
      setProdKey("");
      setProdPage(1);
      setProductList([]);

      // Cancel any pending debounced calls
      if (debouncedSetProdKey && debouncedSetProdKey.cancel) {
        debouncedSetProdKey.cancel();
      }
    }
  }, [isOpen, debouncedSetProdKey]);

  // Reset filter modal state when page changes (when params are reset)
  useEffect(() => {
    // Check if params have been reset (all filter params are null/empty)
    const isParamsReset = !params.orderNum &&
      !params.consigneeContact &&
      !params.consigneePhone &&
      !params.startDate &&
      !params.endDate &&
      !params.productReference &&
      !params.status &&
      !params.paymentMethod &&
      !params.originCountry &&
      !params.destinationCountry &&
      !params.sortBy;

    if (isParamsReset && isOpen) {
      // Reset all modal state to match the reset params
      setOrderNo("");
      setFullName("");
      setPhoneNum("");
      setDateRange({
        start: null,
        end: null,
      });
      setFromCountry(0);
      setToCountry(0);
      setSelectedProduct(null);
      setSelectedStatus(null);
      setSelectedPayment(null);
      setSortedBy(new Set());
      setIsFromCountryOpen(false);
      setIsToCountryOpen(false);
      setIsProductListOpen(false);
      setIsStatusListOpen(false);
      setIsPayementListOpen(false);
      setPendingProductName("");

      // Reset product search state
      setProdKey("");
      setProdPage(1);
      setProductList([]);

      // Cancel any pending debounced calls
      if (debouncedSetProdKey && debouncedSetProdKey.cancel) {
        debouncedSetProdKey.cancel();
      }
    }
  }, [params, isOpen, debouncedSetProdKey]);

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={() => {
        // Call the onClose function provided by the parent component
        onClose();
      }}
      closeOnClickOutside={false}
      title="Filter Properties"
      position="top-32"
      footerContent={
        <div className=" flex flex-row  gap-2 flex-1 justify-center md:justify-end">
          {/* <div>
                        <h4 className="font-medium text-xs md:text-sm">3 Selected</h4>
                        <span className="text-xs md:text-sm text-gray-400">17 Total Filtered</span>
                    </div> */}
          <div className="flex flex-row justify-end flex-1 items-center gap-4">
            <Button
              className=" rounded-full bg-glb_blue text-white text-xs p-2 md:text-base md:p-4 "
              onPress={() => handleApplyFilter()}>
              <FilterIcon size={16} /> Apply Filter
            </Button>
            <Button
              className=" rounded-full bg-glb_red text-white text-xs p-2 md:text-base md:p-4 "
              onPress={() => ClearFilter()}>
              <Delete02Icon size={16} /> Clear All
            </Button>
          </div>
        </div>
      }>
      <div>
        <div className="flex flex-col lg:flex-row gap-2 ">
          <div className="w-full lg:w-1/2">
            <UnderlinedInput
              id="orderNo"
              label="Order No"
              value={orderNo}
              onChange={(e) => setOrderNo(e.target.value)}
              start={true}
            />
          </div>
          <div className="w-full lg:w-1/2">
            <label htmlFor="#Products" className="block mr-2">
              <span className="text-sm text-[#00000050]  dark:text-[#FFFFFF30]">
                Product
              </span>
              <GeneralSelector
                id="Products"
                useAll={false}
                placeholder="Select a product"
                open={isProductListOpen}
                onToggle={() => setIsProductListOpen(!isProductListOpen)}
                onChange={(val) => {

                  setSelectedProduct(
                    ProductList.find((opt) => opt.name === val)
                  );

                  setProdKey("");
                }}
                onSearchChange={(val) => {
                  // Use the debounced function instead of directly setting prodKey
                  if (debouncedSetProdKey) {
                    debouncedSetProdKey(val);
                  } else {
                    // Fallback in case debounce is not available
                    setProdKey(val);
                  }
                }}
                onEndScroll={() => {
                  // Return a promise that loads the next page of products
                  return new Promise((resolve, reject) => {
                    // Check if we have more products to load
                    if (ProductList.length === 0) {
                      reject("No more products to load");
                      return;
                    }

                    // Increment the page number to trigger the useEffect
                    setProdPage((prevPage) => prevPage + 1);

                    // Wait for the products to load (simulate API delay)
                    // In a real app, you might want to use a more sophisticated approach
                    setTimeout(() => {
                      resolve();
                    }, 1000);
                  });
                }}
                defaultSelectedKeys={
                  params.productReference
                    ? ProductList.length > 0
                      ? ProductList.find(
                        (p) => p.id === params.productReference
                      )
                      : null
                    : null
                }
                selectedValue={
                  selectedProduct
                    ? selectedProduct.name
                    : ""
                }

                loading={loadingProductList}
                options={ProductList.map((p) => p.name)}
              />
            </label>
          </div>
        </div>
        <div className="flex flex-col lg:flex-row gap-2">
          <div className="w-full lg:w-1/2">
            <UnderlinedInput
              id="fullname"
              label="Full Name"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              start={true}
            />
          </div>
          <div className="w-full lg:w-1/2">
            <UnderlinedInput
              id="phone"
              label="Phone"
              value={phoneNum}
              onChange={(e) => setPhoneNum(e.target.value)}
              start={true}
            />
          </div>
        </div>
        <div className="flex flex-col lg:flex-row my-2">
          <div className="w-full lg:w-1/2">
            <label htmlFor="#fromCountry" className="block mr-2">
              <span className="text-sm text-[#00000050]  dark:text-[#FFFFFF30]">
                From Country{" "}
              </span>
              <CountrySelector
                placeholder="Select a country"
                open={isFromCountryOpen}
                onToggle={() => setIsFromCountryOpen(!isFromCountryOpen)}
                COUNTRIES={countries}
                loading={loadingCountries}
                id="fromCountry"
                useAll={false}
                onChange={(id) => setFromCountry(id)}
                defaultSelectedKeys={
                  params.originCountry
                    ? countries.find(
                      (option) => option.id === params.originCountry
                    )
                    : null
                }
                selectedValue={
                  fromCountry
                    ? countries.find((option) => option.id === fromCountry)
                    : null
                }
              />
            </label>
          </div>
          <div className="w-full lg:w-1/2">
            <label htmlFor="#toCountry" className="block mt-4 lg:mt-0 lg:ml-2">
              <span className="text-sm text-[#00000050]  dark:text-[#FFFFFF30]">
                To Country{" "}
              </span>
              <CountrySelector
                open={isToCountryOpen}
                onToggle={() => setIsToCountryOpen(!isToCountryOpen)}
                COUNTRIES={countries}
                loading={loadingCountries}
                id="fromCountry"
                useAll={false}
                onChange={(id) => setToCountry(id)}
                defaultSelectedKeys={
                  params.destinationCountry
                    ? countries.find(
                      (option) => option.id === params.destinationCountry
                    ) : null
                }
                selectedValue={
                  toCountry
                    ? countries.find((option) => option.id === toCountry)
                    : null
                }
              />
            </label>
          </div>
        </div>
        <div className="flex flex-col lg:flex-row my-2 ">
          <div className="w-full lg:w-1/2">
            <label htmlFor="#Status" className="block mr-2">
              <span className="text-sm text-[#00000050]  dark:text-[#FFFFFF30]">
                Status
              </span>
              <GeneralSelector
                id="Status"
                placeholder="Select a status"
                open={isStatusListOpen}
                onToggle={() => setIsStatusListOpen(!isStatusListOpen)}
                onChange={(val) => {
                  setSelectedStatus(StatusList.find((opt) => opt[1] === val));
                }}
                selectedValue={selectedStatus ? selectedStatus[1] : ""}
                options={StatusList.map((p) => p[1])}
                loading={loadingOrderStatus}
                useAll={false}
                defaultSelectedKeys={
                  params.status
                    ? StatusList.length > 0
                      ? StatusList.find((s) => s[0] === params.status)?.[1]
                      : null
                    : null
                }
              />
            </label>
          </div>
          <div className="w-full lg:w-1/2">
            <label htmlFor="#Products" className="block mt-4 lg:mt-0 lg:ml-2">
              <span className="text-sm text-[#00000050]  dark:text-[#FFFFFF30]">
                Payment method
              </span>
              <GeneralSelector
                id="Payements"
                placeholder="Select a Payment method"
                open={isPayementListOpen}
                onToggle={() => setIsPayementListOpen(!isPayementListOpen)}
                loading={loadingOrderPayements}
                useAll={false}
                onChange={(val) => {
                  const key = Object.keys(PaymentsList).find(
                    (k) => PaymentsList[k] === val
                  );
                  setSelectedPayment({ key, label: val });
                }}
                selectedValue={selectedPayment ? selectedPayment.label : ""}
                options={Object.values(PaymentsList)}
                defaultSelectedKeys={
                  params.paymentMethod
                    ? Object.keys(PaymentsList).length > 0
                      ? PaymentsList[params.paymentMethod]
                      : null
                    : null
                }
              />
            </label>
          </div>
        </div>
        <div className="flex flex-col lg:flex-row my-2 ">
          <div
            ref={datePickerRef}
            className=" cursor-pointer w-full flex justify-center items-center lg:w-[80%]">
            <I18nProvider locale="en-GB">
              <DateRangePicker
                onClick={(e) => {
                  // Prevent event from bubbling up to parent elements
                  e.stopPropagation();
                }}
                calendarProps={{
                  classNames: {
                    base: "bg-background",
                    headerWrapper: "pt-4 bg-background",
                    prevButton: "border-1 border-default-200 rounded-small",
                    nextButton: "border-1 border-default-200 rounded-small",
                    gridHeader:
                      "bg-background shadow-none border-b-1 border-default-100",
                    cellButton: [
                      "data-[today=true]:bg-default-100 data-[selected=true]:bg-transparent rounded-small",
                      // start (pseudo)
                      "data-[range-start=true]:before:rounded-l-small",
                      "data-[selection-start=true]:before:rounded-l-small",
                      // end (pseudo)
                      "data-[range-end=true]:before:rounded-r-small",
                      "data-[selection-end=true]:before:rounded-r-small",
                      // start (selected)
                      "data-[selected=true]:data-[selection-start=true]:data-[range-selection=true]:rounded-small",
                      // end (selected)
                      "data-[selected=true]:data-[selection-end=true]:data-[range-selection=true]:rounded-small",
                    ],
                  },
                  onPress: (e) => {
                    // Prevent calendar button clicks from bubbling up
                    e.stopPropagation();
                  },
                }}
                isInvalid={exportError}
                firstDayOfWeek="mon"
                errorMessage={exportError}
                classNames={{
                  label: ["!text-[#00000050] dark:!text-[#FFFFFF30] "],
                  selectorButton: "justify-center",
                  input: "hidden",
                  separator: "hidden",
                  innerWrapper: "cursor-pointer",
                }}
                CalendarBottomContent={
                  <RadioGroup
                    value={sortedBy}
                    onValueChange={setSortedBy}
                    aria-label="Sort By"
                    label="Sort By"
                    onClick={(e) => e.stopPropagation()}
                    classNames={{
                      base: "w-full pb-2 radiogroup",
                      label: "px-3",
                      wrapper:
                        "-my-2.5 py-2.5 px-3 gap-1 flex-nowrap max-w-[w-[calc(var(--visible-months)_*_var(--calendar-width))]] overflow-scroll hide-scrollbar",
                    }}
                    defaultValue="leadDate"
                    orientation="horizontal">
                    <CustomRadio
                      value="leadDate"
                      onClick={(e) => e.stopPropagation()}>
                      Lead Date
                    </CustomRadio>
                    <CustomRadio
                      value="shipDate"
                      onClick={(e) => e.stopPropagation()}>
                      Ship Date
                    </CustomRadio>
                    <CustomRadio
                      value="statusDate"
                      onClick={(e) => e.stopPropagation()}>
                      Status Date
                    </CustomRadio>
                    <CustomRadio
                      value="followup"
                      onClick={(e) => e.stopPropagation()}>
                      Follow Up
                    </CustomRadio>
                  </RadioGroup>
                }
                CalendarTopContent={
                  <ButtonGroup
                    fullWidth
                    className="px-3 pb-2 pt-3 bg-content1 [&>button]:text-default-500 [&>button]:border-default-200/60"
                    radius="full"
                    size="sm"
                    variant="bordered">
                    <Button
                      onClick={() => {
                        setDateRange(lastMonth);
                      }}>
                      Last Month
                    </Button>
                    <Button
                      onClick={() => {
                        setDateRange(lastWeek);
                      }}>
                      Last week
                    </Button>
                    <Button
                      onClick={() => {
                        setDateRange(thisWeek);
                      }}>
                      This Week
                    </Button>
                  </ButtonGroup>
                }
                startContent={
                  <div
                    className="w-full flex justify-start items-center gap-1"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenCalendar();
                    }}>
                    {!(!dateRange.end || !dateRange.start) && (
                      <div
                        onClick={(e) => {
                          e.stopPropagation();
                          setDateRange({
                            start: null,
                            end: null,
                          });
                        }}
                        className="flex justify-center items-center p-1 bg-transparent cursor-pointer">
                        <Cancel01Icon size={16} />
                      </div>
                    )}
                    <span
                      className={` ${!dateRange.end || !dateRange.start
                        ? "text-sm text-[#00000050] dark:text-[#FFFFFF30]"
                        : "text-medium text-black dark:text-white"
                        }`}>
                      {formatDate(dateRange)}
                    </span>
                  </div>
                }
                className="flex-grow w-full "
                value={dateRange}
                onChange={setDateRange}
                color="primary"
                label="Date"
                variant="underlined"
              />
            </I18nProvider>
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default OrderFilterModal;
