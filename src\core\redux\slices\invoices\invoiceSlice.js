import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { invoicesStatusUrl, MainDomain, serviceInvoicesListUrl, sourcingInvoicesDetailsUrl, sourcingInvoicesListUrl } from "../URLs";
import { getToken } from "../../../services/TokenHandler";
import axios from "axios";


const initialState = {
    serviceInvoices: [],
    sourcingInvoices: [],
    invoiceStatus: [],
    invoiceServiceCount: 0,
    invoiceSourcingCount: 0,
    params: {
        startDate: null,
        endDate: null,
        status: null,
        invoiceNum: null,
        count: 0,
        page: 1,
        sortBy: null,
    },
    paramsCount: 0,
    loading: false,
    loadingStatus: false,
    loadingServiceCount: false,
    loadingSourcingCount: false,
    error: null,
};


export const updateParams = createAsyncThunk(
    "invoices/updateParams",
    async (newParams, { getState, rejectWithValue }) => {
        try {
            Object.keys(newParams).forEach(key => {
                if (newParams[key] === '') {
                    newParams[key] = null;
                }
            });
            const { params } = getState().invoices;
            if (!newParams.hasOwnProperty('page')) {
                newParams.page = 1;
            }
            return { ...params, ...newParams };
        } catch (error) {
            return rejectWithValue("Failed to update params");
        }
    }
);

export const resetParams = createAsyncThunk(
    "invoices/resetParams",
    async (_, { getState, rejectWithValue }) => {
        try {
            const { params } = getState().invoices;
            const resetParams = Object.keys(params).reduce((acc, key) => {
                acc[key] = null;
                return acc;
            }, {});
            return resetParams;
        } catch (error) {
            return rejectWithValue("Failed to reset params");
        }
    }
);


// Async thunk for installing Shopify app
export const getInvoicesStatus = createAsyncThunk(
    "invoicesStatus/get",
    async (_, { rejectWithValue }) => {
        try {
            const response = await axios.get(invoicesStatusUrl,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }

            return response.data.result;


        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message })
        };
    }

);


// Async thunk for installing Shopify app
export const getServiceInvoice = createAsyncThunk(
    "serviceInvoices/get",
    async (_, { getState, rejectWithValue }) => {
        try {

            const { params } = getState().invoices;
            let query = `${serviceInvoicesListUrl}?page=${params.page}`;
            params.startDate && (query += `&startDate=${params.startDate}`);//in yyyy-mm-dd format
            params.endDate && (query += `&endDate=${params.endDate}`);//in yyyy-mm-dd format
            params.status && (query += `&status=${params.status}`);
            params.invoiceNum && (query += `&invoiceNum=${params.invoiceNum}`);
            params.sortBy && (query += `&sortBy=${params.sortBy}`);


            const response = await axios.get(query,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }

            return response.data;


        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message })
        };
    }
);

// Async thunk for installing Shopify app
export const getServiceInvoiceCount = createAsyncThunk(
    "serviceInvoicesCount/get",
    async (_, { rejectWithValue }) => {
        try {

            const response = await axios.get(`${serviceInvoicesListUrl}?count=1`,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }


            return response.data.result;


        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message })
        };
    }
);
// Async thunk for installing Shopify app
export const getSourcingInvoiceCount = createAsyncThunk(
    "sourcingInvoicesCount/get",
    async (_, { rejectWithValue }) => {
        try {

            const response = await axios.get(`${sourcingInvoicesListUrl}?count=1`,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }


            return response.data.result;


        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message })
        };
    }
);

// Async thunk for installing Shopify app
export const getSourcingInvoice = createAsyncThunk(
    "sourcingInvoices/get",
    async (_, { getState, rejectWithValue }) => {
        try {

            const { params } = getState().invoices;
            let query = `${sourcingInvoicesListUrl}?page=${params.page}`;
            params.startDate && (query += `&startDate=${params.startDate}`);//in yyyy-mm-dd format
            params.endDate && (query += `&endDate=${params.endDate}`);//in yyyy-mm-dd format
            params.status && (query += `&status=${params.status}`);
            params.invoiceNum && (query += `&invoiceNum=${params.invoiceNum}`);
            params.sortBy && (query += `&sortBy=${params.sortBy}`);


            const response = await axios.get(query,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }

            console.log(response.data);

            return response.data;


        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message })
        };
    }
);

// Async thunk for installing Shopify app
export const getSourcingInvoiceDetails = createAsyncThunk(
    "getSourcingInvoiceDetails/get",
    async (id, { rejectWithValue }) => {
        try {

            let query = `${sourcingInvoicesDetailsUrl}${id}`;


            const response = await axios.get(query,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }


            return response.data.result;


        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message })
        };
    }
);

// Async thunk for installing Shopify app
export const getServiceInvoiceStatusCount = createAsyncThunk(
    "serviceInvoiceStatusCount/get",
    async ({ id, status }, { rejectWithValue }) => {
        try {

            const response = await axios.get(`${MainDomain}invoices/${id}/fees?type=${status}&count=1`,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }


            return response.data.result;


        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message })
        };
    }
);
// Async thunk for installing Shopify app
export const getServiceInvoiceDetails = createAsyncThunk(
    "getServiceInvoiceDetails/get",
    async (id, { rejectWithValue }) => {
        try {

            const response = await axios.get(`${MainDomain}invoices/${id}`,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }


            return response.data.result;


        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message })
        };
    }
);
// Async thunk for installing Shopify app
export const getServiceInvoiceStatusDetails = createAsyncThunk(
    "getServiceInvoiceStatusDetails/get",
    async ({ id, status }, { rejectWithValue }) => {
        try {

            const response = await axios.get(`${MainDomain}invoices/${id}/fees?type=${status}`,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }


            return response.data.result.feesDetails;


        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message })
        };
    }
);

const invoiceSlice = createSlice({
    name: 'invoices',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(updateParams.pending, (state) => {
                state.paramsLoading = true;
                state.error = null;
            })
            .addCase(updateParams.fulfilled, (state, action) => {
                state.paramsLoading = false;
                state.params = action.payload;
                // Calculate paramsCount by counting non-null params (excluding page)
                state.paramsCount = Object.keys(state.params).filter(key => key !== 'page' && state.params[key] !== null).length;
            })
            .addCase(updateParams.rejected, (state, action) => {
                state.paramsLoading = false;
                state.error = action.payload;
            })
            .addCase(resetParams.pending, (state) => {
                state.paramsLoading = true;
                state.error = null;
            })
            .addCase(resetParams.fulfilled, (state, action) => {
                state.paramsLoading = false;
                state.params = action.payload;
                // Reset paramsCount to 0
                state.paramsCount = 0;
            })
            .addCase(resetParams.rejected, (state, action) => {
                state.paramsLoading = false;
                state.error = action.payload;
            })
            .addCase(getServiceInvoice.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getServiceInvoice.fulfilled, (state, action) => {
                state.loading = false;
                state.serviceInvoices = action.payload;
            })
            .addCase(getServiceInvoice.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(getSourcingInvoice.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSourcingInvoice.fulfilled, (state, action) => {
                state.loading = false;
                state.sourcingInvoices = action.payload;
            })
            .addCase(getSourcingInvoice.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(getInvoicesStatus.pending, (state) => {
                state.loadingStatus = true;
                state.error = null;
            })
            .addCase(getInvoicesStatus.fulfilled, (state, action) => {
                state.loadingStatus = false;
                state.invoiceStatus = action.payload;
            })
            .addCase(getInvoicesStatus.rejected, (state, action) => {
                state.loadingStatus = false;
                state.error = action.payload;
            })
            .addCase(getServiceInvoiceCount.pending, (state) => {
                state.loadingServiceCount = true;
                state.error = null;
            })
            .addCase(getServiceInvoiceCount.fulfilled, (state, action) => {
                state.loadingServiceCount = false;
                state.invoiceServiceCount = action.payload || 0;

            })
            .addCase(getServiceInvoiceCount.rejected, (state, action) => {
                state.loadingServiceCount = false;
                state.error = action.payload;
            })
            .addCase(getSourcingInvoiceCount.pending, (state) => {
                state.loadingSourcingCount = true;
                state.error = null;
            })
            .addCase(getSourcingInvoiceCount.fulfilled, (state, action) => {
                state.loadingSourcingCount = false;
                state.invoiceSourcingCount = action.payload || 0;
            })
            .addCase(getSourcingInvoiceCount.rejected, (state, action) => {
                state.loadingSourcingCount = false;
                state.error = action.payload;
            })

    },
});

export default invoiceSlice.reducer;