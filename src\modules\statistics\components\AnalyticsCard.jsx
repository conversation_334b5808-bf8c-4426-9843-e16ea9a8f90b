import { But<PERSON> } from "@heroui/button";
import { MoreVerticalCircle01Icon } from "hugeicons-react";

const AnalyticsCard = ({
    icon: Icon,
    iconBg,
    hoverColor,
    sales,
    title,
    isExpanded
}) => {
    return (
        <div className={`w-full ${isExpanded ? '' : 'col-span-1 h-36'}`}>
            <div
                className={`group flex flex-col justify-between border border-black/10 dark:border-white/10 bg-white dark:bg-transparent hover:bg-[#0057FF]/20 ${hoverColor} px-2 lg:px-4 py-4 rounded-2xl shadow-sm m-1 `}>
                <div className="flex flex-row justify-between items-center gap-2">


                    <div className="flex flex-row justify-start items-start gap-2">
                        <span
                            className={`w-6 h-6 p-[4px] md:w-8 md:h-8  flex flex-row items-center justify-center 
                            rounded-full text-white ${iconBg}`}> <Icon /></span>
                        <p className="text-[10px] text-sm dark:text-gray-200 inline-block">{title}</p>
                    </div>
                    <div>
                        <Button isIconOnly variant="light">
                            <MoreVerticalCircle01Icon size={18} />
                        </Button>
                    </div>
                </div>
                <div className="flex flex-row justify-between items-center mt-3">
                    <div>
                        <span className="font-bold">$ {sales}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AnalyticsCard;
