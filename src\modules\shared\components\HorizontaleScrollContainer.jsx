import {useEffect, useRef} from "react";

const HorizontalScrollContainer = ({children}) => {
    const containerRef = useRef(null);

    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        let isScrolling = false;
        let startX;
        let scrollLeft;

        const onTouchStart = (e) => {
            isScrolling = true;
            startX = e.touches[0].pageX - container.offsetLeft;
            scrollLeft = container.scrollLeft;

            // Prevent browser navigation
            if (container.scrollWidth > container.clientWidth) {
                e.preventDefault();
            }
        };

        const onTouchMove = (e) => {
            if (!isScrolling) return;

            const x = e.touches[0].pageX - container.offsetLeft;
            const distance = x - startX;
            container.scrollLeft = scrollLeft - distance;

            // Prevent browser navigation while scrolling
            if (container.scrollWidth > container.clientWidth) {
                e.preventDefault();
            }
        };

        const onTouchEnd = () => {
            isScrolling = false;
        };

        // Add touch event listeners
        container.addEventListener('touchstart', onTouchStart, {passive: false});
        container.addEventListener('touchmove', onTouchMove, {passive: false});
        container.addEventListener('touchend', onTouchEnd);

        // Cleanup
        return () => {
            container.removeEventListener('touchstart', onTouchStart);
            container.removeEventListener('touchmove', onTouchMove);
            container.removeEventListener('touchend', onTouchEnd);
        };
    }, []);

    return (
        <div
            ref={containerRef}
            className="overflow-x-auto whitespace-nowrap touch-pan-x"
            style={{WebkitOverflowScrolling: 'touch'}}
        >
            {children}
        </div>
    );
};

export default HorizontalScrollContainer;