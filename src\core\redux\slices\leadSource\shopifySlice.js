import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { getToken } from "../../../services/TokenHandler";
import { shopifyInstallUrl } from "../URLs";
import { toast } from "sonner";


// Async thunk for installing Shopify app
export const installShopifyApp = createAsyncThunk(
    "shopify/install",
    async ({ shop }, { rejectWithValue }) => {  // token is passed as argument

        if (!shop || shop.trim() === "") {
            return rejectWithValue("Shop parameter is required and cannot be empty.");
        }
        try {
            const response = await axios.get(shopifyInstallUrl,
                {
                    params: {
                        shop: shop,
                        json: 1,
                    },
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error installing shopify' });

            }
            return response.data.result;

        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }



    }
);


const shopifySlice = createSlice({
    name: "shopify",
    initialState: {
        loading: false,
        error: null,
        installUrl: null,
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(installShopifyApp.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(installShopifyApp.fulfilled, (state, action) => {
                state.loading = false;
                state.installUrl = action.payload;
            })
            .addCase(installShopifyApp.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload; // Error message from Laravel backend
            });
    },
});

export default shopifySlice.reducer;
