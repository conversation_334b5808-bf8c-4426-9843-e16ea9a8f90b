import React, { useEffect, useMemo, useRef, useState } from "react";
import CustomModal from "../../shared/components/CustomModal";
import GeneralSelector from "../../shared/components/GeneralSelector";
import axios from "axios";
import { getToken } from "../../../core/services/TokenHandler";
import moment from "moment";
import {
  parseDate,
  endOfMonth,
  endOfWeek,
  getLocalTimeZone,
  startOfMonth,
  startOfWeek,
  today,
} from "@internationalized/date";
import { Cancel01Icon, Delete02Icon, FilterIcon } from "hugeicons-react";
import {
  orderPayementsUrl,
  productListUrl,
} from "../../../core/redux/slices/URLs";
import { Button, ButtonGroup } from "@heroui/button";
import { I18nProvider, useLocale } from "@react-aria/i18n";
import { cn, DateRangePicker, Radio, RadioGroup } from "@heroui/react";
import { useDispatch, useSelector } from "react-redux";
import {
  updateFilterParams,
  resetFilterParams,
} from "../../../core/redux/slices/dachboard/dashboardSlice";
import { debounce } from "lodash";

const DashboardFilterModal = ({ isOpen, onClose }) => {
  const dispatch = useDispatch();
  const { params } = useSelector((state) => state.dashboard);

  // State for date range picker
  const [dateRange, setDateRange] = useState({
    start: null,
    end: null,
  });
  let { locale } = useLocale();

  // Format the date range
  const formatDate = (dateObj) => {
    if (!dateObj) return ""; // Return empty string if dateObj is null or undefined
    if (!dateObj.start || !dateObj.end)
      return `${moment().startOf("week").format("DD/MM/YYYY")} - ${moment()
        .endOf("week")
        .format("DD/MM/YYYY")}`;
    const formattedStart = dateObj?.start
      ? moment(dateObj.start.toDate()).format("DD/MM/YYYY")
      : "";
    const formattedEnd = dateObj?.end
      ? moment(dateObj.end.toDate()).format("DD/MM/YYYY")
      : "";

    return `${formattedStart} - ${formattedEnd}`;
  };

  // Date presets
  let now = today(getLocalTimeZone());
  let lastWeek = {
    start: startOfWeek(now.subtract({ weeks: 1 }), locale),
    end: endOfWeek(now.subtract({ weeks: 1 }), locale),
  };
  let lastMonth = {
    start: startOfMonth(now.subtract({ months: 1 }), locale),
    end: endOfMonth(now.subtract({ months: 1 }), locale),
  };
  let thisWeek = {
    start: startOfMonth(now, locale),
    end: endOfMonth(now, locale),
  };

  // Custom Radio component for date range selection
  const CustomRadio = (props) => {
    const { children, ...otherProps } = props;

    const handleClick = (e) => {
      const target = e.currentTarget; // safer than e.target
      const parent = target.closest(".overflow-scroll"); // Find the parent container

      if (parent) {
        const parentWidth = parent.offsetWidth;
        const itemWidth = target.offsetWidth;
        const itemLeft = target.offsetLeft;
        const scrollPosition = itemLeft - parentWidth / 2 + itemWidth / 2;
        parent.scrollTo({
          left: scrollPosition,
          behavior: "smooth",
        });
      }
    };

    return (
      <Radio
        {...otherProps}
        onClick={handleClick}
        classNames={{
          base: cn(
            "flex-none m-0 h-8 bg-content1 hover:bg-content2 items-center justify-between",
            "cursor-pointer rounded-full border-2 border-default-200/60",
            "data-[selected=true]:border-primary"
          ),
          label: "text-tiny text-default-500",
          labelWrapper: "px-1 m-0",
          wrapper: "hidden",
        }}>
        {children}
      </Radio>
    );
  };

  const datePickerRef = useRef(null);

  const handleOpenCalendar = () => {
    if (datePickerRef.current) {
      // Look for the internal selector button
      const button = datePickerRef.current.querySelector(
        'button[data-slot="selector-button"]'
      );
      if (button) button.click();
    }
  };

  // State for product selector
  const [loadingProductList, setLoadingProductList] = useState(false);
  const [loadingPaymentMethods, setLoadingPaymentMethods] = useState(false);
  const [ProductList, setProductList] = useState([]);
  const [PaymentsList, setPaymentsList] = useState([]);
  const [isProductListOpen, setIsProductListOpen] = useState(false);
  const [isPaymentListOpen, setIsPaymentListOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [pendingProductName, setPendingProductName] = useState("Loading...");
  const [sortedBy, setSortedBy] = useState("");

  // Pagination for product list
  const [ProdPage, setProdPage] = useState(1);
  const [prodKey, setProdKey] = useState("");

  // Create a debounced function to update prodKey using useMemo to ensure it's stable
  const debouncedSetProdKey = useMemo(
    () =>
      debounce((value) => {
        setProdKey(value);
      }, 500), // 500ms delay
    []
  ); // Empty dependency array ensures this is only created once

  // Cleanup debounce on component unmount to avoid memory leaks
  useEffect(() => {
    return () => {
      if (debouncedSetProdKey && debouncedSetProdKey.cancel) {
        debouncedSetProdKey.cancel();
      }
    };
  }, [debouncedSetProdKey]);

  // Fetch product list
  useEffect(() => {
    const fetchProductList = async () => {
      // Only show loading indicator for the first page
      ProdPage === 1 && setLoadingProductList(true);
      try {
        if (prodKey !== "") {
          setProdPage(1);
        }
        const response = await axios.get(
          `${productListUrl}?page=${ProdPage}&keyword=${prodKey}`,
          {
            headers: {
              Authorization: `Bearer ${getToken()}`,
            },
          }
        );

        if (response.data.response !== "success") {
          console.error(response.data.message || "Error fetching products");
          return;
        }

        // If it's the first page or we're searching, replace the list
        // Otherwise, append the new results to the existing list
        if (ProdPage === 1 || prodKey) {
          setProductList(response.data.result);
        } else {
          setProductList((prevList) => [...prevList, ...response.data.result]);
        }
      } catch (error) {
        console.error(error.response?.data?.message || error.message);
      } finally {
        // Only update loading state for the first page
        ProdPage === 1 && setLoadingProductList(false);
      }
    };

    fetchProductList();
  }, [ProdPage, prodKey]);

  // Fetch payment methods
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      setLoadingPaymentMethods(true);
      try {
        const response = await axios.get(`${orderPayementsUrl}`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (response.data.response !== "success") {
          console.error(
            response.data.message || "Error fetching payment methods"
          );
          return;
        }
        setPaymentsList(response.data.result);
      } catch (error) {
        console.error(error.response?.data?.message || error.message);
      } finally {
        setLoadingPaymentMethods(false);
      }
    };

    fetchPaymentMethods();
  }, []);

  // Clear filter function
  const clearFilter = () => {
    setDateRange({
      start: null,
      end: null,
    });
    setSelectedProduct(null);
    setSelectedPayment(null);
    setSortedBy(new Set());
    setIsProductListOpen(false);
    setIsPaymentListOpen(false);
    setProdKey("");
    setProdPage(1);
    setPendingProductName("");

    // Reset filter params in Redux
    dispatch(resetFilterParams());

    onClose();
  };

  // Apply filter function
  const handleApplyFilter = () => {
    // Create filter object from selected values
    const filter = {
      startDate:
        dateRange && dateRange.start
          ? moment(dateRange.start.toDate()).format("YYYY-MM-DD")
          : null,
      endDate:
        dateRange && dateRange.end
          ? moment(dateRange.end.toDate()).format("YYYY-MM-DD")
          : null,
      productReference: selectedProduct?.id || null,
      paymentMethod: selectedPayment?.key || null,
      sortBy: Array.from(sortedBy)[0] || null,
    };

    console.log("Applying dashboard filters:", filter);

    // Update filter params in Redux
    dispatch(updateFilterParams(filter));

    // // Fetch filtered data sequentially
    // const fetchDataSequentially = async () => {
    //     try {
    //         await dispatch(fetchShippingData(filter)).unwrap();
    //         await dispatch(fetchCallCenterData(filter)).unwrap();
    //         await dispatch(fetchFollwupData(filter)).unwrap();
    //     } catch (error) {
    //         console.error("Error fetching filtered data:", error);
    //     }
    // };

    // fetchDataSequentially();
    onClose();
  };

  // Initialize form with current filter params when modal opens
  useEffect(() => {
    if (isOpen && params) {
      // Initialize date range if params has startDate and endDate
      if (params.startDate && params.endDate) {
        setDateRange({
          start: parseDate(params.startDate),
          end: parseDate(params.endDate),
        });
      }

      // Initialize sortedBy if params has sortBy
      if (params.sortBy) {
        setSortedBy(new Set([params.sortBy]));
      }

      // Initialize selected payment if params has paymentMethod
      if (params.paymentMethod && PaymentsList) {
        const paymentLabel = PaymentsList[params.paymentMethod];
        if (paymentLabel) {
          setSelectedPayment({
            key: params.paymentMethod,
            label: paymentLabel,
          });
        }
      }
    } else if (!isOpen) {
      // Reset state when modal is closed
      setDateRange({
        start: null,
        end: null,
      });
      setSelectedProduct(null);
      setSelectedPayment(null);
      setSortedBy(new Set());
      setIsProductListOpen(false);
      setIsPaymentListOpen(false);
      setPendingProductName("");
      setProdKey("");
      setProdPage(1);
      setProductList([]);

      // Cancel any pending debounced calls
      if (debouncedSetProdKey && debouncedSetProdKey.cancel) {
        debouncedSetProdKey.cancel();
      }
    }
  }, [isOpen, params, PaymentsList, debouncedSetProdKey]);

  // Separate useEffect for handling product selection based on ProductList changes
  useEffect(() => {
    if (isOpen && params && params.productReference) {
      if (ProductList.length > 0) {
        const product = ProductList.find(
          (p) => p.id === params.productReference
        );
        if (product) {
          setSelectedProduct(product);
        } else if (!pendingProductName) {
          // If product not in list and we haven't already fetched its name, fetch it
          const fetchProductName = async () => {
            try {
              const response = await axios.get(
                `${productListUrl}/${params.productReference}`,
                {
                  headers: {
                    Authorization: `Bearer ${getToken()}`,
                  },
                }
              );

              if (
                response.data.response === "success" &&
                response.data.result
              ) {
                setPendingProductName(response.data.result.name || "Product");
              }
            } catch (error) {
              console.error("Error fetching product name:", error);
            }
          };

          fetchProductName();
        }
      } else if (!pendingProductName) {
        // If ProductList is empty and we haven't already fetched the product name, fetch it
        const fetchProductName = async () => {
          try {
            const response = await axios.get(
              `${productListUrl}/${params.productReference}`,
              {
                headers: {
                  Authorization: `Bearer ${getToken()}`,
                },
              }
            );

            if (response.data.response === "success" && response.data.result) {
              setPendingProductName(response.data.result.name || "Product");
            }
          } catch (error) {
            console.error("Error fetching product name:", error);
          }
        };

        fetchProductName();
      }
    }
  }, [isOpen, params?.productReference, ProductList, pendingProductName]);

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={() => {
        onClose();
      }}
      closeOnClickOutside={false}
      title="Dashboard Filter"
      position="top-32"
      footerContent={
        <div className="flex flex-row gap-2 flex-1 justify-center md:justify-end">
          <div className="flex flex-row justify-end flex-1 items-center gap-4">
            <Button
              className="rounded-full bg-glb_blue text-white text-xs p-2 md:text-base md:p-4"
              onClick={() => handleApplyFilter()}>
              <FilterIcon size={16} /> Apply Filter
            </Button>
            <Button
              className="rounded-full bg-glb_red text-white text-xs p-2 md:text-base md:p-4"
              onClick={() => clearFilter()}>
              <Delete02Icon size={16} /> Clear All
            </Button>
          </div>
        </div>
      }>
      <div>
        <div className="flex flex-col lg:flex-row gap-2">
          <div className="w-full lg:w-1/2">
            <label htmlFor="#Products" className="block mr-2">
              <span className="text-sm text-[#00000050] dark:text-[#FFFFFF30]">
                Product
              </span>
              <GeneralSelector
                id="Products"
                placeholder="Select a product"
                open={isProductListOpen}
                useAll={false}
                onToggle={() => setIsProductListOpen(!isProductListOpen)}
                onChange={(val) => {
                  setSelectedProduct(
                    ProductList.find((opt) => opt.name === val)
                  );
                  setProdKey("");
                }}
                onSearchChange={(val) => {
                  // Use the debounced function instead of directly setting prodKey
                  if (debouncedSetProdKey) {
                    debouncedSetProdKey(val);
                  } else {
                    // Fallback in case debounce is not available
                    setProdKey(val);
                  }
                }}
                onEndScroll={() => {
                  // Return a promise that loads the next page of products
                  return new Promise((resolve, reject) => {
                    // Check if we have more products to load
                    if (ProductList.length === 0) {
                      reject("No more products to load");
                      return;
                    }

                    // Increment the page number to trigger the useEffect
                    setProdPage((prevPage) => prevPage + 1);

                    // Wait for the products to load (simulate API delay)
                    setTimeout(() => {
                      resolve();
                    }, 1000);
                  });
                }}
                selectedValue={
                  selectedProduct
                    ? selectedProduct.name
                    : params?.productReference && pendingProductName
                      ? pendingProductName
                      : ""
                }
                options={ProductList.map((p) => p.name)}
                loading={loadingProductList}
              />
            </label>
          </div>
          <div className="w-full lg:w-1/2">
            <label htmlFor="#Payments" className="block mr-2">
              <span className="text-sm text-[#00000050] dark:text-[#FFFFFF30]">
                Payment Method
              </span>
              <GeneralSelector
                id="Payments"
                placeholder="Select a payment method"
                useAll={false}
                open={isPaymentListOpen}
                onToggle={() => setIsPaymentListOpen(!isPaymentListOpen)}
                loading={loadingPaymentMethods}
                onChange={(val) => {
                  const key = Object.keys(PaymentsList).find(
                    (k) => PaymentsList[k] === val
                  );
                  setSelectedPayment({ key, label: val });
                }}
                selectedValue={selectedPayment ? selectedPayment.label : ""}
                options={Object.values(PaymentsList)}
              />
            </label>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row my-2">
          <div
            ref={datePickerRef}
            className="cursor-pointer w-full flex justify-center items-center lg:w-[80%]">
            <I18nProvider locale="en-GB">
              <DateRangePicker
                onClick={(e) => {
                  // Prevent event from bubbling up to parent elements
                  e.stopPropagation();
                }}
                calendarProps={{
                  classNames: {
                    base: "bg-background",
                    headerWrapper: "pt-4 bg-background",
                    prevButton: "border-1 border-default-200 rounded-small",
                    nextButton: "border-1 border-default-200 rounded-small",
                    gridHeader:
                      "bg-background shadow-none border-b-1 border-default-100",
                    cellButton: [
                      "data-[today=true]:bg-default-100 data-[selected=true]:bg-transparent rounded-small",
                      // start (pseudo)
                      "data-[range-start=true]:before:rounded-l-small",
                      "data-[selection-start=true]:before:rounded-l-small",
                      // end (pseudo)
                      "data-[range-end=true]:before:rounded-r-small",
                      "data-[selection-end=true]:before:rounded-r-small",
                      // start (selected)
                      "data-[selected=true]:data-[selection-start=true]:data-[range-selection=true]:rounded-small",
                      // end (selected)
                      "data-[selected=true]:data-[selection-end=true]:data-[range-selection=true]:rounded-small",
                    ],
                  },
                  onPress: (e) => {
                    // Prevent calendar button clicks from bubbling up
                    e.stopPropagation();
                  },
                }}
                firstDayOfWeek="mon"
                classNames={{
                  label: ["!text-[#00000050] dark:!text-[#FFFFFF30] "],
                  selectorButton: "justify-center",
                  input: "hidden",
                  separator: "hidden",
                  innerWrapper: "cursor-pointer",
                }}
                CalendarBottomContent={
                  <RadioGroup
                    value={sortedBy}
                    onValueChange={setSortedBy}
                    aria-label="Sort By"
                    label="Sort By"
                    onClick={(e) => e.stopPropagation()}
                    classNames={{
                      base: "w-full pb-2 radiogroup",
                      label: "px-3",
                      wrapper:
                        "-my-2.5 py-2.5 px-3 gap-1 flex-nowrap max-w-[w-[calc(var(--visible-months)_*_var(--calendar-width))]] overflow-scroll hide-scrollbar",
                    }}
                    defaultValue="leadDate"
                    orientation="horizontal">
                    <CustomRadio
                      value="leadDate"
                      onClick={(e) => e.stopPropagation()}>
                      Lead Date
                    </CustomRadio>
                    <CustomRadio
                      value="shipDate"
                      onClick={(e) => e.stopPropagation()}>
                      Ship Date
                    </CustomRadio>
                    <CustomRadio
                      value="statusDate"
                      onClick={(e) => e.stopPropagation()}>
                      Status Date
                    </CustomRadio>
                    <CustomRadio
                      value="followup"
                      onClick={(e) => e.stopPropagation()}>
                      Follow Up
                    </CustomRadio>
                  </RadioGroup>
                }
                CalendarTopContent={
                  <ButtonGroup
                    fullWidth
                    className="px-3 pb-2 pt-3 bg-content1 [&>button]:text-default-500 [&>button]:border-default-200/60"
                    radius="full"
                    size="sm"
                    variant="bordered">
                    <Button
                      onClick={() => {
                        setDateRange(lastMonth);
                      }}>
                      Last Month
                    </Button>
                    <Button
                      onClick={() => {
                        setDateRange(lastWeek);
                      }}>
                      Last week
                    </Button>
                    <Button
                      onClick={() => {
                        setDateRange(thisWeek);
                      }}>
                      This Week
                    </Button>
                  </ButtonGroup>
                }
                startContent={
                  <div
                    className="w-full flex justify-start items-center gap-1"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenCalendar();
                    }}>
                    {!(!dateRange.end || !dateRange.start) && (
                      <div
                        onClick={(e) => {
                          e.stopPropagation();
                          setDateRange({
                            start: null,
                            end: null,
                          });
                        }}
                        className="flex justify-center items-center p-1 bg-transparent cursor-pointer">
                        <Cancel01Icon size={16} />
                      </div>
                    )}
                    <span
                      className={` ${!dateRange.end || !dateRange.start
                        ? "text-sm text-[#00000050] dark:text-[#FFFFFF30]"
                        : "text-medium text-black dark:text-white"
                        }`}>
                      {formatDate(dateRange)}
                    </span>
                  </div>
                }
                className="flex-grow w-full "
                value={dateRange}
                onChange={setDateRange}
                color="primary"
                label="Date"
                variant="underlined"
              />
            </I18nProvider>
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default DashboardFilterModal;
