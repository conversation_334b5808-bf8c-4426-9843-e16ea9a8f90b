import React, { useState } from 'react'
import { motion, useMotionValue, useTransform } from 'framer-motion'

export default function AnimatedWhiteLogo() {
    const [isChecked, setIsChecked] = useState(true);
    const pathLength = useMotionValue(0);
    const opacity = useTransform(pathLength, [0.05, 0.15], [0, 1]);

    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="150" height="150">

            <motion.path stroke="#39e"
                strokeLinecap="round"
                animate={{ pathLength: 0.9 }}
                style={{ pathLength: pathLength, opacity: opacity }} d="M22.2664 33.3988C15.9888 33.3988 11.0352 28.4624 11.2122 22.3959C11.3948 16.3295 16.6451 11.3931 22.9227 11.3931H38.0631C39.7523 7.40964 43.0966 3.82001 47.8219 0.555664H23.248C10.7899 0.555664 0.369077 10.3487 0.00954306 22.3902C-0.349991 34.4318 9.4944 44.2248 21.9468 44.2248H46.7376C42.1777 41.0575 39.1531 37.4279 37.4753 33.3874H22.2664V33.3988Z" fill="white" />
            <motion.path stroke="#39e"
                strokeLinecap="round"
                animate={{ pathLength: 0.9 }}
                style={{ pathLength: pathLength, opacity: opacity }} d="M103.055 0.561523H63.1072C50.6491 0.561523 40.2226 10.3546 39.8631 22.3961C39.5035 34.4319 49.3479 44.2307 61.8061 44.2307C74.2642 44.2307 84.685 34.4319 85.0445 22.3961C85.0845 21.0093 84.7535 18.8407 84.4852 17.3683C84.3882 16.8376 84.3597 16.6835 84.3026 16.4039C84.2912 16.3468 87.0419 15.5821 87.0419 15.5821L76.7809 12.215L71.3137 21.1691C71.8388 20.9637 74.0302 20.3074 74.0302 20.3074L74.1957 22.4018C74.0131 28.4682 68.3975 33.4047 62.1199 33.4047C55.8424 33.4047 50.8888 28.4682 51.0714 22.4018C51.254 16.3354 56.5044 11.3989 62.7762 11.3989H102.73C109.008 11.3989 113.967 16.3297 113.784 22.4018C113.602 28.4682 108.351 33.4047 102.074 33.4047H86.9506C84.4225 38.1814 81.1638 41.8109 77.1462 44.2421H101.754C114.207 44.2421 124.633 34.4433 124.993 22.4075C125.352 10.366 115.514 0.57294 103.05 0.57294" fill="white" />

        </svg>
    );
}
