/* Example: your-app/styles/dateRangeDark.css */

/* Only apply these if .dark is on a parent, e.g. body.dark or .dark-mode root */
.dark .rdrCalendarWrapper {
    background-color: #1f1f1f;   /* dark background */
    color: #f2f2f2;             /* light text */
  }
  
  .dark .rdrMonthAndYearPickers select {
    background-color: #2e2e2e;
    color: #f2f2f2;
  }
  
  .dark .rdrDayNumber span {
    color: #f2f2f2;
  }
  
  /* Adjust hovered, selected, etc. */
  .dark .rdrDayNumber span:hover {
    background-color: #444444;
  }
  
  .dark .rdrSelected,
  .dark .rdrStartEdge,
  .dark .rdrEndEdge {
    background-color: #0072F5 !important; /* or your highlight color */
  }
  
  .dark .rdrDay.today .rdrDayNumber span {
    border: 1px solid #f2f2f2;
  }
  