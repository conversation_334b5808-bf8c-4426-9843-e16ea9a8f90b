import { Input } from "@heroui/react";
import React, { useState } from "react";

const UnderlinedInput = React.forwardRef(({ label, start = false, isInvalid, endContent, ...props }, ref) => {
    return (
        <div className="flex flex-col w-full ">
            <Input
                ref={ref} // <-- forward the ref here
                label={label}
                variant="underlined"
                color={isInvalid ? 'danger' : 'primary'}
                endContent={endContent}
                classNames={{
                    label: ["!text-[#00000050] dark:!text-[#FFFFFF30] "],
                    input: [start ? "text-start" : "text-center"],
                }}
                {...props}
            />
            {isInvalid && <span className="text-[10px] text-glb_red">{isInvalid}</span>}
        </div>
    );
});

export default UnderlinedInput;

export function renderUnderlinedInput({ label, endContent, ...props }) {
    return (
        <Input
            label={label}
            variant="underlined"
            color="primary"
            endContent={endContent}
            classNames={{
                label: ["!text-[#00000050] dark:!text-[#FFFFFF30] "],
            }}
            {...props}
        />
    );
}
