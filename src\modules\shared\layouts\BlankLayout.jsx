import { Navbar, Navbar<PERSON>rand } from "@heroui/navbar";
import Brand<PERSON><PERSON> from '@/modules/shared/assets/images/logo-png.png'; //cod-power-group-logo.svg';
import codPowerGroupLogoDark from "@shared/assets/images/logo-png-dark.png"//cod-power-group-logo-dark.svg";
import { useThemeProvider } from "@/core/providers/ThemeContext.jsx";
import { Link } from "react-router-dom";
import { RouteNames } from '../../../core/routes/routes';

export default function BlankLayout({ children, showNavbar }) {

    const { currentTheme } = useThemeProvider();
    return (

        <div className='h-full overflow-y-auto'>
            <Navbar position='static' maxWidth='full' className="bg-transparent p-8">
                <NavbarBrand className={`${showNavbar ? '' : 'hidden'} w-10/12 mx-auto my-12`}>
                    <Link to={RouteNames.dashboard}>
                        <img src={currentTheme === 'light' ? BrandLogo : codPowerGroupLogoDark} className="my-12 w-36"
                            alt="Logo" />
                    </Link>
                </NavbarBrand>
            </Navbar>
            {children}
        </div>

    );
}
