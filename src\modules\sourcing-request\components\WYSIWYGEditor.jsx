import { useEffect, useRef } from 'react';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import '@shared/css/quill.css';

const WYSIWYGEditor = () => {
    const quillRef = useRef(null);
    let quill = null;

    useEffect(() => {
        if (quillRef.current) {
            if (!quill) {
                quill = new Quill(quillRef.current, {
                    modules: {
                        toolbar: [
                            [{ header: [1, 2, 3, 4, 5, 6, false] }],
                            ['bold', 'italic', 'underline', 'strike'],
                            [{ list: 'ordered' }, { list: 'bullet' }],
                            [{ color: [] }, { background: [] }],
                            ['image', 'link'],
                        ],
                    },
                    theme: 'snow',
                });

                // Handle image uploads
                quill.getModule('toolbar').addHandler('image', () => {
                    const input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', 'image/*');
                    input.click();

                    input.onchange = async () => {
                        const file = input.files[0];
                        // Upload the image to your backend server
                        const imageUrl = await uploadImageToServer(file);
                        const range = quill.getSelection(true);
                        quill.insertEmbed(range.index, 'image', imageUrl);
                    };
                });
            }
        }
    }, []);

    const uploadImageToServer = async (file) => {
        const formData = new FormData();
        formData.append('image', file);

        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
        });

        return await response.json();
    };

    return <div className="min-h-36 bg-gray-50 border-black/10 outline-none dark:border-white/10 dark:bg-white/5 w-full rounded-b-xl" ref={quillRef} />;
};

export default WYSIWYGEditor;