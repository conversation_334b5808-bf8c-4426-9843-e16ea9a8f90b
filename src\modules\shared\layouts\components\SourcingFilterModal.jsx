import { useEffect, useRef, useState } from "react";
import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import { useDispatch, useSelector } from "react-redux";
import {
  updateParams,
  resetParams,
} from "../../../../core/redux/slices/sourcing/sourcingSlice";
import CustomModal from "../../components/CustomModal";
import { FilterIcon, Delete02Icon, Cancel01Icon } from "hugeicons-react";
import GeneralSelector from "../../components/GeneralSelector";
import moment from "moment";
import {
  endOfMonth,
  endOfWeek,
  getLocalTimeZone,
  parseDate,
  startOfMonth,
  startOfWeek,
  today,
} from "@internationalized/date";
import { I18nProvider, useLocale } from "@react-aria/i18n";
import {
  cn,
  DateRangePicker,
  Radio,
  RadioGroup,
  ButtonGroup,
} from "@heroui/react";
import UnderlinedInput from "../../../settings/components/UnderlinedInput";

const SourcingFilterModal = ({ isOpen, onClose }) => {
  const dispatch = useDispatch();
  const { params } = useSelector((state) => state.sourcing);
  let { locale } = useLocale();

  // Form state - based on sourcing slice params: startDate, endDate, status, productReference, sortBy
  const [dateRange, setDateRange] = useState({
    start: null,
    end: null,
  });
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [productText, setProductText] = useState("");
  const [sortedBy, setSortedBy] = useState(new Set());

  // Dropdown states
  const [isStatusListOpen, setIsStatusListOpen] = useState(false);

  // Data states
  const [StatusList, setStatusList] = useState([]);

  // Loading states
  const [loadingStatusList, setLoadingStatusList] = useState(false);

  // Format the date range
  const formatDate = (dateObj) => {
    if (!dateObj) return "";
    if (!dateObj.start || !dateObj.end)
      return `${moment().startOf("week").format("DD/MM/YYYY")} - ${moment()
        .endOf("week")
        .format("DD/MM/YYYY")}`;
    const formattedStart = dateObj?.start
      ? moment(dateObj.start.toDate()).format("DD/MM/YYYY")
      : "";
    const formattedEnd = dateObj?.end
      ? moment(dateObj.end.toDate()).format("DD/MM/YYYY")
      : "";
    return `${formattedStart} - ${formattedEnd}`;
  };

  let now = today(getLocalTimeZone());
  let lastWeek = {
    start: startOfWeek(now.subtract({ weeks: 1 }), locale),
    end: endOfWeek(now.subtract({ weeks: 1 }), locale),
  };
  let lastMonth = {
    start: startOfMonth(now.subtract({ months: 1 }), locale),
    end: endOfMonth(now.subtract({ months: 1 }), locale),
  };
  let thisWeek = {
    start: startOfWeek(now, locale),
    end: endOfWeek(now, locale),
  };
  const datePickerRef = useRef(null);

  const handleOpenCalendar = () => {
    if (datePickerRef.current) {
      // Look for the internal selector button
      const button = datePickerRef.current.querySelector(
        'button[data-slot="selector-button"]'
      );
      if (button) button.click();
    }
  };

  // Custom Radio component
  const CustomRadio = (props) => {
    const { children, ...otherProps } = props;
    return (
      <Radio
        {...otherProps}
        classNames={{
          base: cn(
            "flex-none m-0 h-8 bg-content1 hover:bg-content2 items-center justify-between",
            "cursor-pointer rounded-full border-2 border-default-200/60",
            "data-[selected=true]:border-primary"
          ),
          label: "text-tiny text-default-500",
          labelWrapper: "px-1 m-0",
          wrapper: "hidden",
        }}>
        {children}
      </Radio>
    );
  };

  // Fetch sourcing status list (you'll need to add this URL to your URLs file)
  useEffect(() => {
    const fetchStatusList = async () => {
      setLoadingStatusList(true);
      try {
        // For now, using a mock status list - replace with actual API call
        const mockStatuses = [
          ["pending", "Pending"],
          ["in_progress", "In Progress"],
          ["completed", "Completed"],
          ["cancelled", "Cancelled"],
        ];
        setStatusList(mockStatuses);
      } catch (error) {
        console.error(error.response?.data?.message || error.message);
      } finally {
        setLoadingStatusList(false);
      }
    };
    fetchStatusList();
  }, []);

  // Initialize form fields with params values when component mounts
  useEffect(() => {
    if (params.startDate && params.endDate) {
      setDateRange({
        start: parseDate(params.startDate),
        end: parseDate(params.endDate),
      });
    }
    if (params.sortBy) {
      setSortedBy(new Set([params.sortBy]));
    }
  }, [params.startDate, params.endDate, params.sortBy]);

  // Initialize selected status and product text when data is loaded
  useEffect(() => {
    if (params.status && StatusList.length > 0) {
      setSelectedStatus(
        StatusList.find((option) => option[0] === params.status)
      );
    }

    if (params.productReference) {
      setProductText(params.productReference);
    }
  }, [params.status, params.productReference, StatusList]);

  // Clear all filters
  const ClearFilter = () => {
    setDateRange({
      start: null,
      end: null,
    });
    setSelectedStatus(null);
    setProductText("");
    setSortedBy(new Set());
    setIsStatusListOpen(false);

    dispatch(resetParams());
    onClose();
  };

  // Apply filters
  const handleApplyFilter = () => {
    const filter = {
      startDate:
        dateRange && dateRange.start
          ? moment(dateRange.start.toDate()).format("YYYY-MM-DD")
          : null,
      endDate:
        dateRange && dateRange.end
          ? moment(dateRange.end.toDate()).format("YYYY-MM-DD")
          : null,
      status: (selectedStatus && selectedStatus[0]) || null,
      productReference: productText || null,
      sortBy: Array.from(sortedBy)[0] || null,
    };

    dispatch(updateParams({ ...filter }));
    onClose();
  };

  // Reset state when modal is closed
  useEffect(() => {
    if (!isOpen) {
      setDateRange({
        start: null,
        end: null,
      });
      setSelectedStatus(null);
      setProductText("");
      setSortedBy(new Set());
      setIsStatusListOpen(false);
    }
  }, [isOpen]);

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={() => {
        onClose();
      }}
      closeOnClickOutside={false}
      title="Filter Sourcing Requests"
      position="top-32"
      footerContent={
        <div className="flex flex-row gap-2 flex-1 justify-center md:justify-end">
          <div className="flex flex-row justify-end flex-1 items-center gap-4">
            <Button
              className="rounded-full bg-glb_blue text-white text-xs p-2 md:text-base md:p-4"
              onPress={() => handleApplyFilter()}>
              <FilterIcon size={16} /> Apply Filter
            </Button>
            <Button
              className="rounded-full bg-glb_red text-white text-xs p-2 md:text-base md:p-4"
              onPress={() => ClearFilter()}>
              <Delete02Icon size={16} /> Clear All
            </Button>
          </div>
        </div>
      }>
      <div>
        <div className="flex flex-col lg:flex-row gap-2">
          <div className="w-full lg:w-1/2">
            <UnderlinedInput
              label="Product"
              value={productText}
              onChange={(e) => setProductText(e.target.value)}
              start={true}
            />
          </div>
          <div className="w-full lg:w-1/2">
            <label htmlFor="#Status" className="block mr-2">
              <span className="text-sm text-[#00000050] dark:text-[#FFFFFF30]">
                Status
              </span>
              <GeneralSelector
                id="Status"
                placeholder="Select a status..."
                open={isStatusListOpen}
                onToggle={() => setIsStatusListOpen(!isStatusListOpen)}
                onChange={(val) => {
                  setSelectedStatus(StatusList.find((opt) => opt[1] === val));
                }}
                selectedValue={selectedStatus ? selectedStatus[1] : ""}
                options={StatusList.map((p) => p[1])}
                loading={loadingStatusList}
              />
            </label>
          </div>
        </div>
        <div className="flex flex-col lg:flex-row my-2">
          <div
            ref={datePickerRef}
            className="w-full flex justify-center items-center lg:w-[80%]">
            <I18nProvider locale="en-GB">
              <DateRangePicker
                calendarProps={{
                  classNames: {
                    base: "bg-background",
                    headerWrapper: "pt-4 bg-background",
                    prevButton: "border-1 border-default-200 rounded-small",
                    nextButton: "border-1 border-default-200 rounded-small",
                    gridHeader:
                      "bg-background shadow-none border-b-1 border-default-100",
                    cellButton: [
                      "data-[today=true]:bg-default-100 data-[selected=true]:bg-transparent rounded-small",
                      "data-[range-start=true]:before:rounded-l-small",
                      "data-[selection-start=true]:before:rounded-l-small",
                      "data-[range-end=true]:before:rounded-r-small",
                      "data-[selection-end=true]:before:rounded-r-small",
                      "data-[selected=true]:data-[selection-start=true]:data-[range-selection=true]:rounded-small",
                      "data-[selected=true]:data-[selection-end=true]:data-[range-selection=true]:rounded-small",
                    ],
                  },
                }}
                firstDayOfWeek="mon"
                classNames={{
                  label: ["!text-[#00000050] dark:!text-[#FFFFFF30] "],
                  selectorButton: "justify-center",
                  input: "hidden",
                  separator: "hidden",
                  innerWrapper: "cursor-pointer",
                }}
                CalendarBottomContent={
                  <RadioGroup
                    value={sortedBy}
                    onValueChange={setSortedBy}
                    aria-label="Sort By"
                    label="Sort By"
                    classNames={{
                      base: "w-full pb-2 radiogroup",
                      label: "px-3",
                      wrapper:
                        "-my-2.5 py-2.5 px-3 gap-1 flex-nowrap max-w-[w-[calc(var(--visible-months)_*_var(--calendar-width))]] overflow-scroll hide-scrollbar",
                    }}
                    defaultValue="createdAt"
                    orientation="horizontal">
                    <CustomRadio value="createdAt">Created Date</CustomRadio>
                    <CustomRadio value="updatedAt">Updated Date</CustomRadio>
                  </RadioGroup>
                }
                CalendarTopContent={
                  <ButtonGroup
                    fullWidth
                    className="px-3 pb-2 pt-3 bg-content1 [&>button]:text-default-500 [&>button]:border-default-200/60"
                    radius="full"
                    size="sm"
                    variant="bordered">
                    <Button
                      onClick={() => {
                        setDateRange(lastMonth);
                      }}>
                      Last Month
                    </Button>
                    <Button
                      onClick={() => {
                        setDateRange(lastWeek);
                      }}>
                      Last week
                    </Button>
                    <Button
                      onClick={() => {
                        setDateRange(thisWeek);
                      }}>
                      This week
                    </Button>
                  </ButtonGroup>
                }
                startContent={
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenCalendar();
                    }}
                    className="w-full flex justify-start items-center gap-1">
                    {!(!dateRange.end || !dateRange.start) && (
                      <div
                        onClick={() => {
                          setDateRange({
                            start: null,
                            end: null,
                          });
                        }}
                        className="flex justify-center items-center p-1 bg-transparent cursor-pointer">
                        <Cancel01Icon size={16} />
                      </div>
                    )}
                    <span
                      className={` ${
                        !dateRange.end || !dateRange.start
                          ? "text-sm text-[#00000050] dark:text-[#FFFFFF30]"
                          : "text-medium text-black dark:text-white"
                      }`}>
                      {formatDate(dateRange)}
                    </span>
                  </div>
                }
                className="flex-grow w-full "
                value={dateRange}
                onChange={setDateRange}
                color="primary"
                label="Date Range"
                variant="underlined"
              />
            </I18nProvider>
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default SourcingFilterModal;
