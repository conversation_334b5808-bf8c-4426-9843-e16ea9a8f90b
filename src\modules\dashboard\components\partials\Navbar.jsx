import { useThemeProvider } from "@/core/providers/ThemeContext.jsx";
import { But<PERSON> } from "@heroui/button";
import { Code } from "@heroui/code";
import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
} from "@heroui/dropdown";
import { Input } from "@heroui/input";
import { Navbar, NavbarItem, NavbarMenuItem } from "@heroui/navbar";
import { User } from "@heroui/user";

import {
  ArrowDown01Icon,
  CommandIcon,
  FilterIcon,
  Logout05Icon,
  Moon02Icon,
  MoonCloudIcon,
  Search01Icon,
  Settings02Icon,
  SidebarRight01Icon,
  Sun02Icon,
  ViewIcon,
  ViewOffSlashIcon,
} from "hugeicons-react";
import { useEffect, useRef, useState, useCallback } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { RouteNames } from "../../../../core/routes/routes";

import { AnimatePresence, motion } from "framer-motion";
import ThemeToggle from "../ThemeToggle";
import codPowerGroupLogo from "@shared/assets/images/logo-png.png"; //cod-power-group-logo.svg";
import codPowerGroupLogoDark from "@shared/assets/images/logo-png-dark.png"; //cod-power-group-logo-dark.svg";
import { logoutUser } from "../../../../core/redux/slices/authSlice";
import { useDispatch, useSelector } from "react-redux";
import { updateParams as updateOrderParams } from "../../../../core/redux/slices/orders/ordersManagementSlice";
import { updateParams as updateInvoiceParams } from "../../../../core/redux/slices/invoices/invoiceSlice";
import { setShowPrices } from "../../../../core/redux/slices/contentSlice";
import { Spinner, Tooltip } from "@heroui/react";

export default function NavbarComponent({
  epingled,
  setEpingled,
  showSidebar,
  setShowSidebar,
  isFilterOpen,
  SetFilterOpen,
}) {
  const { currentTheme, changeCurrentTheme } = useThemeProvider();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { paramsCount: ordersParamsCount, params, loading } = useSelector(
    (state) => state.orders
  );
  const { paramsCount: invoicesParamsCount } = useSelector(
    (state) => state.invoices
  );
  const { paramsCount: productsParamsCount } = useSelector(
    (state) => state.products
  );
  const { paramsCount: dashboardParamsCount } = useSelector(
    (state) => state.dashboard
  );
  const showPrices = useSelector((state) => state.content.showPrices);

  const [searchValue, setSearchValue] = useState("");
  const searchInput = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { pathname } = location;
  const user = JSON.parse(localStorage.getItem("xM_htUju"));

  // Determine which paramsCount to use based on the current route
  const isInvoicesPage =
    pathname === RouteNames.serviceInvoices ||
    pathname === RouteNames.sourcingInvoices;
  const isSettingsPage = pathname === RouteNames.settings;
  const isProductsPage = pathname === RouteNames.allProducts;
  const isDashboardPage = pathname === RouteNames.dashboard;
  const isOrdersPage = pathname === RouteNames.allOrders;
  const isFollowupPage = pathname === RouteNames.followUp;
  const isSourcingPage = pathname === RouteNames.SourcingRequest;
  const isStatisticsPage =
    pathname === RouteNames.analytics || pathname === RouteNames.funds;

  // Set the appropriate paramsCount based on the current page
  let paramsCount = 0;
  if (isInvoicesPage) {
    paramsCount = invoicesParamsCount;
  } else if (isProductsPage) {
    paramsCount = productsParamsCount;
  } else if (isDashboardPage) {
    paramsCount = dashboardParamsCount;
  } else {
    paramsCount = ordersParamsCount;
  }

  // Determine if filter button should be shown
  const shouldShowFilterButton =
    isInvoicesPage ||
    isProductsPage ||
    isDashboardPage ||
    isOrdersPage ||
    isFollowupPage ||
    isSourcingPage ||
    isStatisticsPage;

  const debounce = (func, delay) => {
    let timer;
    return (...args) => {
      clearTimeout(timer);
      timer = setTimeout(() => func(...args), delay);
    };
  };

  const debouncedSearch = useCallback(
    debounce((value) => {
      if (value !== "") {
        if (isInvoicesPage) {
          dispatch(updateInvoiceParams({ ...{ invoiceNum: value } }));
        } else {
          dispatch(updateOrderParams({ ...{ orderNum: value } }));
          if (pathname !== RouteNames.allOrders) {
            setTimeout(() => {
              navigate(RouteNames.allOrders);
            }, 50);
          }
        }
      } else {
        if (isInvoicesPage) {
          dispatch(updateInvoiceParams({ ...{ invoiceNum: null } }));
        } else if (params && params.orderNum) {
          dispatch(updateOrderParams({ ...{ orderNum: null } }));
        }
      }
    }, 300),
    [dispatch, isInvoicesPage, navigate, pathname, params]
  );

  const handleSearchChange = (e) => {
    setSearchValue(e.target.value);
    debouncedSearch(e.target.value);
  };

  // Get invoices params
  const invoicesParams = useSelector((state) => state.invoices.params);

  // Initialize search value from params.orderNum or params.invoiceNum based on route
  useEffect(() => {
    if (isInvoicesPage && invoicesParams.invoiceNum) {
      setSearchValue(invoicesParams.invoiceNum);
    } else if (!isInvoicesPage && params.orderNum) {
      setSearchValue(params.orderNum);
    } else {
      setSearchValue("");
    }
  }, [params.orderNum, invoicesParams.invoiceNum, isInvoicesPage]);

  useEffect(() => {
    const handleKeyDown = (e) => {
      // Use userAgent instead of deprecated platform property
      const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent);
      const isCmdK = isMac
        ? e.metaKey && e.key === "k"
        : e.ctrlKey && e.key === "k";

      if (isCmdK) {
        e.preventDefault();
        searchInput.current?.focus();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  const handleNotificationsClick = () => {
    if (RouteNames.notification === pathname) {
      navigate(-1);
    } else {
      navigate(RouteNames.notification);
    }
  };



  return (
    <>
      <Navbar
        disableAnimation
        isBordered
        className="z-20 w-full px-2 py-4 bg-white dark:bg-dark-gradient-header "
        maxWidth="full">
        <Button
          onClick={() => setEpingled(Boolean(!epingled))}
          isIconOnly
          variant="light"
          className="hidden lg:flex">
          <SidebarRight01Icon />
        </Button>
        <Button
          onClick={() => setShowSidebar(Boolean(!showSidebar))}
          isIconOnly
          variant="light"
          className="lg:hidden">
          <SidebarRight01Icon />
        </Button>

        <NavbarItem className="mr-auto lg:hidden">
          <Link to="/dashboard">
            {currentTheme === "light" ? (
              <img
                src={codPowerGroupLogo}
                alt="power group world logo"
                className="w-36"
              />
            ) : (
              <img
                src={codPowerGroupLogoDark}
                alt="power group world logo"
                className="w-36"
              />
            )}
          </Link>
        </NavbarItem>
        <div className="flex justify-end md:justify-between item-center w-full">
          <NavbarItem className="">
            <div className="flex flex-col items-start justify-between w-full gap-4  my-6 md:flex-row ">
              <div className="hidden md:flex">
                {!isSettingsPage && <Input
                  ref={searchInput}
                  value={searchValue}
                  onChange={handleSearchChange}
                  className={` ${showSidebar ? "" : "ml-auto"} min-w-80`}
                  placeholder="Search now"
                  classNames={{
                    inputWrapper: "bg-[#ECECEC] dark:bg-[#1E1D20] rounded-full",
                  }}
                  endContent={
                    loading && searchValue ? <Spinner size="sm" /> : <Code className="flex flex-row justify-center pl-0 bg-transparent dark:text-[#8E8E8F]">
                      {" "}
                      &nbsp;
                      <CommandIcon className="mr-1" size={16} /> + K
                    </Code>
                  }
                  autoComplete="off"
                  startContent={<Search01Icon size={24} />}
                />}

                {shouldShowFilterButton && (
                  <Button
                    isIconOnly
                    className={`mx-2 overflow-visible relative text-white rounded-full ${paramsCount > 0
                      ? "bg-glb_blue"
                      : "bg-[#ECECEC] dark:bg-[#1E1D20]"
                      } `}
                    onClick={() => SetFilterOpen()}>
                    <FilterIcon
                      size={18}
                      className={`${paramsCount > 0
                        ? "text-white"
                        : "text-gray-800 dark:text-white"
                        }  `}
                    />
                  </Button>
                )}
              </div>
            </div>
          </NavbarItem>
          <div className="flex items-center gap-2 justify-center ">
            {/* <NavbarItem>
              <Button
                onClick={() => handleNotificationsClick()}
                isIconOnly
                className={`overflow-visible ${
                  RouteNames.notification === pathname
                    ? "bg-info hover:bg-info/80"
                    : "bg-[#ECECEC] dark:bg-[#1E1D20] hover:bg-gray-100 dark:hover:bg-gray-700/50"
                }  rounded-full flex items-center justify-center`}
                aria-haspopup="true">
                <span className="sr-only">Notifications</span>
                <Notification01Icon
                  className={`${
                    RouteNames.notification === pathname
                      ? "text-white"
                      : "text-gray-800 dark:text-white"
                  }`}
                />
                <div className="absolute top-0 right-0 w-2.5 h-2.5 bg-red-500 border-2 border-gray-100 dark:border-gray-900 rounded-full"></div>
              </Button>
            </NavbarItem> */}
            <NavbarItem className="">
              <Tooltip content={showPrices ? "Hide prices" : "Show prices"}>
                {showPrices ? (
                  <Button
                    isIconOnly
                    color="default"
                    to="#"
                    variant="flat"
                    className="font-bold bg-[#ECECEC] dark:bg-[#1E1D20] text-gray-700 rounded-full"
                    onClick={() => dispatch(setShowPrices(false))}>
                    <ViewIcon className="text-gray-800 dark:text-white" />
                  </Button>
                ) : (
                  <Button
                    isIconOnly
                    color="secondary"
                    to="#"
                    variant="flat"
                    className="font-bold bg-[#ECECEC] dark:bg-[#1E1D20] text-gray-700 rounded-full"
                    onClick={() => dispatch(setShowPrices(true))}>
                    <ViewOffSlashIcon className="text-gray-800 dark:text-white" />
                  </Button>
                )}
              </Tooltip>
            </NavbarItem>
            <NavbarItem className="">
              {currentTheme === "light" ? (
                <Button
                  isIconOnly
                  color="default"
                  to="#"
                  variant="flat"
                  className="font-bold bg-[#ECECEC] dark:bg-[#1E1D20] text-gray-700 rounded-full"
                  onClick={() => changeCurrentTheme("dark")}>
                  <Moon02Icon className="text-gray-800 dark:text-white" />
                </Button>
              ) : (
                <Button
                  isIconOnly
                  color="secondary"
                  to="#"
                  variant="flat"
                  className="font-bold bg-[#ECECEC] dark:bg-[#1E1D20] text-gray-700 rounded-full"
                  onClick={() => changeCurrentTheme("light")}>
                  <Sun02Icon className="text-gray-800 dark:text-white" />
                </Button>
              )}
            </NavbarItem>
            <NavbarItem className="ml-2 mr-4 hidden md:block">
              <Dropdown placement="bottom-start">
                <DropdownTrigger className="bg-black/5 dark:bg-[#1E1D20] px-2 py-1.5 border border-black/10 dark:border-white/10 rounded-full">
                  <div className="flex flex-row items-center gap-2">
                    <User
                      as="button"
                      avatarProps={{
                        src: user.logo,
                        size: "sm",
                      }}
                      classNames={{
                        name: "font-bold",
                      }}
                      className="transition-transform"
                      name={user.fullname}
                    />
                    <ArrowDown01Icon className="text-gray-400" />
                  </div>
                </DropdownTrigger>
                <DropdownMenu aria-label="User Actions" variant="flat">
                  <DropdownItem key="settings">
                    <div
                      className="cursor-pointer"
                      onClick={() => navigate(RouteNames.settings)}>
                      Settings
                    </div>
                  </DropdownItem>
                  <DropdownItem key="logout" color="danger">
                    <div
                      className="text-glb_red cursor-pointer"
                      onClick={() => dispatch(logoutUser())}>
                      Log Out
                    </div>
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </NavbarItem>
            <NavbarItem className="md:hidden flex justify-center items-center">
              <Button
                className=" rounded-full"
                isIconOnly
                variant="light"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsMenuOpen(!isMenuOpen);
                }}
                aria-label={isMenuOpen ? "Close menu" : "Open menu"}>
                {/* <Menu11Icon /> */}
                <img src={"https://i.pravatar.cc/150?u=a042581f4e29026024d"} />
              </Button>
            </NavbarItem>
          </div>
        </div>
        <AnimatePresence>
          {isMenuOpen && (
            <div
              onClick={() => setIsMenuOpen(false)}
              className={`backdrop-blur-xl backdrop-saturate-150 z-30 flex fixed w-full inset-0 p-0 h-screen bg-gray-500/10`}>
              <motion.div
                layout
                initial={{ x: "100%" }}
                animate={{ x: 0 }}
                exit={{ x: "100%" }}
                transition={{ type: "tween", duration: 0.3 }}
                onClick={(e) => e.stopPropagation()}
                className={`max-w-80 ml-auto h-screen bg-base_light dark:bg-base_dark`}>
                {/* <NavbarMenuItem className="ml-auto mr-4 w-fit">
                                <Button isIconOnly className="my-4 bg-gray-100 rounded-full dark:bg-gray-800"
                                    onClick={() => setIsMenuOpen(false)}>
                                    <Cancel01Icon className="mx-auto" />
                                </Button>
                            </NavbarMenuItem> */}
                <motion.div
                  layout
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                  exit={{
                    opacity: 0,
                    transition: { ease: "easeIn", delay: 0 },
                  }}
                  className="w-full  overflow-y-auto h-full flex flex-col justify-center ">
                  <NavbarMenuItem className="px-4 my-8">
                    <div className="flex justify-center gap-2  w-full  items-center">
                      <User
                        as="button"
                        avatarProps={{
                          isBordered: false,
                          src: user.logo,
                        }}
                        classNames={{ name: "font-bold" }}
                        className="transition-transform"
                      // description="Super Admin"
                      // name="Tony Reichert"
                      />
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                        exit={{
                          opacity: 0,
                          transition: { ease: "easeIn", delay: 0 },
                        }}
                        className="flex flex-col">
                        <h4 className="font-thin">
                          {"Hello, 👋 Good Morning"}
                        </h4>
                        <h1 className="font-semibold">{user.fullname}</h1>
                      </motion.div>
                    </div>

                    <div className="flex flex-row w-full  my-4">
                      <Input
                        initial={{ scale: 1 }}
                        whileFocus={{ scale: 0.9 }}
                        transition={{ duration: 0.1 }}
                        value={searchValue}
                        onChange={handleSearchChange}
                        className="w-full ml-auto"
                        placeholder="Search"
                        classNames={{
                          inputWrapper:
                            "bg-gray-100 dark:bg-neutral-800 rounded-full",
                        }}
                        startContent={
                          <Search01Icon size={18} className="text-gray-500" />
                        }
                        endContent={
                          searchValue !== "" ? (
                            <div
                              className="font-bold cursor-pointer text-primary"
                              onClick={() => {
                                setIsMenuOpen(false);
                                debouncedSearch(searchValue);
                              }}>
                              GO
                            </div>
                          ) : (
                            <FilterIcon
                              onClick={() => SetFilterOpen()}
                              size={18}
                              className={`cursor-pointer ${isFilterOpen ? "text-glb_blue" : "text-gray-500"
                                }  `}
                            />
                          )
                        }
                      />
                    </div>
                  </NavbarMenuItem>
                  <NavbarMenuItem className="px-8 my-12 text-sm flex-grow">
                    <h3 className="mt-4 mb-2 text-gray-400">System</h3>
                    <ul>
                      <li className="px-2 py-2">
                        <Link
                          to="#"
                          className="flex w-full justify-start gap-4 items-center">
                          <span className="flex w-full gap-1">
                            <MoonCloudIcon
                              className={showSidebar ? "mr-2 ml-1" : ""}
                              size="20"
                            />
                            Dark Mode
                          </span>
                          <ThemeToggle />
                        </Link>
                      </li>
                      <li className="px-2 py-2">
                        <Link
                          state={{ from: pathname }}
                          to="/settings"
                          className="flex w-full">
                          <span className="flex w-full gap-1">
                            <Settings02Icon className="mr-2" size="20" />
                            Settings
                          </span>
                        </Link>
                      </li>
                      {/* <li className="px-2 py-2">
                                                <Link state={{ from: pathname }} to="/referrals" className="flex w-full">
                                                    <span className="flex w-full gap-1">
                                                        <Share08Icon className="mr-2" size="20" />
                                                        Referrals
                                                    </span>
                                                </Link>
                                            </li>
                                            <li className="px-2 py-2">
                                                <Link state={{ from: pathname }} to="/help" className="flex w-full">
                                                    <span className="flex w-full gap-1">
                                                        <HelpCircleIcon className="mr-2" size="20" />
                                                        Help
                                                    </span>
                                                </Link>
                                            </li> */}
                    </ul>
                  </NavbarMenuItem>

                  <NavbarMenuItem className="block px-8 my-12">
                    <Link
                      to="/login"
                      className="flex flex-row items-center justify-start w-full gap-2 my-2 text-danger">
                      <Logout05Icon size={18} className="rotate-45" />
                      <span>Logout</span>
                    </Link>
                    <span className="text-sm text-gray-500">
                      Copyright © {new Date().getFullYear()}. Power Group World,
                      All rights reserved.
                    </span>
                  </NavbarMenuItem>
                </motion.div>
              </motion.div>
            </div>
          )}
        </AnimatePresence>
      </Navbar>

      {/* <FilterModal modalOpen={showFilterModal} setModalOpen={setShowFilterModal} id={2} /> */}
    </>
  );
}
