import React from "react";
import { ArrowUp01Icon, ArrowUpDownIcon, Delete02Icon, FilterIcon } from "hugeicons-react";

export default function FilterModal({ open, onClose }) {
  // If 'open' is false, render nothing
  if (!open) return null;

  return (
    <div className="fixed inset-0 overflow-hidden flex items-center justify-center px-4 sm:px-6 bg-[#DCDDE4]/50 dark:bg-[#08090B]/70 z-[200] transition-opacity">
      <div className="pb-4 bg-white dark:bg-[#0C0B0C] border border-transparent dark:border-white/10 w-full max-w-3xl max-h-full rounded-2xl shadow-lg">
        {/* Modal Header */}
        <div className="relative flex justify-between items-center mb-4">
          <h2 className="w-full p-6 text-[16px] font-medium lg:text-2xl border-b border-gray-200 dark:border-gray-800 lg:font-semibold flex items-center">
            Filter Properties
          </h2>
          {/* Close Button */}
          <button
            onClick={onClose}
            className="text-gray-600 hover:text-black absolute top-[22px] right-[22px] text-2xl"
          >
            ✖
          </button>
        </div>

        {/* Modal Body */}
        <div className="space-y-6 lg:p-6 p-3 min-h-[300px]">
          <h4 className="flex gap-2 items-center text-gray-400 my-2">
            Filter By <ArrowUpDownIcon size={18} />
          </h4>
          <div className="space-y-6">
            {/* Country Input */}
            <div>
              <input
                type="text"
                placeholder="Country"
                className="w-full bg-transparent border border-gray-700 rounded-lg py-2 px-3"
              />
            </div>

            {/* Select Product Dropdown */}
            <div>
              <select className="w-full bg-transparent border border-gray-700 rounded-lg py-2 px-3">
                <option value="" disabled selected>
                  select product
                </option>
                <option value="1">Product 1</option>
                <option value="2">Product 2</option>
              </select>
            </div>

            {/* Due Date Section (unchanged) */}
            <div>
              <h3 className="font-medium justify-between mb-2 flex items-center">
                Due Date
                <ArrowUp01Icon className="w-5 h-5 ml-2" />
              </h3>
             
            </div>
          </div>
        </div>

        {/* Modal Footer */}
        <div className="flex justify-between items-center px-6 pb-6 border-t border-gray-200 dark:border-gray-800 mt-8 pt-12">
          <div className="flex flex-col text-sm">
            <strong>3 Selected</strong>
            <span className="text-gray-500">17 Total Filtered</span>
          </div>
          <div className="flex gap-2">
            <button
              onClick={onClose}
              className="lg:px-8 lg:py-2 py-[6px] px-[18px] lg:h-[50px] h-fit lg:text-[14px] text-[12px] flex items-center bg-blue-600 text-white rounded-full"
            >
              <FilterIcon size={18} className="text-white" />
              Apply Filter
            </button>
            <button
              onClick={onClose}
              className="lg:px-8 lg:py-2 py-[6px] px-[18px] lg:h-[50px] h-fit bg-red-600 lg:text-[14px] text-[12px] text-white rounded-full flex items-center"
            >
              <Delete02Icon size={18} className="text-white" />
              Clear All
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
