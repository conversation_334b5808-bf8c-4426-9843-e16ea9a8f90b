import { Switch } from "@heroui/switch";

const SwitchStyled = ({ isSelected, onChange }) => {
    return (
        <Switch
            isSelected={isSelected}
            onChange={onChange}
            className="me-12"
            classNames={{
                wrapper: [
                    // Default state
                    "bg-gray-200 dark:bg-gray-700",
                    // Focus state
                    "group-data-[focus-visible=true]:ring-2",
                    "group-data-[focus-visible=true]:ring-blue-500",
                    // Selected state
                    "data-[selected=true]:bg-blue-500",
                    "data-[selected=true]:dark:bg-glb_blue",
                    // Hover state on selected
                    "data-[selected=true]:hover:bg-glb_blue",
                    "data-[selected=true]:dark:hover:bg-blue-700",
                    // Transition
                    "transition-colors"
                ].join(" "),
                thumb: [
                    // Default state
                    "bg-white",
                    // Selected state shadow
                    "data-[selected=true]:shadow-lg",
                    // Transition
                    "transition-transform"
                ].join(" ")
            }}
        />
    );
};

export default SwitchStyled;