import { But<PERSON> } from "@heroui/button";
import { Chip, Image, Link, Spinner } from "@heroui/react";
import CustomModal from "@shared/components/CustomModal.jsx";
import DataTile from "@shared/components/DataTile.jsx";
import { Step, StepIndicator, Stepper, StepTitle } from "@shared/components/Stepper.jsx";
import {
    Navigation03Icon,
    Settings02Icon,
    ShoppingBasketDone02Icon,
    TruckDeliveryIcon,
    WarehouseIcon
} from "hugeicons-react";
import moment from "moment";
import React, { useEffect, useRef, useState } from "react";
import { getCurrencies } from "../../../core/redux/slices/general/generalSlice";
import { useDispatch } from "react-redux";
import { get } from "lodash";
import { countriesUrl } from "../../../core/redux/slices/URLs";
import axios from "axios";
import { getToken } from "../../../core/services/TokenHandler";
import { toast } from "sonner";
import PriceRenderer from "../../shared/components/PriceRenderer";

// Define row class names for tables
const tableRowClassNames = {
    even: 'bg-white dark:bg-[#ffffff10] h-12',
    odd: 'bg-[#00000010] dark:bg-[#ffffff05] h-12'
}
const TableComponent = ({
    loading,
    columns,
    count,
    data,
    renderCell,
    rowClassNames = {
        even: 'bg-white dark:bg-[#ffffff10] h-12',
        odd: 'bg-[#00000010] dark:bg-[#ffffff05] h-12'
    },
    emptyMessage = "No Data Available"
}) => {
    // Default renderCell function if none is provided
    const defaultRenderCell = (item, columnKey) => {
        if (columnKey.toLowerCase().includes('date') && item[columnKey]) {
            return moment(item[columnKey]).format('DD-MM-YYYY HH:mm');
        } else if (typeof item[columnKey] === 'string') {
            return item[columnKey].charAt(0).toUpperCase() + item[columnKey].slice(1);
        } else {
            return item[columnKey];
        }
    };

    // Use the provided renderCell function or fall back to the default
    const cellRenderer = renderCell || defaultRenderCell;

    return (
        <div className="bg-black/5 dark:bg-white/5 rounded-2xl">
            <h3 className="text-lg font-semibold p-4">Products : {count}</h3>
            <div className="overflow-x-auto flex-grow ">

                <table className="box-border border-collapse overflow-auto min-w-full">
                    <thead>
                        <tr className="border-b border-gray-300 h-11 dark:border-gray-600">
                            {columns.map((col) => (
                                <th key={col.key} className="dark:bg-[#ffffff05] mx-6 whitespace-nowrap text-center px-10 py-2 text-[#00000060] dark:text-[#ffffff60] text-base font-medium">
                                    {col.label}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {loading ? (
                            // Loading skeleton
                            (Array.from({ length: 5 }).map((_, rowIndex) => (
                                <motion.tr
                                    key={rowIndex}
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3, delay: rowIndex * 0.1 }}
                                    className={`h-14 ${rowIndex % 2 === 0 ? rowClassNames.even : rowClassNames.odd}`}
                                >
                                    {columns.map((_, colIndex) => (
                                        <td
                                            key={colIndex}
                                            className="px-1 py-2 text-center dark:text-gray-300 text-sm whitespace-nowrap"
                                        >
                                            <motion.div
                                                initial={{ opacity: 0 }}
                                                animate={{ opacity: [0, 1, 0] }}
                                                transition={{
                                                    duration: 1,
                                                    repeat: Infinity,
                                                    repeatType: "loop",
                                                    repeatDelay: 0.5,
                                                    delay: colIndex * 0.1,
                                                }}
                                                className="h-4 bg-gray-300 dark:bg-gray-700 rounded"
                                            />
                                        </td>
                                    ))}
                                </motion.tr>
                            )))
                        ) : data && data.length > 0 ? (
                            // Actual data rows
                            (data.map((row, index) => {
                                const rowClass = ''//index % 2 === 0 ? rowClassNames.even : rowClassNames.odd;
                                return (
                                    <tr key={index}>
                                        {columns.map((col) => (
                                            <td key={col.key} className={`${rowClass} px-1 py-2 text-center dark:text-gray-300 text-sm whitespace-nowrap`}>
                                                {cellRenderer(row, col.key)}
                                            </td>
                                        ))}
                                    </tr>
                                );
                            }))
                        ) : (
                            // Empty state
                            (<tr>
                                <td colSpan={columns.length} className="text-center p-4">
                                    {emptyMessage}
                                </td>
                            </tr>)
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

const ViewProductRequestModal = ({ isOpen, onClose, selectedItemID }) => {
    const dispatch = useDispatch();
    const [countryNames, setCountryNames] = useState({});
    const [loadingCountries, setLoadingCountries] = useState({});

    const fetchCountryName = async (itemID, countryID) => {
        setLoadingCountries((prev) => ({ ...prev, [itemID]: true }));
        try {
            const response = await axios.get(`${countriesUrl}?id=${countryID}`, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response === "success") {
                setCountryNames((prev) => ({ ...prev, [itemID]: response.data.result.name || "-" }));
            } else {
                setCountryNames((prev) => ({ ...prev, [itemID]: "-" }));
            }
        } catch (error) {
            console.error(error.message || "Error fetching country name");
            setCountryNames((prev) => ({ ...prev, [itemID]: "-" }));
        } finally {
            setLoadingCountries((prev) => ({ ...prev, [itemID]: false }));
        }
    };

    useEffect(() => {
        if (!selectedItemID?.products) return;

        // Find all products that need fetching of destination country names
        const missingCountries = selectedItemID.products
            .filter(
                (item) =>
                    item.destinationCountry && // has destination country
                    !countryNames[item.id] &&  // not in cache
                    !loadingCountries[item.id] // not already loading
            )
            .map((item) => ({ itemID: item.id, countryID: item.destinationCountry }));

        missingCountries.forEach(({ itemID, countryID }) => {
            fetchCountryName(itemID, countryID);
        });
    }, [selectedItemID?.products, countryNames, loadingCountries]);


    const steps = [
        { title: "Placed", icon: <ShoppingBasketDone02Icon className="text-white" /> },
        { title: "Processing", icon: <Settings02Icon className="text-white" /> },
        { title: "Parking", icon: <Navigation03Icon className="text-white" /> },
        { title: "Shipped", icon: <TruckDeliveryIcon className="text-white" /> },
        { title: "Delivered", icon: <WarehouseIcon className="text-white" /> },
    ];

    const ProductColumns =
        [
            { key: 'image', label: 'Image' },
            { key: 'name', label: 'Product Name' },
            { key: 'processMode', label: 'Process Mode' },
            { key: 'shippingMethod', label: 'Shipping Method' },
            { key: 'variants', label: 'Variants' },

            // { key: 'price', label: 'Price' },
        ]

    const shippingMode = {
        byair: "By Air",
        bysea: "By Sea",
    };


    const renderTableCell = (item, columnKey) => {
        switch (columnKey) {

            case "processMode":

                return (
                    <div className="flex mx-auto w-fit justify-center items-center flex-col gap-1 ">
                        <span className="font-bold text-lg text-glb_blue">{item.processMode}</span>
                        <span className="font-normale"><b>Qty : </b>{item.quantity}</span>
                        {item.processMode === 'Bulk' && (
                            <span className="font-normale"><b>To : </b>{
                                loadingCountries[item.id] ? <Spinner size="sm" /> : <span>{countryNames[item.id] || "-"}</span>
                            }</span>
                        )}
                    </div>
                )

            case 'date':
                return item[columnKey] ? moment(item[columnKey]).format('DD-MM-YYYY HH:mm') : '-';

            case 'image':

                return item.image ? (
                    <Image disableSkeleton={true} classNames={{ wrapper: 'mx-auto' }}
                        src={item.image} isZoomed={true} height={80} width={80} alt={item.name} />
                ) : (
                    <Image disableSkeleton={true} classNames={{ wrapper: 'mx-auto' }}
                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/250px-No-Image-Placeholder.svg.png"
                        isZoomed={true} height={80} width={80} alt={item.name} />
                );
            case 'name':
                return (
                    <Link isExternal showAnchorIcon href={item.productLink}>
                        {item.name}
                    </Link>)
            case 'price':
                return item.price ? <PriceRenderer price={item.price} /> : 'Not Agreed Yet';
            case 'shippingMethod':
                return (
                    <Chip className="text-success border border-success bg-success/30">{shippingMode[item.shippingMethod] || '-'}</Chip>
                );
            case 'variants':
                return (<div className="flex justify-center items-center gap-1 w-full flex-wrap">
                    {item.variants && item.variants.length > 0 && item.variants.map(v => (
                        <span key={v.name} className="">{v.value || '-'}</span>
                    ))}
                </div>)


            default:
                // Default formatting for all other cell types
                if (typeof item[columnKey] === 'string') {
                    return item[columnKey].charAt(0).toUpperCase() + item[columnKey].slice(1);
                }
                return item[columnKey] || '-';
        }
    };

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={onClose}
            width="max-w-6xl"
            showHeader={true}
            title={`View Request N° 102 `}
            headerClassName="px-8 py-4 border-b"
            bodyClassName="p-8"
            showFooter={false}
            footerContent={
                <div className="flex justify-center gap-4">
                    <Button
                        className="px-8 py-2 rounded-full bg-glb_blue text-white">
                        Accept
                    </Button>
                    <Button
                        className="px-8 py-2 rounded-full border border-gray-300 bg-transparent"
                        onClick={onClose}
                    >
                        Cancel
                    </Button>
                </div>
            }
        >
            <h2 className="text-xl w-full text-center lg:text-start font-medium my-4 px-4 ">CODE #{selectedItemID?.requestCode}</h2>
            <Stepper orientation="horizontal">
                {selectedItemID && steps.map((step, index) => (
                    <Step key={index}>
                        <StepIndicator
                            color={
                                selectedItemID.status === step.title.toLowerCase()
                                    ? "bg-info"
                                    : "bg-[#00000010] dark:bg-[#FFFFFF10]"
                            }
                        >
                            {React.cloneElement(step.icon, {
                                className:
                                    selectedItemID.status === step.title.toLowerCase()
                                        ? "text-white"
                                        : "text-[#00000030] dark:text-[#FFFFFF30]",
                            })}
                        </StepIndicator>
                        <StepTitle
                            className={
                                selectedItemID.status === step.title.toLowerCase()
                                    ? ""
                                    : "text-[#00000030] dark:text-[#FFFFFF30]"
                            }
                        >
                            {step.title}
                        </StepTitle>
                    </Step>
                ))}
            </Stepper>
            <div className="p-2">
                {/* Product Details */}
                <TableComponent
                    count={selectedItemID?.products.length}
                    loading={false}
                    columns={ProductColumns}
                    data={selectedItemID?.products}
                    renderCell={renderTableCell}
                    rowClassNames={tableRowClassNames}
                />
                {/* Quotation Details */}
                <div className="bg-black/5 dark:bg-white/5 lg:w-1/2 rounded-2xl p-6 space-y-4 mt-8">
                    <h3 className="text-lg font-semibold mb-6">Quotation</h3>
                    <div className="space-y-4 text-[12px] lg:text-lg">
                        <div className="flex justify-between items-center">
                            <span className="text-gray-600 dark:text-gray-300">Quantity</span>
                            <span className="font-medium">
                                {selectedItemID?.products.reduce((sum, product) => sum + (product.quantity || 0), 0)}
                            </span>
                        </div>

                        <div
                            className="flex justify-between items-center bg-info dark:bg-info p-6 rounded-xl text-white">
                            <span className="font-semibold">Total</span>
                            <span className="font-semibold">
                                {selectedItemID?.products.reduce((sum, product) => sum + (product.price || 0), 0) ? (
                                    <PriceRenderer
                                        additionalClass={'font-bold'}
                                        price={selectedItemID?.products.reduce((sum, product) => sum + (product.price || 0), 0)}
                                    />
                                ) : 'Not Agreed Yet'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </CustomModal>
    );
};

export default ViewProductRequestModal;