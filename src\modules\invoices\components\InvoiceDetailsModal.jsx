import CustomModal from "@shared/components/CustomModal.jsx";
import { useDispatch } from "react-redux";
import { useCallback, useEffect, useRef, useState } from "react";
import axios from "axios";
import { motion } from "framer-motion";
import { Chip, Link, Spinner, Pagination, Input } from "@heroui/react";
import { getServiceInvoiceDetails, getServiceInvoiceStatusCount, getServiceInvoiceStatusDetails } from "../../../core/redux/slices/invoices/invoiceSlice";
import { details } from "framer-motion/client";
import moment from "moment";
import PriceRenderer from "../../shared/components/PriceRenderer";
import React from "react";
import { statusColorMap, statusIconsMap } from "../../../core/utils/functions";
import { RouteNames } from "../../../core/routes/routes";
import { Search01Icon } from "hugeicons-react";
import { data } from "autoprefixer";

const defaultStatus = [
    {
        slug: 'shipped', title: 'Shipped', count: 0,
        columns: [
            { slug: "orderNum", label: 'Order N°' },

            { slug: "originCountry", label: 'From' },
            { slug: "destinationCountry", label: 'To' },
            { slug: "orderStatus", label: 'Status' },
            { slug: "shippedAt", label: 'Shipped At' },
            { slug: "orderPrice", label: 'Order Price' },
            { slug: "shippingFees", label: 'Shipping' },
            { slug: "firstMileFees", label: 'First Mile' },
            { slug: "customSurcharge", label: 'Custom Surcharge' },
            { slug: "fulfillment", label: 'Fulfillment' },


        ]
    },
    {
        slug: 'delivered', title: 'Delivered', count: 0,
        columns: [
            { slug: "orderNum", label: 'Order N°' },
            { slug: "originCountry", label: 'From' },
            { slug: "destinationCountry", label: 'To' },
            { slug: "orderStatus", label: 'Status' },
            { slug: "orderPrice", label: 'Order Price' },

            { slug: "deliveredFees", label: 'Delivered Fees' },

            { slug: "codFees", label: 'COD Fees' },
            { slug: "vat", label: 'VAT' },

        ]
    },
    {
        slug: 'return', title: 'Return', count: 0,
        columns: [
            { slug: "orderNum", label: 'Order N°' },
            { slug: "originCountry", label: 'From' },
            { slug: "destinationCountry", label: 'To' },
            { slug: "orderPrice", label: 'Order Price' },
            { slug: "orderStatus", label: 'Status' },
            { slug: "fees", label: 'Return Fees' },


        ]
    },
    {
        slug: 'confirmation', title: 'Confirmation', count: 0,

    },
    {
        slug: 'upsell', title: 'Upsell', count: 0,
        columns: [
            { slug: "orderNum", label: 'Order N°' },
            { slug: "orderPrice", label: 'Order Price' },
            { slug: "orderStatus", label: 'Status' },
            { slug: "fees", label: 'Upsell Fees' },


        ]
    },
    {
        slug: 'confirmationCalls', title: 'Outbound Calls - Confirmation', count: 0,
        subTabs:
            [
                { slug: 'all', title: 'All' },
                { slug: 'newlead', title: 'New Lead' },
                { slug: 'confirmed', title: 'Confirmed' },
                { slug: 'noanswer', title: 'No Answer' },
                { slug: 'canceled', title: 'Canceled' },
                { slug: 'schedule', title: 'Scheduled' },
                { slug: 'doubleorder', title: 'Duplicate Order' },
                { slug: 'pending', title: 'Pending' },
                { slug: 'wrongphonenumber', title: 'Wrong Phone Number' },
                { slug: 'test', title: 'Test' },
                { slug: 'processing', title: 'Processing' },
                { slug: 'intransit', title: 'In Transit' },
                { slug: 'delivered', title: 'Delivered' },
                { slug: 'return', title: 'Return' }
            ]

        ,
        columns: [
            { slug: "orderNum", label: 'Order N°' },
            { slug: "callDate", label: 'Date' },
            { slug: "orderStatus", label: 'Response' },
            { slug: "fees", label: 'Call Fees' },


        ]
    },
    {
        slug: 'followupCalls', title: 'Outbound Calls - Follow-up', count: 0,
        subTabs: [
            { slug: 'all', title: 'All' },
            { slug: 'new', title: 'New' },
            { slug: 'delivery', title: 'Delivery again' },
            { slug: 'abandon', title: 'Abandon' },
            { slug: 'no-answer', title: 'No Answer' },
            { slug: 're-delivery', title: 'Re-delivery' },
            { slug: 'wrong-number', title: 'Wrong number' },
            { slug: 'callback', title: 'Call back' },
            { slug: 'rtc', title: 'Return to Client' },
            { slug: 'already-received', title: 'Already received' },
            { slug: 'cnee-pick-up', title: 'Cnee pick up' },
            { slug: 'cnee-feedback-received', title: 'Cnee feedback received' },
            { slug: 'cnee-reject-order', title: 'Cnee reject order' },
            { slug: 'cnee-need-help', title: 'Cnee need help' },
            { slug: 'inbound', title: 'Unpacking' },
            { slug: 'Temporary', title: 'Temporary Storage' }


        ],
        columns: [
            { slug: "orderNum", label: 'Order N°' },
            { slug: "callDate", label: 'Date' },
            { slug: "followpStatus", label: 'FeedBack' },
            { slug: "fees", label: 'Call Fees' },


        ]
    },
]
const TableComponent = ({
    loading,
    columns,
    data, // Ensure data is always an array
    renderCell,
    rowClassNames = {
        even: 'bg-white dark:bg-[#ffffff10] h-12',
        odd: 'bg-[#00000010] dark:bg-[#ffffff05] h-12'
    },
    emptyMessage = "No Data Available",

}) => {
    const [currentPage, setCurrentPage] = useState(1)
    const rowsPerPage = 10
    const paginatedData = data ? data.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage) : [];
    useEffect(() => {
        setCurrentPage(1); // Reset to the first page when data changes


    }, [data])


    return (
        <div>
            <div className="overflow-x-auto custom-scrollbar flex-grow mt-8  min-h-[600px]">
                <table className="box-border border-collapse overflow-auto min-w-full">
                    <thead>
                        <tr className="border-b border-gray-300 h-11 dark:border-gray-600 ">
                            {columns.map((col) => (
                                <th key={col.slug} className="dark:bg-[#ffffff05] mx-6 whitespace-nowrap text-center px-10 py-2 text-[#00000060] dark:text-[#ffffff60] text-base font-medium">
                                    {col.label}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {loading ? (
                            Array.from({ length: 5 }).map((_, rowIndex) => (
                                <motion.tr
                                    key={rowIndex}
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3, delay: rowIndex * 0.1 }}
                                    className={`h-14 ${rowIndex % 2 === 0 ? rowClassNames.even : rowClassNames.odd}`}
                                >
                                    {columns.map((_, colIndex) => (
                                        <td
                                            key={colIndex}
                                            className="px-1 py-2 text-center dark:text-gray-300 text-sm whitespace-nowrap"
                                        >
                                            <motion.div
                                                initial={{ opacity: 0 }}
                                                animate={{ opacity: [0, 1, 0] }}
                                                transition={{
                                                    duration: 1,
                                                    repeat: Infinity,
                                                    repeatType: "loop",
                                                    repeatDelay: 0.5,
                                                    delay: colIndex * 0.1,
                                                }}
                                                className="h-4 bg-gray-300 dark:bg-gray-700 rounded"
                                            />
                                        </td>
                                    ))}
                                </motion.tr>
                            ))
                        ) : paginatedData && paginatedData.length > 0 ? (
                            paginatedData.map((row, index) => {
                                const rowClass = index % 2 === 0 ? rowClassNames.even : rowClassNames.odd;
                                return (
                                    <tr key={index}>
                                        {columns.map((col) => (
                                            <td key={col.slug} className={`${rowClass} px-1 py-2 text-center dark:text-gray-300 text-sm whitespace-nowrap`}>
                                                {renderCell(row, col.slug)}
                                            </td>
                                        ))}
                                    </tr>
                                );
                            })
                        ) : (
                            <tr>
                                <td colSpan={columns.length} className="text-center p-4">
                                    {emptyMessage}
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>
            {data.length > rowsPerPage && (
                <div className="px-2 pagination-container flex justify-center items-center my-4 space-x-2">
                    <Pagination
                        isCompact
                        showControls
                        color="primary"
                        page={currentPage}
                        total={Math.ceil(data.length / rowsPerPage)}
                        onChange={setCurrentPage}
                    />
                </div>
            )}
        </div>
    );
};


// Reusable skeleton component with pulsing animation
const SkeletonPulse = ({ className, delay = 0 }) => (
    <motion.div
        className={`bg-gray-200 dark:bg-gray-700 rounded ${className}`}
        initial={{ opacity: 0.5 }}
        animate={{ opacity: [0.5, 0.8, 0.5] }}
        transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay
        }}
    />
);



const Navigator = ({ navItems, currentTab, setCurrentTab, loading }) => {
    const containerRef = useRef(); // Ref for the parent container
    const itemRefs = useRef([]); // Ref array for each nav item


    const handleTabClick = (slug, idx) => {
        setCurrentTab(slug);

        // Scroll the clicked tab into view
        const element = itemRefs.current[idx];
        if (element) {
            element.scrollIntoView({
                behavior: "smooth", // Smooth scrolling
                block: "center",
                inline: "center",
            });
        }
    };

    if (loading) {
        return (
            <div className="w-full flex justify-start items-start gap-4">
                {Array.from({ length: 5 }).map((_, idx) => (
                    <SkeletonPulse key={idx} className="w-24 h-8" delay={idx * 0.2} />
                ))}
            </div>
        );
    }


    return (
        <div ref={containerRef} className="w-full hide-scrollbar flex justify-start items-start gap-4">
            <div
                ref={(el) => (itemRefs.current[0] = el)} // Store the ref for each item
                onClick={() => handleTabClick('Informations', 0)}

                className={`relative p-3 text-nowrap cursor-pointer hover:opacity-50  text-black dark:text-white  flex items-center justify-center gap-2`}
            >
                <span
                    className={`${currentTab === 'Informations' ? "opacity-100" : "opacity-30"
                        } text-sm md:text-base lg:text-lg`}
                >
                    Informations
                </span>

                {currentTab === 'Informations' && (
                    <motion.div
                        // Unique ID for shared layout animations
                        className="absolute bottom-0 left-0 right-0 h-1 bg-glb_blue"
                        style={{ borderRadius: "4px" }}
                    />
                )}
            </div>
            {navItems.filter(i => i.count != 0).map((i, idx) => {
                idx += 1; // Start idx from 1
                return (
                    <div
                        ref={(el) => (itemRefs.current[idx] = el)} // Store the ref for each item
                        onClick={() => handleTabClick(i.slug, idx)}
                        key={idx}
                        className={`relative p-3 text-nowrap cursor-pointer hover:opacity-50  flex items-center justify-center gap-2`}
                    >
                        <span
                            className={`${currentTab === i.slug ? "opacity-100" : "opacity-30"
                                } text-sm md:text-base lg:text-lg`}
                        >
                            {i.title}
                        </span>
                        <span className="text-xs text-white bg-glb_red rounded-full px-2 py-1">
                            {i.count}
                        </span>
                        {currentTab === i.slug && (
                            <motion.div
                                layoutId="underline" // Unique ID for shared layout animations
                                className="absolute bottom-0 left-0 right-0 h-1 bg-glb_blue"
                                style={{ borderRadius: "4px" }}
                            />
                        )}

                    </div>
                );
            })}

        </div>
    );
};

const InvoiceDetailsModal = ({ isOpen, onClose, invoiceId }) => {
    const dispatch = useDispatch()

    const [loading, setLoading] = useState(false)
    const [loadingCounts, setLoadingCounts] = useState(false)
    const [invoice, setInvoice] = useState(null)
    const [Data, setData] = useState(null)
    const [tabsData, setTabsData] = useState(null)
    const [currentStatus, setCurrentStatus] = useState('Informations')
    const [possibleStatus, setPossibleStatus] = useState(defaultStatus);
    const [currentPage, setCurrentPage] = useState(1);
    const [currentSubStatus, setCurrentSubStatus] = useState('confirmed'); // Default sub-status for confirmation calls
    const rowsPerPage = 10;
    const [searchInput, setsearchInput] = useState('')


    useEffect(() => {
        if (currentStatus && (currentStatus === 'confirmationCalls' || currentStatus === 'followupCalls')) {
            setCurrentSubStatus('all'); // Reset to default sub-status when changing to confirmation or follow-up calls
        }
    }, [currentStatus])

    useEffect(() => {
        setCurrentPage(1); // Reset to the first page when data changes
    }, [Data]);


    const labelClassName = 'w-[40%] truncate text-center text-xs lg:text-base'
    const valueClassName = 'w-[60%] text-center text-sm lg:text-base'

    const onModalClose = () => {
        // Just call the parent's onClose function
        // We'll reset state when the modal reopens
        setInvoice(null);
        setCurrentStatus('Informations');
        setCurrentSubStatus('confirmed'); // Reset to default sub-status
        setPossibleStatus(defaultStatus);
        setData(null)
        setTabsData(null)
        onClose();
    };





    useEffect(() => {
        // Only fetch data when the modal is open
        if (!isOpen) return;

        const fetchStatusCounts = async () => {
            setLoadingCounts(true)
            const updatedStatus = await Promise.all(
                possibleStatus.map(async (status) => {
                    try {
                        const result = await dispatch(
                            getServiceInvoiceStatusCount({ id: invoiceId, status: status.slug })
                        ).unwrap();
                        return { ...status, count: result };
                    } catch (error) {
                        console.error(`Error fetching count for status ${status.slug}:`, error);
                        return { ...status, count: 0 };
                    }
                })
            );
            setPossibleStatus(updatedStatus);
            setLoadingCounts(false);
        };
        const getInvoiceDetails = async () => {
            setLoading(true)
            const res = await dispatch(getServiceInvoiceDetails(invoiceId))
            if (getServiceInvoiceDetails.fulfilled.match(res)) {

                setInvoice(res.payload)
            }
            setLoading(false)
        }

        fetchStatusCounts();
        getInvoiceDetails();
    }, [isOpen, invoiceId, dispatch]);

    const detailsColumns = [
        { key: "id", label: "ID", showIn: 0 },
        { key: "createdAt", label: "Created At", showIn: 2 },
        { key: "invoiceNum", label: "Invoice N°", showIn: 2 },
        { key: "totalRemittance", label: "Total Remittance", showIn: 3 },
        { key: "totalCharges", label: "Total Charges", showIn: 3 },
        { key: "netRemittance", label: "Net Remittance", showIn: 3 },
        { key: "firstMile", label: "First Mile", showIn: 3 },
        { key: "lastMile", label: "Last Mile", showIn: 3 },
        { key: "customSurcharge", label: "Custom Surcharge", showIn: 3 },
        { key: "fulfillment", label: "Fulfillment", showIn: 3 },
        { key: "return", label: "Return", showIn: 3 },
        { key: "deliveredFees", label: "Delivered Fees", showIn: 3 },
        { key: "confirmationCalls", label: "Confirmation Calls", showIn: 3 },
        { key: "followupCalls", label: "Follow-up Calls", showIn: 3 },
        { key: "confirmedFees", label: "Confirmed Fees", showIn: 3 },
        { key: "upsellFees", label: "Upsell Fees", showIn: 3 },
        { key: "vat", label: "VAT", showIn: 3 },
        { key: "codFees", label: "COD Fees", showIn: 3 },
        { key: "enteredLeads", label: "Entered Leads", showIn: 3 },
        { key: "status", label: "Status", showIn: 0 },
        { key: "statusDescription", label: "Status Description", showIn: 2 },
        { key: "paidAt", label: "Paid At", showIn: 2 },
    ];

    const renderCell = (d) => {
        if (!invoice) return null;
        const value = invoice[d.key]
        console.log(invoice, d);

        switch (d.key) {
            case "createdAt":
                return <div className="grid grid-cols-2 gap-2 p-2 bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10" key={d.key}>
                    <span className="font-bold text-xs md:text-sm">{d.label}</span>
                    <span className="font-normal text-center">{moment(value).format('DD/MM/YYYY HH:mm')}</span>

                </div>

            case "totalCharges":
            case "deliveredFees":
            case "confirmationCalls":
            case "followupCalls":
            case "firstMile":
            case "lastMile":
            case "customSurcharge":
            case "fulfillment":
            case "return":
            case "enteredLeads":
            case "confirmedFees":
            case "upsellFees":
            case "vat":
            case "codFees":
                return <div className="grid grid-cols-2 gap-2 p-2 bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10" key={d.key}>
                    <span className="font-bold text-xs md:text-sm">- {d.label}</span>
                    <span className="font-normal text-center"><PriceRenderer price={value} /></span>

                </div>;
            case "totalRemittance":
            case "netRemittance":
                return <div className="grid grid-cols-2 gap-2 p-2 bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10" key={d.key}>
                    <span className="font-bold text-xs md:text-sm">{d.label}</span>
                    <span className="font-normal text-center"><PriceRenderer price={value} /></span>

                </div>;
            case "statusDescription":
                return (
                    <div className="grid grid-cols-2 gap-2 p-2 bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10" key={d.key}>
                        <span className="font-bold text-xs md:text-sm">Status</span>
                        <span className="font-normal text-center">{value}</span>

                    </div>
                );
            default:
                return <div className="grid grid-cols-2 gap-2 p-2 bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10" key={d.key}>
                    <span className="font-bold text-xs md:text-sm">{d.label}</span>
                    <span className="font-normal text-center">{value}</span>

                </div>;
        }
    };

    const renderTableCell = (row, slug) => {
        console.log(row, slug);

        const value = row[slug]
        switch (slug) {
            case "orderNum":
                return <Link isExternal showAnchorIcon href={`${RouteNames.allOrders}?page=1&od=${btoa(row.orderId)}`}>
                    {value}
                </Link>
            case "shippedAt":
            case "callDate":
            case "date":
            case "createdAt":
                return <span className="font-normal text-center">{moment(value).format('DD/MM/YYYY HH:mm')}</span>
            case "shippingFees":
            case "customSurcharge":
            case "fulfillment":
            case "deliveredFees":
            case "codFees":
            case "fees":
            case "firstMileFees":
            case "vat":
                return <span className="font-normal text-center">{value ? <PriceRenderer price={value} additional={'USD'} /> : <PriceRenderer price={0} additional={'USD'} />}</span>
            case "orderPrice":
                return <div className="flex flex-col justify-center items-center gap-1">
                    <span className="font-normal text-center"><PriceRenderer price={row['usdPrice']} additional={'USD'} /></span>
                    <span className="font-normal  text-xs"><PriceRenderer price={value} additional={row['currency']} /></span>
                </div>
            case "followpStatus":
            case "orderStatus":
                return <Chip
                    className={`capitalize py-1 px-2 h-fit text-white ${statusColorMap[value?.toLowerCase().replace(' ', '')]}`}
                    variant="solid">
                    <div className="flex flex-row py-2 justify-evenly gap-1 items-center text-black dark:text-white">

                        <span className="inline-block">
                            {statusIconsMap[value?.toLowerCase().replace(' ', '')] && React.createElement(statusIconsMap[value?.toLowerCase().replace(' ', '')], {
                                className: "mr-2 ml-1",
                                size: 20,
                            })}
                        </span>
                        <span className="inline-block">
                            {
                                value || ''
                            }
                        </span>


                    </div>

                </Chip>
            default:
                return <span className="font-normal text-center">{value || '-'}</span>;
        }
    };
    useEffect(() => {
        const fetchData = async () => {
            if (tabsData && tabsData[currentStatus]) {
                setData(tabsData[currentStatus].data)
                return;
            }
            setLoading(true)
            const res = await dispatch(getServiceInvoiceStatusDetails({ id: invoiceId, status: currentStatus }))
            if (getServiceInvoiceStatusDetails.fulfilled.match(res)) {

                setTabsData(prevData => ({
                    ...prevData,
                    [currentStatus]: { data: res.payload }
                }));
                setData(res.payload)
            }
            setLoading(false)
        }
        if (currentStatus === 'Informations') {
            return
        } else {
            fetchData();
        }



    }, [currentStatus])
    const containerSubRef = useRef(); // Ref for the parent container
    const itemSubRefs = useRef([]); // Ref array for each nav item


    const handleTabClick = (slug, idx) => {
        setCurrentSubStatus(slug);

        // Scroll the clicked tab into view
        const element = itemSubRefs.current[idx];
        if (element) {
            element.scrollIntoView({
                behavior: "smooth", // Smooth scrolling
                block: "center",
                inline: "center",
            });
        }
    };


    return (
        <CustomModal
            isOpen={isOpen}
            onClose={onModalClose}
            width="max-w-6xl"
            showHeader={true}
            title={"Invoice N° " + (invoice ? invoice.invoiceNum : '...')}
            headerClassName="px-0 lg:px-6 py-3 border-b"
            bodyClassName="p-0 lg:p-6"
            showFooter={false}
            headerAdditionalItem={
                <Input
                    placeholder="Quick Search"
                    value={searchInput}
                    onChange={(e) => setsearchInput(e.target.value)}
                    className="w-full max-w-xs"
                    isClearable
                    onClear={() => setsearchInput('')}
                    startContent={<Search01Icon size={24} />

                    }
                />
            }
        >
            <Navigator navItems={possibleStatus} currentTab={currentStatus} setCurrentTab={setCurrentStatus} loading={loadingCounts} />
            {(currentStatus === 'confirmationCalls' || currentStatus === 'followupCalls') && (
                <div ref={containerSubRef} className={`w-fit max-w-[90%] mx-auto ${!loading ? 'bg-glb_blue' : ''} mt-2 rounded-lg hide-scrollbar overflow-x-auto flex justify-start items-start gap-4 p-1`}>
                    {loading ? (
                        // Show Skeleton or Loading Spinner while the sub-tab data is loading
                        Array.from({ length: 5 }).map((_, idx) => (
                            <SkeletonPulse key={idx} className="w-24 h-8" delay={idx * 0.2} />
                        ))
                    ) : (
                        possibleStatus.find(s => s.slug === currentStatus)?.subTabs
                            .map((subTab, idx) => {
                                // Determine which field to filter by based on the currentStatus
                                const statusField = currentStatus === 'confirmationCalls' ? 'orderStatus' : 'followpStatus';

                                // Filter the Data based on the statusField
                                const filteredData = Array.isArray(Data)
                                    ? Data.filter(d => d[statusField] && (d[statusField].trim().replace(/\s/g, '').toLowerCase() === subTab.slug.trim().replace(/\s/g, '').toLowerCase() || subTab.slug === 'all'))
                                    : [];

                                return (
                                    filteredData.length > 0 && (
                                        <div
                                            key={idx}
                                            ref={(el) => (itemSubRefs.current[idx] = el)}
                                            onClick={() => handleTabClick(subTab.slug, idx)}
                                            className={`relative p-1.5 text-nowrap cursor-pointer hover:opacity-50 rounded-lg flex items-center justify-center gap-1 ${currentSubStatus === subTab.slug ? "bg-white text-black" : "text-white"}`}
                                        >
                                            <span className="text-[15px] md:text-[15px]">
                                                {subTab.title}
                                            </span>
                                            <span className="text-xs text-white bg-glb_red rounded-full px-2 py-1">
                                                {filteredData.length}
                                            </span>
                                        </div>
                                    )
                                );
                            })
                    )}
                </div>
            )}




            {
                currentStatus === 'Informations' && (
                    <div className="grid lg:grid-cols-[40%_60%] w-full gap-1 mt-4">
                        {loading ? (
                            <div className="flex flex-col gap-2">
                                {Array.from({ length: 5 }).map((_, idx) => (
                                    <SkeletonPulse key={idx} className="h-5 w-full" delay={idx * 0.2} />
                                ))}
                            </div>
                        ) : (
                            <div className="flex rounded-lg flex-col overflow-hidden">
                                {invoice &&
                                    detailsColumns
                                        .filter((d) => d.showIn === 2 && d.key !== "totalRemittance")
                                        .map((d) => renderCell(d))}
                            </div>
                        )}
                        {loading ? (
                            <div className="flex flex-col gap-2">
                                {Array.from({ length: 5 }).map((_, idx) => (
                                    <SkeletonPulse key={idx} className="h-5 w-full" delay={idx * 0.2} />
                                ))}
                            </div>
                        ) : (
                            <div className="flex rounded-lg flex-col overflow-hidden">
                                {invoice && [
                                    renderCell(detailsColumns.find((d) => d.key === "totalRemittance")),
                                    ...detailsColumns
                                        .filter(
                                            (d) =>
                                                d.showIn === 3 &&
                                                d.key !== "netRemittance" &&
                                                d.key !== "totalRemittance"
                                        )
                                        .map((d) => renderCell(d)),
                                    renderCell(detailsColumns.find((d) => d.key === "netRemittance")),
                                ]}
                            </div>
                        )}
                    </div>
                )
            }
            {
                currentStatus !== 'Informations' &&
                <TableComponent
                    data={
                        (currentStatus === 'confirmationCalls'
                            ? (Array.isArray(Data)
                                ? Data.filter(d => {
                                    // If 'All' is selected, don't filter by orderStatus
                                    const statusMatch = currentSubStatus === 'all' || (d.orderStatus && d.orderStatus.trim().replace(/\s/g, '').toLowerCase() === currentSubStatus.trim().replace(/\s/g, '').toLowerCase());
                                    return statusMatch && d.orderNum && String(d.orderNum).toLowerCase().includes(searchInput.toLowerCase());
                                })
                                : []
                            )
                            : (currentStatus === 'followupCalls'
                                ? (Array.isArray(Data)
                                    ? Data.filter(d => {
                                        // If 'All' is selected, don't filter by followpStatus
                                        const statusMatch = currentSubStatus === 'all' || (d.followpStatus && d.followpStatus.trim().replace(/\s/g, '').toLowerCase() === currentSubStatus.trim().replace(/\s/g, '').toLowerCase());
                                        return statusMatch && d.orderNum && String(d.orderNum).toLowerCase().includes(searchInput.toLowerCase());
                                    })
                                    : []
                                )
                                : (Array.isArray(Data)
                                    ? Data.filter(d => d.orderNum && String(d.orderNum).toLowerCase().includes(searchInput.toLowerCase()))
                                    : []
                                )
                            )
                        ) || []
                    }
                    columns={possibleStatus.find(s => s.slug === currentStatus)?.columns || []}
                    loading={loading}
                    renderCell={renderTableCell}
                />


            }
        </CustomModal >
    );
};

export default InvoiceDetailsModal;