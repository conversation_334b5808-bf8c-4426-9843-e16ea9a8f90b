import PropTypes from 'prop-types';
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { ArrowDown01Icon, Cancel01Icon, GlobeIcon } from "hugeicons-react";
import { Spinner } from "@heroui/react";
import { countryNameToCode } from '../../../core/constants/countries';

export default function CountrySelector({
    id,
    open,
    disabled = false,
    onToggle,
    onChange,
    selectedValue,
    defaultSelectedKeys,
    COUNTRIES,
    loading,
    useMap = true,
    placeholder = "Select a country",
    useAll = true,
    onEndScroll,
}) {
    const ref = useRef(null);
    const listRef = useRef(null); // <-- Ref for scrollable list
    const [isLoadingMore, setIsLoadingMore] = useState(false);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (ref.current && !ref.current.contains(event.target) && open) {
                onToggle();
                setQuery("");
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [onToggle, open, ref]);

    const [query, setQuery] = useState("");

    // Handle scroll event to detect when user has scrolled to the bottom
    const handleScroll = (e) => {
        if (!onEndScroll || isLoadingMore) return; // Prevent multiple simultaneous requests

        const { scrollTop, scrollHeight, clientHeight } = e.target;
        // Check if scrolled to bottom (with a small threshold)
        if (scrollHeight - scrollTop <= clientHeight + 5) {
            setIsLoadingMore(true);
            onEndScroll()
                .then(() => {
                    // Small delay to prevent immediate triggering again
                    setTimeout(() => {
                        setIsLoadingMore(false);
                    }, 300);
                })
                .catch(() => {
                    setIsLoadingMore(false);
                });
        }
    };

    // Filter out the "All" option if useAll is false
    const filteredCountries = useAll
        ? COUNTRIES
        : COUNTRIES.filter(country => country.code !== 'all');



    return (
        <div ref={ref} className="">
            <div className="mt-1 relative">
                <button
                    type="button"
                    className={`
                    ${disabled ? "bg-neutral-100" : "bg-white dark:bg-base_dark"
                        } relative w-full border border-gray-300 dark:border-[#ffffff10] rounded-md shadow-sm pl-3 pr-10 py-2.5 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    aria-haspopup="listbox"
                    aria-expanded="true"
                    aria-labelledby="listbox-label"
                    onClick={onToggle}
                    disabled={disabled}
                >
                    <span className="truncate flex items-center">
                        {selectedValue && (
                            <button
                                type="button"
                                className="text-glb_red mr-2"
                                onClick={() => {
                                    // For multiple select, clear the internal selected value and trigger onChange with an empty array
                                    if (Array.isArray(selectedValue)) {
                                        onChange([]); // Trigger parent change
                                    } else {
                                        // For single select, clear the selected value and trigger onChange with null
                                        onChange(null); // Reset to null for single selection
                                    }
                                }}
                            >
                                <Cancel01Icon size={20} />
                            </button>
                        )}
                        {loading ? 'Loading...' :
                            selectedValue && selectedValue.name ? (
                                <>
                                    {useMap && selectedValue.code !== 'all' && (
                                        <img
                                            alt={`${selectedValue.code}`}
                                            src={`https://flagcdn.com/32x24/${selectedValue.flag ? selectedValue.flag.toLowerCase() : countryNameToCode[selectedValue.name.toLowerCase()] || ''}.png`}
                                            className={"inline mr-2 w-6 rounded-sm"}
                                            onError={(e) => {
                                                e.target.style.display = 'none';
                                                e.target.onerror = null;
                                            }}
                                        />
                                    )}
                                    {selectedValue.name}
                                </>
                            ) : defaultSelectedKeys ?
                                (
                                    <>
                                        {useMap && defaultSelectedKeys.code !== 'all' && (
                                            <img
                                                alt={`${defaultSelectedKeys.code}`}
                                                src={`https://flagcdn.com/32x24/${defaultSelectedKeys.flag ? defaultSelectedKeys.flag.toLowerCase() : countryNameToCode[defaultSelectedKeys.name.toLowerCase()] || ''}.png`}
                                                className={"inline mr-2 w-6 rounded-sm"}
                                                onError={(e) => {
                                                    e.target.style.display = 'none';
                                                    e.target.onerror = null;
                                                }}
                                            />
                                        )}
                                        {defaultSelectedKeys.name}
                                    </>
                                ) :
                                (
                                    <span className=" flex items-center">
                                        <GlobeIcon className="mr-2 h-4 w-4" />
                                        {placeholder}
                                    </span>
                                )}
                    </span>
                    <span
                        className={`absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none ${disabled ? "hidden" : ""
                            }`}
                    >
                        <ArrowDown01Icon />
                    </span>
                </button>

                <AnimatePresence>
                    {open && (
                        <motion.ul
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            transition={{ duration: 0.1 }}
                            className="absolute z-50 mt-1 w-full bg-white dark:bg-base_card shadow-lg max-h-80 rounded-md text-base ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                            tabIndex={-1}
                            role="listbox"
                            aria-labelledby="listbox-label"
                            aria-activedescendant="listbox-option-3"
                        >
                            <div className="sticky top-0 z-10 bg-white dark:bg-base_card ">
                                <li className=" text-gray-900 dark:text-gray-200 cursor-default select-none relative">
                                    <input
                                        type="search"
                                        name="search"
                                        autoComplete={"off"}
                                        className="focus:ring-blue-500 py-2 px-3 focus:border-gray-500 block w-full sm:text-sm border-gray-300 dark:border-[#ffffff10] rounded-md"
                                        placeholder={"Search a country"}
                                        onChange={(e) => setQuery(e.target.value)}
                                    />
                                </li>
                                <hr />
                            </div>

                            <div
                                ref={listRef}
                                className={
                                    "max-h-64 z-[100] scrollbar scrollbar-track-gray-100 scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-600 scrollbar-thumb-rounded scrollbar-thin overflow-y-scroll"
                                }
                                onScroll={onEndScroll ? handleScroll : undefined}
                            >
                                {loading && !filteredCountries.length ? <Spinner /> : filteredCountries.filter((country) =>
                                    country.name.toLowerCase().startsWith(query.toLowerCase())
                                ).length === 0 ? (
                                    <li className="text-gray-900 dark:text-gray-200 cursor-default select-none relative py-2 pl-3 pr-9">
                                        No countries found
                                    </li>
                                ) : (
                                    <>
                                        {filteredCountries.filter((country) =>
                                            country.name.toLowerCase().startsWith(query.toLowerCase())
                                        ).map((value, index) => {
                                            return (
                                                <li
                                                    key={`${id}-${index}`}
                                                    className="text-gray-900 dark:text-gray-200 cursor-default select-none relative py-2 pl-3 pr-9 flex items-center hover:bg-gray-50 dark:hover:bg-gray-800 transition"
                                                    id="listbox-option-0"
                                                    role="option"
                                                    onClick={() => {
                                                        onChange(value.id);
                                                        setQuery("");
                                                        onToggle();
                                                    }}
                                                >
                                                    {value.code !== 'all' && <img
                                                        alt={`${value.code}`}
                                                        src={`https://flagcdn.com/32x24/${value.flag ? value.flag.toLowerCase() : countryNameToCode[value.name.toLowerCase()] || ''}.png`}
                                                        className={"inline mr-2 h-4 rounded-sm"}
                                                        onError={(e) => {
                                                            e.target.style.display = 'none';
                                                            e.target.onerror = null;
                                                        }}
                                                    />
                                                    }
                                                    <span className={`${value.code === 'all' ? 'font-bold' : 'font-normal'}  truncate`}>
                                                        {value.name}
                                                    </span>
                                                    {(selectedValue && value.code === selectedValue.code) ? (
                                                        <span
                                                            className="text-blue-600 absolute inset-y-0 right-0 flex items-center pr-8">
                                                            <svg
                                                                className="h-5 w-5"
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                viewBox="0 0 20 20"
                                                                fill="currentColor"
                                                                aria-hidden="true"
                                                            >
                                                                <path
                                                                    fillRule="evenodd"
                                                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                                    clipRule="evenodd"
                                                                />
                                                            </svg>
                                                        </span>) : (!selectedValue && defaultSelectedKeys && value.code === defaultSelectedKeys.code) ? (
                                                            <span
                                                                className="text-blue-600 absolute inset-y-0 right-0 flex items-center pr-8">
                                                                <svg
                                                                    className="h-5 w-5"
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                    viewBox="0 0 20 20"
                                                                    fill="currentColor"
                                                                    aria-hidden="true"
                                                                >
                                                                    <path
                                                                        fillRule="evenodd"
                                                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                                        clipRule="evenodd"
                                                                    />
                                                                </svg>
                                                            </span>) : null}
                                                </li>
                                            );
                                        })}
                                        {isLoadingMore && (
                                            <li className="text-center py-2 text-gray-500 dark:text-gray-300">
                                                ...loading
                                            </li>
                                        )}
                                    </>
                                )}
                            </div>
                        </motion.ul>
                    )}
                </AnimatePresence>
            </div>
        </div>
    );
}

// PropTypes for validation
CountrySelector.propTypes = {
    id: PropTypes.string.isRequired,
    open: PropTypes.bool.isRequired,
    disabled: PropTypes.bool,
    onToggle: PropTypes.func.isRequired,
    onChange: PropTypes.func.isRequired,
    selectedValue: PropTypes.shape({
        code: PropTypes.string,
        name: PropTypes.string,
        flag: PropTypes.string,
        id: PropTypes.int,
    }),
    defaultSelectedKeys: PropTypes.shape({
        code: PropTypes.string,
        name: PropTypes.string,
        flag: PropTypes.string,
        id: PropTypes.int,
    }),
    COUNTRIES: PropTypes.array.isRequired,
    loading: PropTypes.bool,
    useMap: PropTypes.bool,
    placeholder: PropTypes.string,
    useAll: PropTypes.bool,
    onEndScroll: PropTypes.func,
};
