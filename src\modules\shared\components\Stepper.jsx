import React from 'react';

const StepStatus = {
    WAITING: 'waiting',
    ACTIVE: 'active',
    SUCCESS: 'success',
};

const Stepper = ({
    children,
    orientation = 'horizontal',
    stepContainerClasses = '',
    connectorClasses = '',
    ...props
}) => {
    return (
        <div
            className={`flex ${orientation === 'horizontal' ? 'flex-row' : 'flex-col'} flex-wrap w-fit items-center justify-center align-middle self-center gap-2 mx-auto ${stepContainerClasses}`}
            {...props}
        >
            {React.Children.map(children, (child, index) => (
                <React.Fragment key={index}>
                    {index > 0 && (
                        <div className={`${orientation === 'horizontal' ? 'h-0' : 'w-0'} border-1.5 border-dashed border-gray-300 mb-3 ${connectorClasses}`}>
                            {orientation === 'horizontal' ? (
                                <div className="w-10 h-1" />
                            ) : (
                                <div className="w-1 h-12" />
                            )}
                        </div>
                    )}
                    {child}
                </React.Fragment>
            ))}
        </div>
    );
};

const StepIndicator = ({
    indicatorClasses = '',
    color = 'bg-black/10 dark:bg-white/10',
    ...props
}) => {

    return (
        <div
            className={`
        w-12 h-12 rounded-full mx-auto
        flex items-center my-1 justify-center 
        text-lg font-bold ${color}
        ${indicatorClasses}
      `}
            {...props}
        >
            {/*{getIcon()}*/}
        </div>
    );
};

const Step = ({
    onClick,
    children,
    stepClasses = '',
    ...props
}) => {
    return (
        <div
            onClick={onClick}
            className={`
        flex flex-col
        items-start
        justify-center gap-2
        cursor-pointer
        ${stepClasses}
      `}
            {...props}
        >
            <div className="mt-2">{children}</div>
        </div>
    );
};

const StepDescription = ({
    children,
    descriptionClasses = '',
    ...props
}) => {
    return (
        <p className={`text-sm text-gray-600 block ${descriptionClasses}`} {...props}>
            {children}
        </p>
    );
};

const StepTitle = ({
    children,
    titleClasses = '',
    ...props
}) => {
    return (
        <p className={`font-medium block ${titleClasses}`} {...props}>
            {children}
        </p>
    );
};

export { Stepper, StepIndicator, StepStatus, Step, StepDescription, StepTitle };