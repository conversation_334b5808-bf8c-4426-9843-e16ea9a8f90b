import { set } from "lodash";

// Generate random Arabic product names
const arabicProducts = [
    "أيفون مع غلاف",
    "سماعات لاسلكية",
    "شاحن محمول",
    "حافظة هاتف",
    "ساعة ذكية"
];


export const ProductType = [
    { id: 1, name: "Smartphone" },
    { id: 2, name: "<PERSON><PERSON><PERSON>" },
    { id: 3, name: "Smartwatch" },
    { id: 4, name: "Tablet" },
    { id: 5, name: "Headphones" },
];
export const ProductCategory = [
    { id: 1, name: "Electronics" },
    { id: 2, name: "Accessories" },
    { id: 3, name: "Home Appliances" },
    { id: 4, name: "Fitness Gear" },
    { id: 5, name: "Audio Devices" },
];


// Generate random data
export const generateRandomData = (count) => {
    return Array.from({ length: count }, (_, index) => ({
        key: (index + 1).toString(),
        product: `Product ${index + 1}`,
        arabicName: arabicProducts[Math.floor(Math.random() * arabicProducts.length)],
        sku: Math.floor(Math.random() * 1000000).toString().padStart(6, '0'),
        type: ProductType[Math.floor(Math.random() * ProductType.length)].name,
        category: ProductCategory[Math.floor(Math.random() * ProductCategory.length)].name,
        status: index % 2 === 0 ? "Active" : "Deleted",
        actions: 'id'
    }));
};

export const generateRandomSalesPricesData = (count) => {
    const generatePrices = () => {
        const priceCount = Math.floor(Math.random() * 3) + 2; // Randomly generate between 1 to 4 prices
        return Array.from({ length: priceCount }, () => ({
            amount: (Math.random() * 100).toFixed(2), // Random price amount (0.00-100.00)
            cur: "USD" // Currency set to "USD"
        }));
    };

    return Array.from({ length: count }, (_, index) => ({
        key: (index + 1).toString(),
        offerName: `Offer ${index + 1}`,
        quantityPaid: Math.floor(Math.random() * 100) + 1, // Random quantity paid (1-100)
        quantityFree: Math.floor(Math.random() * 20), // Random quantity free (0-20)
        price: generatePrices() // Array of 1 to 4 price objects
    }));
};


export const GetTypeFromSet = (set = new set["1"]) => {
    return ProductType[parseInt(Array.from(set)[0], 10) - 1].name
}

export const GetTypeSetByName = (name) => {

    // Find the index of the object with the matching name
    const index = ProductType.findIndex((type) => type.name === name);

    // Return a Set containing the index as a string
    return index !== -1 ? new Set([(index + 1).toString()]) : new Set();
}

export const GetCategoryFromSet = (set = new set["1"]) => {
    return ProductCategory[parseInt(Array.from(set)[0], 10) - 1].name
}

export const GetCategorySetByName = (name) => {
    // Find the index of the object with the matching name
    const index = ProductCategory.findIndex((type) => type.name === name);

    // Return a Set containing the index as a string
    return index !== -1 ? new Set([(index + 1).toString()]) : new Set();
}