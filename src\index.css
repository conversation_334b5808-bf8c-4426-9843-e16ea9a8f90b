@tailwind base;
@tailwind components;
@tailwind utilities;

/* Font Customization */
@layer base {
    @font-face {
        font-family: 'Gotham';
        font-weight: 900;
        src: url('@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Black.otf') format('opentype');
    }

    @font-face {
        font-family: 'Gotham';
        font-weight: 700;
        src: url('@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Bold.otf') format('opentype');
    }

    @font-face {
        font-family: 'Gotham';
        font-weight: 500;
        src: url('@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Medium.otf') format('opentype');
    }

    @font-face {
        font-family: 'Gotham';
        font-weight: 400;
        src: url('@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Book.otf') format('opentype');
    }

    @font-face {
        font-family: 'Gotham';
        font-weight: 300;
        src: url('@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Light.otf') format('opentype');
    }
    @font-face {
        font-family: 'Gotham';
        font-weight: 100;
        src: url('@assets/fonts/Gotham-font-family-full/Gotham/Gotham-Thin.otf') format('opentype');
    }
}

*{
    box-sizing: border-box;
    font-family: 'Gotham', sans-serif;
}


input[type=range] {
    -webkit-appearance: none; /* Remove default styling */
    height: 8px;
    background: transparent; /* make it transparent for custom track */
  }
  
  /* Track for WebKit browsers (Chrome, Safari) */
  input[type=range]::-webkit-slider-runnable-track {
    height: 8px;
    background: #005FFF; /* your track background color */
    border-radius: 4px;
  }
  
  /* Track for Firefox */
  input[type=range]::-moz-range-track {
    height: 8px;
    background: #005FFF;
    border-radius: 4px;
  }
  
  /* Thumb styling (the draggable handle) */
  input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #ffffff;
    border: 2px solid #005FFF;
    border-radius: 5px;
    cursor: pointer;
    margin-top: -6px; /* align thumb vertically */
  }
  
  input[type=range]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #ffffff;
    border: 2px solid #005FFF;
    border-radius: 5px;
    cursor: pointer;
  }
  



input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
body {
    width:100vw;
    overflow-x:hidden;
}

header {
	padding: 0 !important;
}

.hide-scrollbar {
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
::-webkit-scrollbar-track-piece{
	background-color:#005FFF30;
	-webkit-border-radius:0;
}
::-webkit-scrollbar{
	width:8px;
	height:8px;
}
::-webkit-scrollbar-thumb{
	height:50px;
	background-color:#999;
	-webkit-border-radius:4px;
	outline:2px solid #005FFF00;
	outline-offset:-2px;
	border: 2px solid #005FFF00;
}
::-webkit-scrollbar-thumb:hover{
	height:50px;
	background-color:#005FFF;
	-webkit-border-radius:4px;
}
.dark ::-webkit-scrollbar-track-piece{
	background-color:#141618;
	-webkit-border-radius:0;
}
.dark ::-webkit-scrollbar{
	width:8px;
	height:8px;
}
.dark ::-webkit-scrollbar-thumb{
	height:50px;
	background-color:#999;
	-webkit-border-radius:4px;
	outline:2px solid #141618;
	outline-offset:-2px;
	border: 2px solid #141618;
}
.dark ::-webkit-scrollbar-thumb:hover{
	height:50px;
	background-color:#005FFF;
	-webkit-border-radius:4px;
}

/* Duplicate styles for a specific class, e.g., .custom-scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    height: 50px;
    background-color: #005FFF;
    -webkit-border-radius: 4px;
    outline: 2px solid #005FFF30;
    outline-offset: -2px;
    border: 2px solid #005FFF30;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    height: 50px;
    background-color: #005FFF30;
    -webkit-border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track-piece {
    background-color: #005FFF30;
    -webkit-border-radius: 0;
}

/* Dark mode styles for the custom scrollbar */
.custom-scrollbar.dark::-webkit-scrollbar-track-piece {
    background-color: #141618;
    -webkit-border-radius: 0;
}

.custom-scrollbar.dark::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.custom-scrollbar.dark::-webkit-scrollbar-thumb {
    height: 50px;
    background-color: #005FFF;
    -webkit-border-radius: 4px;
    outline: 2px solid #141618;
    outline-offset: -2px;
    border: 2px solid #141618;
}

.custom-scrollbar.dark::-webkit-scrollbar-thumb:hover {
    height: 50px;
    background-color: #005FFF;
    -webkit-border-radius: 4px;
}

/* Firefox-specific scrollbar styling */
body {
    scrollbar-width: thin; /* Options: auto, thin, none */
    scrollbar-color: #999 #005FFF00; /* thumb color, track color */
}

/* Dark mode for Firefox */
body.dark {
    scrollbar-width: thin;
    scrollbar-color: #999 #141618;
}
/* Firefox-specific scrollbar styling */
.custom-scrollbar {
    scrollbar-width: thin; /* Options: auto, thin, none */
    scrollbar-color: #005FFF #005FFF30; /* thumb color, track color */
}

/* Dark mode for Firefox */
.custom-scrollbar.dark {
    scrollbar-width: thin;
    scrollbar-color: #005FFF #141618;
}