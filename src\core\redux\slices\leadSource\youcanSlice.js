import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { youcanInstallUrl } from "../URLs";
import { getToken } from "../../../services/TokenHandler";
import axios from "axios";

// Async thunk for installing youcan app
export const installYouCanApp = createAsyncThunk(
    "youcan/install",
    async (_, { rejectWithValue }) => {  // token is passed as argument

        try {
            const response = await axios.get(youcanInstallUrl,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );



            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error installing YouCan' });

            }
            return response.data.result;

        } catch (error) {
            console.log("error", error);
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);


const youcanSlice = createSlice({
    name: "youcan",
    initialState: {
        loading: false,
        error: null,
        installUrl: null,
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(installYouCanApp.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(installYouCanApp.fulfilled, (state, action) => {
                state.loading = false;
                state.installUrl = action.payload;
            })
            .addCase(installYouCanApp.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload; // Error message from Laravel backend
            });
    },
});

export default youcanSlice.reducer;
