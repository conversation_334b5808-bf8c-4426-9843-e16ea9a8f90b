import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { forceimportOrdersGoogleSheetUrl, importOrdersGoogleSheetUrl, installGoogleSheetUrl, lightFunnelsStateSavingUrl } from "../URLs";
import axios from "axios";
import { getToken } from "../../../services/TokenHandler";



// Async thunk for installing Light Funnels app
export const installGoogleSheet = createAsyncThunk(
    "googleSheet/install",
    async (_, { rejectWithValue }) => {


        try {

            // Call the API to store the state in session (GET request with query params)
            const response = await axios.post(installGoogleSheetUrl, {}, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response === "error") {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || "Error saving state" });
            }

            return response.data;

        } catch (error) {
            console.log(error);

            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || "Error saving state" });
        }

    }
);

// Async thunk for installing Light Funnels app
export const importOrderFromGoogleSheet = createAsyncThunk(
    "googleSheet/importOrder",
    async ({ spreadSheetId, sheetName }, { rejectWithValue }) => {

        try {
            // Call the API to import orders from Google Sheet             
            const response = await axios.post(
                `${importOrdersGoogleSheetUrl}?spreadsheetId=${encodeURIComponent(spreadSheetId)}&sheetName=${encodeURIComponent(sheetName)}`, {},

                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );
            console.log('res', response);

            return response.data;
        } catch (error) {

            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || "Error importing orders" });
        }
    }
);
// Async thunk for installing Light Funnels app
export const forceImportOrderFromGoogleSheet = createAsyncThunk(
    "googleSheet/ForceImportOrder",
    async ({ spreadSheetId }, { rejectWithValue }) => {

        try {
            // Call the API to import orders from Google Sheet             
            const response = await axios.post(
                `${forceimportOrdersGoogleSheetUrl}${spreadSheetId}`, {},

                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );
            console.log('res', response);

            return response.data;
        } catch (error) {

            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || "Error importing orders" });
        }
    }
);


const googleSheetSlice = createSlice({
    name: "googleSheet",
    initialState: {
        loading: false,
        actionType: null,
        error: null,
        installUrl: null,
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(installGoogleSheet.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(installGoogleSheet.fulfilled, (state, action) => {
                state.loading = false;

            })
            .addCase(installGoogleSheet.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload; // Error message from Laravel backend
            });
    },
});

export default googleSheetSlice.reducer;
