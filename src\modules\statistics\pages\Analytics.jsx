// src/modules/dashboard/pages/Analytics.jsx

import React, { useState } from "react";
import {
  ArrowDown01Icon,
  ArrowUpDownIcon,
  Calendar03Icon,
  CallEnd03Icon,
  Dollar01Icon,
  FilterIcon,
  PercentIcon,
  Settings01Icon,
  Shield01Icon,
  ShippingTruck01Icon,
  ShoppingBasket01Icon,
  ShoppingBasket03Icon,
  ShoppingCart01Icon,
  Airplane01Icon,
} from "hugeicons-react";
import { VerticalBarChart } from "@/modules/dashboard/components/BarChartCard.jsx";
import { Button } from "@heroui/button";
import TotalFees from "@/modules/statistics/components/TotalFees.jsx";
import AnalyticsCard from "@/modules/statistics/components/AnalyticsCard.jsx";
import DashboardLayout from "../../shared/layouts/DashboardLayout";

import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem
} from "@heroui/dropdown";

import DateRangeDropdown from "../../shared/components/DateRangeDropdown.jsx";
import FilterModal from "../components/FilterModal.jsx";

const statisticCardsData = [
  { title: "Entered Fees", amount: 2354, icon: ShoppingBasket01Icon, bgColor: "bg-info", hoverColor: "hover:bg-info/20 dark:hover:bg-info/20" },
  { title: "Confirmed Fees", amount: 1892, icon: Shield01Icon, bgColor: "bg-[#C324EB]", hoverColor: "hover:bg-[#C324EB]/20 dark:hover:bg-[#C324EB]/20" },
  { title: "Shipping Cost", amount: 1723, icon: Dollar01Icon, bgColor: "bg-[#9D5704]", hoverColor: "hover:bg-[#9D5704]/20 dark:hover:bg-[#9D5704]/20" },
  { title: "VAT/Duty & Clearance", amount: 2145, icon: PercentIcon, bgColor: "bg-[#9D9604]", hoverColor: "hover:bg-[#9D9604]/20 dark:hover:bg-[#9D9604]/20" },
  { title: "Up/Down/Cross Sell", amount: 1680, icon: ArrowUpDownIcon, bgColor: "bg-[#ED0006]", hoverColor: "hover:bg-[#ED0006]/20 dark:hover:bg-[#ED0006]/20" },
  { title: "COD Fees", amount: 1934, icon: ShippingTruck01Icon, bgColor: "bg-[#3424EB]", hoverColor: "hover:bg-[#3424EB]/20 dark:hover:bg-[#3424EB]/20" },
];

export default function Analytics() {
  const [showFilterModal, setShowFilterModal] = useState(false);

  const [dateRange, setDateRange] = useState({
    startDate: new Date("2023-10-28"),
    endDate: new Date("2024-01-01"),
  });

  const [timeRange, setTimeRange] = useState("Monthly");

  const barChartData = [
    { label: "Sun 12", value: 1600, color: "#0258E8" },
    { label: "Mon 13", value: 2550, color: "#ED0006" },
    { label: "Tue 14", value: 2300, color: "#0258E8" },
    { label: "Wed 15", value: 2500, color: "#ED0006" },
    { label: "Thu 16", value: 2700, color: "#0258E8" },
    { label: "Fri 17", value: 2800, color: "#ED0006" },
    { label: "Sat 18", value: 2590, color: "#0258E8" },
  ];

  return (
    <>
      <DashboardLayout
        title="Analytics"
        actions={
          <div className="flex flex-row gap-2 flex-1 justify-end">

            {/* Date Range dropdown (DateRangeDropdown) */}
            <DateRangeDropdown
              initialStartDate={dateRange.startDate}
              initialEndDate={dateRange.endDate}
              onRangeChange={({ startDate, endDate }) => {
                setDateRange({ startDate, endDate });
                console.log("Selected date range:", startDate, "->", endDate);
              }}
            />

            {/* Time Range dropdown (Daily / Monthly / Yearly) */}
            <Dropdown>
              <DropdownTrigger>
                <Button
                  variant="bordered"
                  className="rounded-full text-xs p-2 md:text-base md:p-4 border-[#444444] border-1"
                >
                  {timeRange}
                  <ArrowDown01Icon className="ml-1" />
                </Button>
              </DropdownTrigger>
              <DropdownMenu
                aria-label="Time Range"
                onAction={(key) => setTimeRange(key)}
              >
                <DropdownItem key="Daily">Daily</DropdownItem>
                <DropdownItem key="Monthly">Monthly</DropdownItem>
                <DropdownItem key="Yearly">Yearly</DropdownItem>
              </DropdownMenu>
            </Dropdown>

            {/* Filter button -> open the modal */}
            <Button
              variant="solid"
              className="rounded-full text-xs p-2 md:text-base md:p-4 bg-glb_red text-white"
              onPress={() => setShowFilterModal(true)}
            >
              <FilterIcon size={18} />
              Filter
            </Button>
          </div>
        }
      >
        <div className="flex flex-col lg:flex-row">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-2 my-4 w-full lg:w-1/2">
            {statisticCardsData.map((item, index) => (
              <AnalyticsCard
                key={index}
                isExpanded={true}
                icon={item.icon}
                iconBg={item.bgColor}
                sales={item.amount}
                title={item.title}
                hoverColor={item.hoverColor}
              />
            ))}
          </div>
          <div className="w-full lg:w-1/2 p-4">
            <TotalFees />
          </div>
        </div>

        <div className="w-full p-4">
          <h2 className="text-2xl font-bold my-4">Profits</h2>
          <VerticalBarChart chartData={barChartData} />
        </div>
      </DashboardLayout>

      {/* Filter Modal (toggle via showFilterModal) */}
      <FilterModal
        open={showFilterModal}
        onClose={() => setShowFilterModal(false)}
      />
    </>
  );
}