import { ArrowUpRight01Icon, File02Icon, MapsIcon, ViewIcon } from "hugeicons-react";
import { Link } from "react-router-dom";
import DashboardLayout from "../../shared/layouts/DashboardLayout";

const HelpSection = () => {
    return (
        <DashboardLayout title="Help" hasReturnLink={true} bgColor="bg-greyish">


            <div
                className="flex flex-row flex-wrap lg:flex-nowrap items-stretch justify-center gap-6 lg:max-w-2xl mx-auto my-24">
                {/* Tariff Guide Card */}
                <div
                    className="bg-white dark:bg-zinc-950 border border-gray-200 dark:border-zinc-900 hover:bg-gray-200/20 dark:hover:bg-zinc-900/40 transition-colors inline-block w-full md:w-1/2 rounded-lg">
                    <div className="p-8">
                        <div className="flex flex-col justify-center h-full">
                            <div className="mb-4 flex flex-col justify-center items-center gap-6">
                                <File02Icon
                                    className="text-blue-500"
                                    size={36}
                                />
                                <h2 className="text-lg font-bold text-center">Tarif Guide</h2>
                                <p className="text-gray-500 text-sm mt-1 text-center">
                                    Provide details about shipping services, so you can make informed decision about
                                    costs
                                </p>
                            </div>

                            <Link to={""}
                                className="rounded-full font-semibold border border-gray-200 dark:border-zinc-800 transition-colors ease-in-out dark:hover:bg-zinc-800/60 hover:bg-info hover:text-white dark:bg-zinc-900 text-sm flex flex-row py-3 items-center justify-center gap-2 shadow-sm"
                            >
                                <ViewIcon size={20} />
                                See Details
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Help Center Card */}
                <div
                    className="bg-white dark:bg-zinc-950 border border-gray-200 dark:border-zinc-900 hover:bg-gray-200/20 dark:hover:bg-zinc-900/40 transition-colors inline-block  w-full md:w-1/2 rounded-lg"
                >
                    <div className="p-8">
                        <div className="flex flex-col justify-center h-full">
                            <div className="mb-4 flex flex-col justify-center items-center gap-6">
                                <MapsIcon
                                    className="text-blue-500"
                                    size={36}
                                />
                                <h2 className="text-lg font-bold text-center">Help Center</h2>
                                <p className="text-gray-500 text-sm mt-1 text-center">
                                    Provide details about shipping services, so you can make informed decision about
                                    costs
                                </p>
                            </div>

                            <Link to={""}
                                className="rounded-full font-semibold  hover:bg-info hover:text-white border border-gray-200 dark:border-zinc-800 transition-colors ease-in-out dark:hover:bg-zinc-800/60 dark:bg-zinc-900 text-sm flex flex-row py-3 items-center justify-center gap-2 shadow">
                                Open Help Center
                                <ArrowUpRight01Icon size={20} />
                            </Link>
                        </div>
                    </div>
                </div>
            </div>

        </DashboardLayout>
    );
};

export default HelpSection;