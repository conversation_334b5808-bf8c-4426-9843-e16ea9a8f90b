import React, { useEffect, useRef, useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@heroui/button";
import codPowerGroupLogo from "@shared/assets/images/logo-png.png"; //cod-power-group-logo.svg";
import codPowerGroupLogoDark from "@shared/assets/images/logo-png-dark.png"; //cod-power-group-logo-dark.svg";
import {
    SidebarLeft01Icon,
    MoonCloudIcon,
    Settings02Icon,
    Share08Icon,
    HelpCircleIcon,
    Sun02Icon,
    Moon02Icon,
} from "hugeicons-react";
import ThemeToggle from "@/modules/dashboard/components/ThemeToggle.jsx";
import { useThemeProvider } from "../../../../core/providers/ThemeContext";
import { motion } from "framer-motion";
import { useSelector, useDispatch } from 'react-redux';
import { RouteNames, RoutesConfig } from "../../../../core/routes/routes";
import { setShowSidebar } from "../../../../core/redux/slices/sidebarSlice";
import { getServiceInvoiceCount, getSourcingInvoiceCount } from "../../../../core/redux/slices/invoices/invoiceSlice";
import { usePermissions } from "../../../../core/providers/PermissionContext";
import { resetParams as resetOrderParams } from "../../../../core/redux/slices/orders/ordersManagementSlice";
import { resetParams as resetProductParams } from "../../../../core/redux/slices/stock/products/productsSlice";
import { resetParams as resetInvoiceParams } from "../../../../core/redux/slices/invoices/invoiceSlice";
import { resetFilterParams as resetDashboardParams } from "../../../../core/redux/slices/dachboard/dashboardSlice";
import { resetParams as resetSourcingParams } from "../../../../core/redux/slices/sourcing/sourcingSlice";

export default function Sidebar() {
    const location = useLocation();
    const { currentTheme } = useThemeProvider();
    const { pathname } = location;

    // Use our permission hook to get permissions
    const { permissions, hasPermission } = usePermissions();

    const sidebar = useRef(null);
    const [expandedRoutes, setExpandedRoutes] = useState({});
    const [scrollPosition, setScrollPosition] = useState(0);

    const navigate = useNavigate();



    // reduuux
    // reduuuux
    const dispatch = useDispatch();
    const sidebarEpingled = useSelector((state) => state.sidebar.sidebarEpingled);
    const { loadingServiceCount, loadingSourcingCount, invoiceServiceCount, invoiceSourcingCount } = useSelector((state) => state.invoices);
    const showSidebar = useSelector((state) => state.sidebar.sidebarEpingled ? true : state.sidebar.showSidebar);
    const HandleSetShowSidebar = (v) => {
        dispatch(setShowSidebar(v));
    }

    // Create a mapping of routes to their reset actions
    const routeResetActions = {
        [RouteNames.allOrders]: resetOrderParams,
        [RouteNames.allProducts]: resetProductParams,
        [RouteNames.serviceInvoices]: resetInvoiceParams,
        [RouteNames.sourcingInvoices]: resetInvoiceParams,
        [RouteNames.dashboard]: resetDashboardParams,
        [RouteNames.SourcingRequest]: resetSourcingParams,
        [RouteNames.followUp]: resetOrderParams,
        // Add more routes as needed
    };

    // Generic handler for any route with filter params
    const handleFilteredRouteClick = (e, path) => {
        console.log(path, routeResetActions[path]);

        e.preventDefault();
        if (routeResetActions[path]) {
            dispatch(routeResetActions[path]()).then(() => {
                navigate(path);
            });
        } else {
            // For routes without reset actions, just navigate
            navigate(path);
        }
    };

    useEffect(() => {
        dispatch(getServiceInvoiceCount())
        dispatch(getSourcingInvoiceCount())
    }, [dispatch])


    const toggleRoute = (routePath) => {
        setExpandedRoutes((prev) => ({
            ...prev,
            [routePath]: !prev[routePath],
        }));
    };



    useEffect(() => {
        RoutesConfig.forEach((route) => {
            if (route.children) {
                const isParentActive = route.children.some((child) => {
                    const isChildActive =
                        pathname === child.path || pathname.startsWith(`${child.path}/`);
                    const isGrandchildActive =
                        child.children &&
                        child.children.some(
                            (grandChild) =>
                                pathname === grandChild.path ||
                                pathname.startsWith(`${grandChild.path}/`)
                        );

                    return isChildActive || isGrandchildActive;
                });

                if (isParentActive) {
                    setExpandedRoutes((prev) => ({
                        ...prev,
                        [route.path]: true,
                    }));
                }

                route.children.forEach((child) => {
                    const isGrandchildActive =
                        child.children &&
                        child.children.some(
                            (grandChild) =>
                                pathname === grandChild.path ||
                                pathname.startsWith(`${grandChild.path}/`)
                        );

                    if (isGrandchildActive) {
                        setExpandedRoutes((prev) => ({
                            ...prev,
                            [child.path]: true,
                        }));
                    }
                });
            }
        });
    }, [pathname]);

    // Close sidebar on ESC key press
    useEffect(() => {
        const keyHandler = ({ keyCode }) => {
            if (!showSidebar || keyCode !== 27) return;
            HandleSetShowSidebar(false);
        };
        document.addEventListener("keydown", keyHandler);
        return () => document.removeEventListener("keydown", keyHandler);
    }, [showSidebar, HandleSetShowSidebar]);

    useEffect(() => {
        localStorage.setItem("sidebar-expanded", showSidebar);
        if (showSidebar) {
            document.querySelector("body").classList.add("sidebar-expanded");
        } else {
            document.querySelector("body").classList.remove("sidebar-expanded");
        }
    }, [showSidebar]);



    useEffect(() => {
        if (showSidebar) {
            sidebar.current.scrollTop = scrollPosition;
        } else {
            setScrollPosition(sidebar.current.scrollTop);
        }
    }, [showSidebar, scrollPosition]);

    const scrollToItem = (route) => {
        const element = document.getElementById(route);
        element?.scrollIntoView({ behavior: "instant", block: "center" });
    };

    const expandCurrentRouteOnly = () => {
        let newExpandedRoutes = {};

        RoutesConfig.forEach((route) => {
            if (route.children && route.children.some((child) => pathname.startsWith(child.path))) {
                scrollToItem(route.path);
                newExpandedRoutes[route.path] = true;
                route.children.forEach((child) => {
                    if (pathname === child.path || pathname.startsWith(`${child.path}/`)) {
                        newExpandedRoutes[child.path] = true;
                    }
                });
            }
        });

        setExpandedRoutes(newExpandedRoutes);
    };

    useEffect(() => {
        if (showSidebar) {
            expandCurrentRouteOnly();
        } else {
            setExpandedRoutes({});
        }

    }, [showSidebar, pathname]);

    const pathMapWithNotif = {};

    RoutesConfig.forEach((route) => {

        if (route.children) {
            route.children.forEach((child) => {
                const childPath = child.path;

                // Ensure notification number is displayed for invoice-related routes
                if (childPath === "/services-invoices" || childPath === "/sourcing-invoices") {
                    pathMapWithNotif[childPath] = {
                        notifNum: childPath === "/services-invoices" ? invoiceServiceCount : invoiceSourcingCount,
                    };
                }
            });
        }
    });

    return (
        <motion.div
            initial={{ width: "3.5rem" }} // Initial width (collapsed state)
            animate={{ width: showSidebar ? "20rem" : "3.5rem" }} // Width based on showSidebar
            exit={{ width: "3.5rem" }} // Exit width (collapsed state)
            transition={{ duration: 0.2 }} // Animation duration
            ref={sidebar}
            onMouseEnter={() => HandleSetShowSidebar(true)} // Expand sidebar
            onMouseLeave={() => HandleSetShowSidebar(false)} // Collapse sidebar
            className={`hidden lg:block flex-shrink-0 left-0 top-0 lg:max-w-72 xl:max-w-80 bottom-0
                overflow-x-hidden bg-base_light dark:bg-transparent border-r border-gray-200
                dark:border-[#ffffff10] z-[51] overflow-y-auto max-h-screen hide-scrollbar`}
        >
            <div
                className={`flex justify-between items-center ${showSidebar ? "my-6 px-6" : "my-6 px-2"
                    } h-10`}
            >
                <Link to="/dashboard">
                    {currentTheme === "light" ? (
                        <img
                            src={codPowerGroupLogo}
                            alt="power group world logo"
                            className="w-36"
                        />
                    ) : (
                        <img
                            src={codPowerGroupLogoDark}
                            alt="power group world logo"
                            className="w-36"
                        />
                    )}
                </Link>

            </div>

            <div className={`${showSidebar ? "px-4 my-12" : "p-0 my-12"}`}>
                <h3
                    className={`my-2 ${showSidebar ? "" : "text-start ml-[1.3rem]"
                        } text-gray-600`}
                >
                </h3>
                <ul
                    initial="hidden"
                    animate="visible"
                    className="flex flex-col gap-2 dark:text-gray-400 text-gray-600"
                >
                    {RoutesConfig.filter((route) => {
                        // Check if this route should be shown based on permissions
                        if (route.requiredPermission) {
                            return hasPermission(route.requiredPermission) && route.showInSidebar && route.path;
                        }

                        const shouldShow = typeof route.showInSidebar === 'function'
                            ? route.showInSidebar
                            : route.showInSidebar;
                        return shouldShow && route.path;
                    }).map(
                        (route) => {
                            const isActiveParent =
                                route.children &&
                                route.children.some((child) => {
                                    const isChildActive =
                                        pathname === child.path ||
                                        pathname.startsWith(`${child.path}/`);
                                    const isGrandchildActive =
                                        child.children &&
                                        child.children.some(
                                            (grandChild) =>
                                                pathname === grandChild.path ||
                                                pathname.startsWith(`${grandChild.path}/`)
                                        );

                                    return isChildActive || isGrandchildActive;
                                });

                            return (
                                <li key={route.path}>
                                    {route.children ? (
                                        <div id={route.path}>
                                            <button
                                                id={route.path}
                                                onClick={() => toggleRoute(route.path)}
                                                className={`flex ${showSidebar
                                                    ? "w-full justify-between"
                                                    : "w-[50px] ml-1 justify-start"
                                                    } items-center py-2 rounded-xl hover:bg-dark_selected_hover hover:text-black hover:dark:text-white
                                                ${isActiveParent || pathname === route.path
                                                        ? "bg-glb_blue text-white"
                                                        : ""
                                                    }`}
                                            >
                                                <div
                                                    className={`flex w-full items-center text-ellipsis overflow-hidden ${showSidebar
                                                        ? "px-2"
                                                        : "justify-center"
                                                        }`}
                                                >
                                                    {showSidebar ? (
                                                        <>
                                                            {React.createElement(route.icon, {
                                                                className: "mr-2 ml-1",
                                                                size: 20,
                                                            })}

                                                            {/* initial={{ opacity: 0 }}
                                                                animate={{ opacity: 1, transition: { ease: 'easeIn', delay: 0.1, duration: 0.1 } }}
                                                                exit={{ opacity: 0 }} */}
                                                            <motion.h4
                                                                className="text-ellipsis overflow-hidden"
                                                            >
                                                                {route.name}
                                                            </motion.h4>
                                                        </>
                                                    ) : (
                                                        React.createElement(route.icon, { size: 20 })
                                                    )}
                                                </div>
                                            </button>
                                            {expandedRoutes[route.path] && showSidebar && (
                                                <ul className="pl-4">
                                                    {route.children.map((child) => {
                                                        const isActiveChild =
                                                            pathname === child.path ||
                                                            pathname.startsWith(
                                                                `${child.path}/`
                                                            );
                                                        const isActiveGrandchild =
                                                            child.children &&
                                                            child.children.some(
                                                                (grandChild) =>
                                                                    pathname === grandChild.path ||
                                                                    pathname.startsWith(
                                                                        `${grandChild.path}/`
                                                                    )
                                                            );

                                                        // Check if this child should be shown based on permissions
                                                        // Check for requiredPermission property
                                                        const shouldShowChild = child.requiredPermission
                                                            ? hasPermission(child.requiredPermission) && child.showInSidebar
                                                            : typeof child.showInSidebar === 'function'
                                                                ? child.showInSidebar
                                                                : child.showInSidebar;

                                                        // Skip rendering this child if it shouldn't be shown
                                                        if (!shouldShowChild) return null;

                                                        return (
                                                            <li key={child.path} className="ml-6">
                                                                {child.children ? (
                                                                    <>
                                                                        <button
                                                                            onClick={() =>
                                                                                toggleRoute(child.path)
                                                                            }
                                                                            className={`flex w-full justify-between items-center px-11 py-2 rounded-xl ${isActiveChild ||
                                                                                isActiveGrandchild
                                                                                ? "text-dark_selected"
                                                                                : "text-gray-600 dark:text-white"
                                                                                } hover:text-glb_blue dark:hover:text-blue-400`}
                                                                        >
                                                                            <div className="flex items-center">
                                                                                {child.name}
                                                                            </div>
                                                                        </button>
                                                                        {expandedRoutes[child.path] &&
                                                                            showSidebar && (
                                                                                <ul className="pl-20">
                                                                                    {child.children.map(
                                                                                        (grandChild) => (
                                                                                            <li
                                                                                                key={grandChild.path}
                                                                                                className={`flex justify-between items-center ml-2 px-[10px] py-2 rounded-xl ${pathname ===
                                                                                                    grandChild.path
                                                                                                    ? "text-dark_selected"
                                                                                                    : "text-gray-600 dark:text-white"
                                                                                                    } hover:text-glb_blue dark:hover:text-blue-400`}
                                                                                            >
                                                                                                <Link
                                                                                                    to={grandChild.path}
                                                                                                    className="flex w-full items-center"
                                                                                                >
                                                                                                    {grandChild.name}
                                                                                                </Link>
                                                                                            </li>
                                                                                        )
                                                                                    )}
                                                                                </ul>
                                                                            )}
                                                                    </>
                                                                ) : Object.keys(routeResetActions).includes(child.path) ?
                                                                    (
                                                                        child.showInSidebar && (<Link
                                                                            id={child.path}
                                                                            to="#"
                                                                            onClick={(e) => handleFilteredRouteClick(e, child.path)}
                                                                            className={`flex w-full items-center px-2 py-2 rounded-xl ${pathname === child.path
                                                                                ? "text-dark_selected"
                                                                                : "text-gray-600 dark:text-white "
                                                                                } hover:text-glb_blue dark:hover:text-blue-400`}
                                                                        >
                                                                            <div
                                                                                className={` flex justify-center text-[11px] items-center p-1 rounded-full ${pathMapWithNotif && pathMapWithNotif[
                                                                                    child.path
                                                                                ] &&
                                                                                    pathMapWithNotif[
                                                                                        child.path
                                                                                    ].notifNum !== undefined
                                                                                    ? "bg-glb_red"
                                                                                    : ""
                                                                                    }  w-6 h-6 mr-3 text-white`}
                                                                            >
                                                                                {pathMapWithNotif && pathMapWithNotif[
                                                                                    child.path
                                                                                ] &&
                                                                                    pathMapWithNotif[
                                                                                        child.path
                                                                                    ].notifNum !== undefined
                                                                                    ? pathMapWithNotif[
                                                                                        child.path
                                                                                    ].notifNum
                                                                                    : ""}
                                                                            </div>
                                                                            {child.name}
                                                                        </Link>)
                                                                    ) : child.showInSidebar &&
                                                                    (<Link
                                                                        to={child.path}
                                                                        className={`flex w-full items-center px-2 py-2 rounded-xl ${pathname === child.path
                                                                            ? "text-dark_selected"
                                                                            : "text-gray-600 dark:text-white "
                                                                            } hover:text-glb_blue dark:hover:text-blue-400`}
                                                                    >
                                                                        <div
                                                                            className={` flex justify-center text-[11px] items-center p-1 rounded-full ${pathMapWithNotif && pathMapWithNotif[
                                                                                child.path
                                                                            ] &&
                                                                                pathMapWithNotif[
                                                                                    child.path
                                                                                ].notifNum !== undefined
                                                                                ? "bg-glb_red"
                                                                                : ""
                                                                                }  w-6 h-6 mr-3 text-white`}
                                                                        >
                                                                            {pathMapWithNotif && pathMapWithNotif[
                                                                                child.path
                                                                            ] &&
                                                                                pathMapWithNotif[
                                                                                    child.path
                                                                                ].notifNum !== undefined
                                                                                ? pathMapWithNotif[
                                                                                    child.path
                                                                                ].notifNum
                                                                                : ""}
                                                                        </div>
                                                                        {child.name}
                                                                    </Link>)
                                                                }
                                                            </li>
                                                        );
                                                    })}
                                                </ul>
                                            )}
                                        </div>
                                    ) : (
                                        <Link
                                            id={route.path}
                                            to={route.path}
                                            className={`flex  items-center ${showSidebar
                                                ? "px-2 w-full justify-start"
                                                : "w-[50px] ml-1 justify-center"
                                                } py-2 rounded-xl hover:bg-dark_selected_hover hover:text-black hover:dark:text-white ${isActiveParent || pathname === route.path
                                                    ? "bg-glb_blue text-white"
                                                    : ""
                                                }`}
                                        >
                                            {showSidebar ? (
                                                <>
                                                    {React.createElement(route.icon, {
                                                        className: "mr-2 ml-1",
                                                        size: 20,
                                                    })}
                                                    <h4
                                                    >
                                                        {route.name}
                                                    </h4>
                                                </>
                                            ) : (
                                                React.createElement(route.icon, { size: 20 })
                                            )}
                                        </Link>
                                    )}
                                </li>
                            );
                        }
                    )}
                </ul>

                <div className={`mt-24 ${showSidebar ? "px-2" : ""}`}>
                    <h3
                        className={`my-2 ${showSidebar ? "" : "text-center"
                            } text-gray-600`}
                    >
                        {showSidebar ? "System" : "S"}
                    </h3>

                    <ul className="dark:text-gray-400 text-gray-600">

                        <li className={`${showSidebar ? "px-2" : ""} py-2`}>
                            <Link
                                state={{ from: pathname }}
                                to="/settings"
                                className="flex w-full hover:text-black hover:dark:text-white"
                            >
                                <span
                                    className={`flex w-full gap-1 ${showSidebar
                                        ? ""
                                        : "items-center justify-center"
                                        }`}
                                >
                                    <Settings02Icon
                                        className={`${showSidebar ? "mr-2 ml-1" : ""
                                            }`}
                                        size="20"
                                    />
                                    {showSidebar && "Settings"}
                                </span>
                            </Link>
                        </li>
                        {/* <li className={`${showSidebar ? "px-2" : ""} py-2`}>
                            <Link
                                state={{ from: pathname }}
                                to="/referrals"
                                className="flex w-full hover:text-black hover:dark:text-white"
                            >
                                <span
                                    className={`flex w-full gap-1 ${showSidebar
                                        ? ""
                                        : "items-center justify-center"
                                        }`}
                                >
                                    <Share08Icon
                                        className={`${showSidebar ? "mr-2 ml-1" : ""
                                            }`}
                                        size="20"
                                    />
                                    {showSidebar && "Referrals"}
                                </span>
                            </Link>
                        </li>
                        <li className={`${showSidebar ? "px-2" : ""} py-2`}>
                            <Link
                                state={{ from: pathname }}
                                to="/help"
                                className="flex w-full hover:text-black hover:dark:text-white"
                            >
                                <span
                                    className={`flex w-full gap-1 ${showSidebar
                                        ? ""
                                        : "items-center justify-center"
                                        }`}
                                >
                                    <HelpCircleIcon
                                        className={`${showSidebar ? "mr-2 ml-1" : ""
                                            }`}
                                        size="20"
                                    />
                                    {showSidebar && "Help & FAQ"}
                                </span>
                            </Link>
                        </li> */}
                    </ul>
                </div>
            </div>
        </motion.div >
    );
}
