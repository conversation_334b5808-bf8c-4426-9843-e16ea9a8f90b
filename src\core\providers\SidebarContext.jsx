import {createContext, useState} from 'react';

export const SidebarContext = createContext(undefined);

export const SidebarProvider = ({children}) => {
    const [activeSubmenuId, setActiveSubmenuId] = useState(null);
    const [sidebarExpanded, setSidebarExpanded] = useState(false);

    return (
        <SidebarContext.Provider value={{
            activeSubmenuId,
            setActiveSubmenuId,
            sidebarExpanded,
            setSidebarExpanded
        }}>
            {children}
        </SidebarContext.Provider>
    );
};