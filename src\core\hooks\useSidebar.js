import { useState, useEffect } from 'react';

const BREAKPOINT = 1024;

export const useSidebarState = () => {
    const [showSidebar, setShowSidebar] = useState(true);
    const [isMobile, setIsMobile] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const [isLocked, setIsLocked] = useState(
        localStorage.getItem("sidebar-locked") === "true"
    );

    // Handle window resize
    useEffect(() => {
        const handleResize = () => {
            const isMobileView = window.innerWidth < BREAKPOINT;
            setIsMobile(isMobileView);

            // Reset states when switching between mobile and desktop
            if (isMobileView) {
                setShowSidebar(false);
                setIsLocked(false);
            } else {
                setShowSidebar(isLocked);
            }
        };

        handleResize();
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [isLocked]);

    // Persist locked state
    useEffect(() => {
        localStorage.setItem("sidebar-locked", isLocked);
    }, [isLocked]);

    // Handle hover state
    useEffect(() => {
        if (!isMobile && !isLocked) {
            setShowSidebar(isHovered);
        }
    }, [isHovered, isMobile, isLocked]);

    const toggleSidebar = () => {
        if (!isMobile) {
            // On desktop: toggle locked state
            setIsLocked(!isLocked);
            setShowSidebar(!isLocked);
        } else {
            // On mobile: just toggle visibility
            setShowSidebar(!showSidebar);
        }
    };

    const closeSidebar = () => {
        setShowSidebar(false);
        if (!isMobile) {
            setIsLocked(false);
        }
    };

    return {
        showSidebar,
        setShowSidebar,
        isLocked,
        setIsLocked,
        isMobile,
        isHovered,
        setIsHovered,
        toggleSidebar,
        closeSidebar
    };
};