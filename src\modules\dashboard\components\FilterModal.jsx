import {<PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader} from "@heroui/modal";
import {But<PERSON>} from "@heroui/button";
import {ArrowUp01Icon, ArrowUpDownIcon, Delete02Icon, FilterIcon} from "hugeicons-react";

export default function FilterModal({modalOpen, setModalOpen, id}) {
    return (
        <Modal
            backdrop="opaque"
            isOpen={modalOpen}
            onOpenChange={setModalOpen}
            id={id}
            radius="lg"
            size="xl"
            classNames={{
                body: "py-6",
                base: "dark:bg-[#0C0B0C]",
                header: "border-b border-gray-200 dark:border-gray-800",
                footer: "border-t border-gray-200 dark:border-gray-800",
                closeButton: "bg-gray-100 active:bg-white/10 dark:bg-zinc-900 mt-2 me-2",
            }}
        >
            <ModalContent>
                {(onClose) => (
                    <>
                        <ModalHeader className="flex flex-col gap-1">Filter Properties</ModalHeader>
                        <ModalBody className="min-h-[300px]">
                            <h4 className="flex gap-2 items-center text-gray-400 my-2">Sort By <ArrowUpDownIcon
                                size={18}/></h4>
                            <div className="space-y-6">
                                <div>
                                    <h3 className="font-medium justify-between mb-2 flex items-center">
                                        Price Range
                                        <ArrowUp01Icon className="w-5 h-5 ml-2"/>
                                    </h3>
                                    {/* Add price range input fields here */}
                                </div>
                                <div>
                                    <h3 className="font-medium justify-between mb-2 flex items-center">
                                        Statut
                                        <ArrowUp01Icon className="w-5 h-5 ml-2"/>
                                    </h3>
                                    {/* Add status input fields here */}
                                </div>
                                <div>
                                    <h3 className="font-medium justify-between mb-2 flex items-center">
                                        Due Date
                                        <ArrowUp01Icon className="w-5 h-5 ml-2"/>
                                    </h3>
                                    {/* Add due date input fields here */}
                                </div>
                            </div>
                        </ModalBody>
                        <ModalFooter
                            className="min-w-full flex flex-row justify-between items-center py-8">
                            <div className="flex flex-col text-sm">
                                <strong>3 Selected</strong>
                                <span className="text-gray-500">17 Total Filtered</span>
                            </div>
                            <div className="flex gap-2">
                                <Button className="bg-[#0258E8] text-white shadow-lg rounded-full" onPress={onClose}>
                                    <FilterIcon size={18} className="text-white"/>
                                    Apply Filter
                                </Button>
                                <Button className="bg-[#ED0006] text-white shadow-lg rounded-full" onPress={onClose}>
                                    <Delete02Icon size={18} className="text-white"/>
                                    Clear All
                                </Button>
                            </div>
                        </ModalFooter>
                    </>
                )}
            </ModalContent>
        </Modal>
    );
}